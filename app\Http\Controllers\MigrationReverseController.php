<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Settings\Models\PharmacySalesRegLineDetail;
use Modules\Diagnostic\Models\SampleHomecollectionCharge;
use Modules\Diagnostic\Models\SampleCollection;
use Modules\Diagnostic\Models\SampleCollectionBreakupBill;
use Modules\Diagnostic\Models\SampleCollectionBreakupTestItdose;
use Modules\Users\Services\UserService;
use Modules\Doctor\Services\DoctorService;
use Modules\Users\Services\ClusterUserService;
use Modules\Users\Services\CenterManagerService;
use Modules\Users\Services\ReceptionistService;
use Modules\Users\Services\NurseService;
use Modules\Users\Services\PhlebotomistService;
use Modules\Users\Services\PharmacistService;
use Modules\Agent\Services\AgentService;
use Modules\Users\Services\CustomerCareService;
use Modules\Patient\Services\PatientService;
use Modules\Billing\Services\BillingService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Reward\Services\RewardService;
use Modules\Appointment\Services\AppointmentService;
use Modules\Appointment\Services\VitalsService;
use Modules\Prescription\Services\PrescriptionService;
use Modules\Prescription\Services\PrescriptionChildService;
use Modules\Settings\Services\UploadCsquareService;
use Modules\Pharmacy\Services\PharmacyService;
use Modules\Pharmacy\Services\PharmacyChildService;
use Modules\Diagnostic\Services\DiagnosticService;
use Modules\Diagnostic\Services\DiagnosticBreakupTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupBillService;
use Modules\Diagnostic\Services\DiagnosticPhleboAssignmentService;
use Modules\Diagnostic\Services\DiagnosticApiService;
use Modules\Diagnostic\Services\DiagnosticBarcodeResubmissionService;
use Modules\Diagnostic\Services\EstimationService;
use App\Traits\DBConnection;
use Illuminate\Support\Arr;
use DB;
use Log;

class MigrationReverseController extends Controller
{
    use DBConnection;
    private $queryBuilder;
    private $userService;
    private $doctorService;
    private $clusterUserService;
    private $centerManagerService;
    private $receptionistService;
    private $nurseService;
    private $phlebotomistService;
    private $pharmacistService;
    private $agentService;
    private $customerCareService;
    private $patientService;
    private $billingService;
    private $paymentBillService;
    private $paymentService;
    private $paymentDetailService;
    private $rewardService;
    private $appointmentService;
    private $vitalsService;
    private $prescriptionService;
    private $prescriptionChildService;
    private $uploadCsquareService;
    private $pharmacyService;
    private $pharmacyChildService;
    private $diagnosticService;
    private $diagnosticBreakupTestService;
    private $diagnosticBreakupBillService;
    private $diagnosticPhleboAssignmentService;
    private $diagnosticApiService;
    private $diagnosticBarcodeResubmissionService;
    private $estimationService;

    public function __construct(
        UserService $userService, DoctorService $doctorService, ClusterUserService $clusterUserService, 
        CenterManagerService $centerManagerService, ReceptionistService $receptionistService, 
        NurseService $nurseService, PhlebotomistService $phlebotomistService, PharmacistService $pharmacistService, 
        AgentService $agentService, CustomerCareService $customerCareService, PatientService $patientService,
        BillingService $billingService, PaymentBillService $paymentBillService, PaymentService $paymentService,
        PaymentDetailService $paymentDetailService, RewardService $rewardService, AppointmentService $appointmentService,
        VitalsService $vitalsService, PrescriptionService $prescriptionService, PrescriptionChildService $prescriptionChildService,
        UploadCsquareService $uploadCsquareService, PharmacyService $pharmacyService, PharmacyChildService $pharmacyChildService,
        DiagnosticService $diagnosticService, DiagnosticBreakupTestService $diagnosticBreakupTestService,
        DiagnosticBreakupBillService $diagnosticBreakupBillService, DiagnosticPhleboAssignmentService $diagnosticPhleboAssignmentService,
        DiagnosticApiService $diagnosticApiService, DiagnosticBarcodeResubmissionService $diagnosticBarcodeResubmissionService,
        EstimationService $estimationService
    )
    {
        $this->setMymdTestDBConnection();
        $this->userService = $userService;
        $this->doctorService = $doctorService;
        $this->clusterUserService = $clusterUserService;
        $this->centerManagerService = $centerManagerService;
        $this->receptionistService = $receptionistService;
        $this->nurseService = $nurseService;
        $this->phlebotomistService = $phlebotomistService;
        $this->pharmacistService = $pharmacistService;    
        $this->agentService = $agentService;
        $this->customerCareService = $customerCareService;
        $this->patientService = $patientService;
        $this->billingService = $billingService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->rewardService = $rewardService;
        $this->appointmentService = $appointmentService;
        $this->vitalsService = $vitalsService;
        $this->prescriptionService = $prescriptionService;
        $this->prescriptionChildService = $prescriptionChildService;
        $this->uploadCsquareService = $uploadCsquareService;
        $this->pharmacyService = $pharmacyService;
        $this->pharmacyChildService = $pharmacyChildService;
        $this->diagnosticService = $diagnosticService;
        $this->diagnosticBreakupTestService = $diagnosticBreakupTestService;
        $this->diagnosticBreakupBillService = $diagnosticBreakupBillService;
        $this->diagnosticPhleboAssignmentService = $diagnosticPhleboAssignmentService;
        $this->diagnosticApiService = $diagnosticApiService;
        $this->diagnosticBarcodeResubmissionService = $diagnosticBarcodeResubmissionService;
        $this->estimationService = $estimationService;
    }
    // public function dataMigrateIndex()
    // {
    //     return view('admin.dataMigrate');
    // }
    public function dataMigrateDistrict()
    {
        $all_content = DB::table('districts')->count();
        // dd($all_content);
        $chunkSize = 10;
        $chunkCount = ceil($all_content / $chunkSize);

        for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            $this->queryBuilder = DB::table('districts')
                ->select('id', 'district_name')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);


            $this->queryBuilder->chunk($chunkSize, function ($chunk_contents) {

                // Initialize arrays for content and content tags
                $contentInsert = [];

                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'district_name' => $content->district_name,
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('district')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        // DB::table('temp_increment_migrations')
                        //     ->where('table_name', 'sample_types')
                        //     ->increment('count');
                    });
                    Log::info('Chunk reverse migrated successfully for district.', ['count' => count($contentInsert)]);
                }
            });
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        }
    }
    public function dataMigrateClinic()
    {
        $all_content = DB::table('clinics')->count();
        // dd($all_content);
        $chunkSize = 10;
        $chunkCount = ceil($all_content / $chunkSize);

        for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            $this->queryBuilder = DB::table('clinics')
                // ->select('clinic_id', 'clinic_name','clinic_slug','c_square_id','is_warehouse','is_new')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);


            $this->queryBuilder->chunk($chunkSize, function ($chunk_contents) {

                // Initialize arrays for content and content tags
                $contentInsert = [];

                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'clinic_id' => $content->id,
                        'panel_id' => $content->panel_id,
                        'cluster_id' => 0,
                        'clinic_name' => $content->clinic_name,
                        'clinic_slug' => $content->clinic_slug,
                        'c_square_id' => $content->c_square_id,
                        'is_warehouse' => $content->is_warehouse,
                        'is_new' => $content->is_new,
                        'clinic_address' => $content->clinic_address,
                        'clinic_phno' => $content->clinic_phno,
                        'clinic_email' => $content->clinic_email,
                        'latitude' => $content->latitude,
                        'longitude' => $content->longitude,
                        'map_embeded_src' => $content->map_embeded_src,
                        'photo' => $content->photo,
                        'district' => $content->district_id,
                        'ion_user_id' => 0,
                        // 'status' => $content->is_deleted == 1 ? 0 : 1,
                        // 'created_by' => 1,
                        // 'modified_by' => 1,
                        'is_deleted' => $content->deleted_by ? 1 : 0,
                        'pharmacy_phno' => $content->pharmacy_phno,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp,
                        // 'deleted_at' => $content->is_deleted == 1 ? $timestamp : null
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('clinic')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        // DB::table('temp_increment_migrations')
                        //     ->where('table_name', 'sample_types')
                        //     ->increment('count');
                    });
                    Log::info('Chunk reverse migrated successfully for clinic.', ['count' => count($contentInsert)]);
                }
            });
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        }
    }
    public function dataMigrateHoliday()
    {
        $all_content = DB::table('holidays')->count();
        // dd($all_content);
        $chunkSize = 10;
        $chunkCount = ceil($all_content / $chunkSize);

        for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            $this->queryBuilder = DB::table('holidays')
                // ->select('clinic_id', 'clinic_name','clinic_slug','c_square_id','is_warehouse','is_new')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);


            $this->queryBuilder->chunk($chunkSize, function ($chunk_contents) {

                // Initialize arrays for content and content tags
                $contentInsert = [];

                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'clinic_id' => $content->clinic_id,
                        'date' => $content->date,
                        'reason' => $content->reason,
                        // 'status' => 1,
                        'created_by' => $content->created_by,
                        // 'modified_by' => 1,
                        // 'deleted_by' => $content->is_deleted == 1 ? 1 : null,
                        'created_at' => $content->created_at,
                        // 'updated_at' => $content->created_at,
                        // 'deleted_at' => $content->is_deleted == 1 ? $timestamp : null
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('holidays')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        // DB::table('temp_increment_migrations')
                        //     ->where('table_name', 'sample_types')
                        //     ->increment('count');
                    });
                    Log::info('Chunk reverse migrated successfully for holidays.', ['count' => count($contentInsert)]);
                }
            });
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        }
    }
    public function dataMigrateSpeciality()
    {
        $all_content = DB::table('specialitys')->count();
        // dd($all_content);
        $chunkSize = 10;
        $chunkCount = ceil($all_content / $chunkSize);

        for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            $this->queryBuilder = DB::table('specialitys')
                // ->select('clinic_id', 'clinic_name','clinic_slug','c_square_id','is_warehouse','is_new')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);


            $this->queryBuilder->chunk($chunkSize, function ($chunk_contents) {

                // Initialize arrays for content and content tags
                $contentInsert = [];

                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s');
                    // $uploadedFile = [];
                    // $url = $content->speciality_image;
                    // if ($url && strpos(get_headers($url, 1)[0], '200') !== false) {
                    //     $file_data = file_get_contents($url);
                    //     $this->imageUploadService->setImageContent($file_data);
                    //     $file_name = 'speciality/'.basename($url);
                    //     $this->imageUploadService->setImagickSetFilename($file_name);
                    //     $uploadedFile = $this->imageUploadService->uploadImageContent();
                    //     // dd($uploadedFile['name']);
                    // }
                    // dd(strpos(get_headers($url, 1)[0], '200'));
                    $contentInsert[] = [
                        'speciality_id' => $content->id,
                        'speciality' => $content->speciality,
                        'speciality_slug' => $content->speciality_slug,
                        'speciality_image' => 0,
                        // 'status' => 1,
                        'added_by' => $content->created_by,
                        // 'modified_by' => 1,
                        'is_deleted' => $content->deleted_by ? 1 : 0,
                        'added_on' => $content->created_at,
                        // 'updated_at' => $content->added_on,
                        // 'deleted_at' => $content->is_deleted == 1 ? $timestamp : null
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('speciality')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        // DB::table('temp_increment_migrations')
                        //     ->where('table_name', 'sample_types')
                        //     ->increment('count');
                    });
                    Log::info('Chunk reverse migrated successfully for speciality.', ['count' => count($contentInsert)]);
                }
            });
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        }
    }
    public function dataMigrateTest()
    {
        $all_content = DB::table('tests')->count();
        // dd($all_content);
        $chunkSize = 10;
        $chunkCount = ceil($all_content / $chunkSize);

        for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            $this->queryBuilder = DB::table('tests')
                // ->select('clinic_id', 'clinic_name','clinic_slug','c_square_id','is_warehouse','is_new')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);


            $this->queryBuilder->chunk($chunkSize, function ($chunk_contents) {

                // Initialize arrays for content and content tags
                $contentInsert = [];

                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s');
                    // dd(strpos(get_headers($url, 1)[0], '200'));
                    $contentInsert[] = [
                        'id' => $content->id,
                        'itdose_testid' => $content->itdose_testid,
                        'test_name' => $content->test_name,
                        'test_price' => $content->test_price,
                        'test_code' => $content->test_code,
                        'sample_type_id' => $content->sample_type_id,
                        'sample_type' => $content->sample_type,
                        'delivery_date' => $content->delivery_date,
                        'department_id' => $content->department_id,
                        'sub_parameter_name' => $content->sub_parameter_name,
                        'method' => $content->method,
                        'test_information' => $content->test_information,
                        'sample_quantity' => $content->sample_quantity,
                        'package_test' => $content->package_test,
                        'is_package' => $content->is_package,   
                        'is_inhouse' => $content->is_inhouse,                     
                        // 'status' => 1,
                        // 'created_by' => 1,
                        'is_delete' => $content->deleted_by ? 0 : 1,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp,
                        // 'deleted_at' => $content->is_delete == 0 ? $timestamp : null
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('test')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        // DB::table('temp_increment_migrations')
                        //     ->where('table_name', 'sample_types')
                        //     ->increment('count');
                    });
                    Log::info('Chunk reverse migrated successfully for test.', ['count' => count($contentInsert)]);
                }
            });
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        }
    }
    public function dataMigratePrescriptionTemplate()
    {
        try {
            Log::info('Migration started for prescription_template.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name', 'prescription_templates')->select('reverse_count', 'limit')->first();
            $all_content = DB::table('prescription_templates')->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize; //$i * $chunkSize;
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('prescription_templates')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                // $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $medicine_details = DB::table('prescription_templates_childs')
                        ->select('type_id','name','frequency','days','instruction','notes')
                        ->where('template_id', $content->id)
                        ->where('type', 'M')
                        ->orderBy('id', 'asc')
                        ->get();
                    $medicine = [];
                    foreach ($medicine_details as $row) {
                        $each_medicine = [$row->name,$row->frequency,$row->days,$row->instruction,$row->notes];
                        $medicine[] = implode('***', $each_medicine);
                    }
                    $medicine = implode('***###', $medicine).'***###';
                    // dd($medicine);
                    $medicine_ids = DB::table('prescription_templates_childs')
                        ->where('template_id', $content->id)
                        ->where('type', 'M')
                        ->orderBy('id', 'asc')
                        ->pluck('type_id');
                    
                    $lab_test = DB::table('prescription_templates_childs')
                        ->where('template_id', $content->id)
                        ->where('type', 'T')
                        ->orderBy('id', 'asc')
                        ->pluck('type_id');
                   
                    $contentInsert[] = [
                        'id' => $content->id,
                        'template_name' => $content->template_name,
                        'speciality' => $content->speciality,
                        'visibility' => $content->visibility,
                        'medicine' => $medicine,
                        'medicine_ids' => json_encode($medicine_ids), //$medicine_ids,
                        'lab_test' => json_encode($lab_test), //$lab_test,
                        'advice' => $content->advice,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'updated_by' => $content->modified_by ? $content->modified_by : 0,
                        'created_at' => $content->created_at,
                        'updated_at' => $content->updated_at ? $content->updated_at : null
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('prescription_template')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'prescription_templates')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk reverse migrated successfully for prescription_template.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Reverse Migration completed successfully.');
            dd("Loop iteration completed for prescription_template table");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateMedicine()
    {
        try {
            Log::info('Migration started for medicine.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','medicines')->select('reverse_count','limit')->first();
            $all_content = DB::table('medicines')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('medicines')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $category = DB::table('medicine_categorys')->where('id', $content->category)->value('category');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'name' => $content->name,
                        'category' => $category,
                        'c_item_code' => $content->c_item_code,
                        'price' => $content->price,
                        'box' => $content->box,
                        's_price' => $content->s_price,
                        'quantity' => $content->quantity,
                        'generic' => $content->generic,
                        'company' => $content->company,
                        'effects' => $content->effects,
                        'e_date' => $content->e_date,
                        'add_date' => $content->add_date,
                        'c_cat_code' => $content->c_cat_code,
                        'c_item_category_head_code' => $content->c_item_category_head_code,
                        'item_category_class_code' => $content->item_category_class_code,
                        'c_mfac_code' => $content->c_mfac_code,
                        'c_group_code' => $content->c_group_code,
                        'c_cont_code' => $content->c_cont_code,
                        'c_pack_type_code' => $content->c_pack_type_code,
                        'hsn_sac_code' => $content->hsn_sac_code,//added_on
                        'packing' => $content->packing,
                        'medicine_composition' => $content->medicine_composition,
                        'status' => $content->status,
                        // 'created_by' => 1,
                        'added_on' => $content->created_at,
                        // 'updated_at' => $content->added_on
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('medicine')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'medicines')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk reverse migrated successfully for medicine.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Reverse Migration completed successfully.');
            dd("Loop iteration completed for medicine table");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    
    // public function dataMigrateDoctor()
    // {
    //     $all_content_users = DB::table('users')
    //         ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
    //         ->where('model_has_roles.role_id',2)
    //         ->where('users.active',1)
    //         ->count();
    //     // dd($all_content_users);
    //     $chunkSize_users = 10;
    //     $chunkCount_users = ceil($all_content_users / $chunkSize_users);

    //     for ($i = 0; $i <= $chunkCount_users; $i++) {
    //         $startId_users = $i * $chunkSize_users;
    //         $this->queryBuilder = DB::table('users')
    //             ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
    //             ->select('users.*','model_has_roles.role_id as role_id')
    //             ->where('model_has_roles.role_id',2)
    //             ->where('users.active',1)
    //             ->orderBy('users.id')
    //             ->offset($startId_users)
    //             ->limit($chunkSize_users);

    //         $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
    //             // Initialize arrays for content and content tags
    //             // dd($chunk_content_users);
    //             $contentInsert = [];
    //             $contentInsertRole = [];
    //             $contentInsertDoctor = [];
                
    //             foreach ($chunk_content_users as $content) {
    //                 // user
    //                 $user_created_at = strtotime($content->created_at);
    //                 $contentInsert[] = [
    //                     'id' => $content->id,
    //                     'ip_address' => $content->ip_address,
    //                     'username' => $content->username,
    //                     'email' => $content->email,
    //                     'phone' => $content->phone,
    //                     'password' => $content->password,
    //                     'last_login' => strtotime($content->last_login),
    //                     'active' => ($content->status == 1 ? 1 : 0),
    //                     'created_on' => $user_created_at,
    //                 ];
    //                 // user details
    //                 $doctor = DB::table('doctors')
    //                     ->where('user_id', $content->id)
    //                     ->first();
    //                 if (!empty($doctor)) {
    //                     // $name = explode(" ",str_replace("Dr. ", "", $doctor->name));
    //                     $doctor_type = match ($doctor->doctor_type) {
    //                         1 => 0,
    //                         2 => 1,
    //                         3 => 2,
    //                         default => 3,
    //                     };
                        
    //                     $contentInsertDoctor[] = [
    //                         'id' => $doctor->id,
    //                         'ion_user_id' => $doctor->user_id,
    //                         'itdose_doctorid' => $doctor->itdose_doctorid,
    //                         'name' => 'Dr. ' . $doctor->first_name . ' ' . $doctor->last_name,
    //                         'img_url' => $doctor->img_url,
    //                         'email' => $content->email,
    //                         'secret_key' => $doctor->secret_key,
    //                         'doctor_slug' => $doctor->doctor_slug,
    //                         'address' => $doctor->address,
    //                         'profile' => $doctor->profile,
    //                         'registration_no' => $doctor->registration_no,
    //                         'docor_mail_verification_frontend' => $doctor->docor_mail_verification_frontend,
    //                         // 'doctor_visit' => null,
    //                         'visit_price' => $doctor->visit_price,
    //                         'degree' => $doctor->degree,
    //                         'description' => $doctor->description,
    //                         'featured_doctor' => $doctor->featured_doctor,
    //                         'doctor_type' => $doctor_type,
    //                         'speciality' => $doctor->speciality,
    //                         'key_procedure_performed' => $doctor->key_procedure_performed,
    //                         'status' => $doctor->status,
    //                         'doctor_esign' => $doctor->doctor_esign,
    //                         'data_source' => $doctor->data_source,
    //                         'manage_bill' => $doctor->manage_bill,
    //                         // 'created_by' => 1,
    //                         'is_deleted' => ($doctor->deleted_by ? 1 : 0),
    //                         // 'created_at' => $user_created_at,
    //                         // 'updated_at' => $user_created_at,
    //                         // 'deleted_at' => ($doctor->is_deleted == 1 ? date('Y-m-d H:i:s') : null),
    //                     ];
    //                 }
    //                 // role
    //                 $contentInsertRole[] = [
    //                     'group_id' => 4,
    //                     'user_id' => $content->id
    //                 ];
    //             }
    //             // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
    //             if (!empty($contentInsert)) {
    //                 DB::transaction(function () use ($contentInsert) {
    //                     $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
    //                     $this->dbConnectionInstance->table('users')->upsert(
    //                         $contentInsert,
    //                         ['id'], // Unique columns to check for duplicates
    //                         $columnsToUpdate // Columns to update if a duplicate is found
    //                     );
    //                 });
    //                 Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
    //             }
    //             if (!empty($contentInsertRole)) {
    //                 DB::transaction(function () use ($contentInsertRole) {
    //                     $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
    //                     $this->dbConnectionInstance->table('users_groups')->upsert(
    //                         $contentInsertRole,
    //                         ['id'], // Unique columns to check for duplicates
    //                         $columnsToUpdate // Columns to update if a duplicate is found
    //                     );
    //                 });
    //                 Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
    //             }
    //             if (!empty($contentInsertDoctor)) {
    //                 DB::transaction(function () use ($contentInsertDoctor) {
    //                     $columnsToUpdate = array_diff(array_keys($contentInsertDoctor[0]), ['id']);
    //                     $this->dbConnectionInstance->table('doctor')->upsert(
    //                         $contentInsertDoctor,
    //                         ['id'], // Unique columns to check for duplicates
    //                         $columnsToUpdate // Columns to update if a duplicate is found
    //                     );
    //                 });
    //                 Log::info('Chunk reverse migrated successfully for doctor.', ['count' => count($contentInsertDoctor)]);
    //             }
                
    //         });
    //         // dd("Loop iteration completed for district table");
    //     }
    //     Log::info('Reverse Migration completed successfully.');
    //     dd("Loop iteration completed for doctor user table");
    //     // return back();
    // }
    public function dataMigrateClusterManager()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',3)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',3)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertClusterUser = [];
                // $contentInsertCenterManager = [];
                // $contentInsertReceptionist = [];
                // $contentInsertNurse = [];
                // $contentInsertPhlebotomist = [];
                // $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    
                    $laravel_role_id = 3;
                    $cluster_user = DB::table('cluster_users')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($cluster_user)) {                                
                        $contentInsertClusterUser[] = [
                            'id' => $cluster_user->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => $content->phone,
                            'ion_user_id' => $cluster_user->user_id,
                            'img_url' => $cluster_user->img_url,
                            'address' => $cluster_user->address,
                            'clinic_id' => $cluster_user->clinic_id,
                            'status' => $cluster_user->status,
                            'created_by' => $cluster_user->created_by,
                            'updated_by' => ($cluster_user->modified_by ? $cluster_user->modified_by : 0),
                            'created_at' => $cluster_user->created_at,
                            'updated_at' => $cluster_user->updated_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 26,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertClusterUser);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertClusterUser)) {
                    DB::transaction(function () use ($contentInsertClusterUser) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertClusterUser[0]), ['id']);
                        $this->dbConnectionInstance->table('cluster_user')->upsert(
                            $contentInsertClusterUser,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for cluster_user.', ['count' => count($contentInsertClusterUser)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateCenterManager()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',4)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',4)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertCenterManager = [];
                // $contentInsertReceptionist = [];
                // $contentInsertNurse = [];
                // $contentInsertPhlebotomist = [];
                // $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 4;
                    $center_manager =DB::table('center_managers')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($center_manager)) {                                
                        $contentInsertCenterManager[] = [
                            'id' => $center_manager->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => $content->phone,
                            'ion_user_id' => $center_manager->user_id,
                            'img_url' => $center_manager->img_url,
                            'clinic_id' => $center_manager->clinic_id,
                            'is_active' => $center_manager->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 27,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertCenterManager);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertCenterManager)) {
                    DB::transaction(function () use ($contentInsertCenterManager) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertCenterManager[0]), ['id']);
                        $this->dbConnectionInstance->table('center_manager')->upsert(
                            $contentInsertCenterManager,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for center_manager.', ['count' => count($contentInsertCenterManager)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateReceptionist()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',5)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',5)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertReceptionist = [];
                // $contentInsertNurse = [];
                // $contentInsertPhlebotomist = [];
                // $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 5;
                    $receptionist = DB::table('receptionists')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($receptionist)) {                                
                        $contentInsertReceptionist[] = [
                            'id' => $receptionist->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => $content->phone,
                            'ion_user_id' => $receptionist->user_id,
                            'img_url' => $receptionist->img_url,
                            'address' => $receptionist->address,
                            'clinic_id' => $receptionist->clinic_id,
                            'is_active' => $receptionist->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 10,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertReceptionist);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertReceptionist)) {
                    DB::transaction(function () use ($contentInsertReceptionist) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertReceptionist[0]), ['id']);
                        $this->dbConnectionInstance->table('receptionist')->upsert(
                            $contentInsertReceptionist,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for receptionist.', ['count' => count($contentInsertReceptionist)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateNurse()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',6)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',6)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertNurse = [];
                // $contentInsertPhlebotomist = [];
                // $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 6;
                    $nurse = DB::table('nurses')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($nurse)) {                                
                        $contentInsertNurse[] = [
                            'id' => $nurse->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => $content->phone,
                            'ion_user_id' => $nurse->user_id,
                            'img_url' => $nurse->img_url,
                            'address' => $nurse->address,
                            'clinic_id' => $nurse->clinic_id,
                            'is_active' => $nurse->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 6,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertNurse);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertNurse)) {
                    DB::transaction(function () use ($contentInsertNurse) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertNurse[0]), ['id']);
                        $this->dbConnectionInstance->table('nurse')->upsert(
                            $contentInsertNurse,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for nurse.', ['count' => count($contentInsertNurse)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    
    public function dataMigratePhlebotomist()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',7)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',7)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertPhlebotomist = [];
                // $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 7;
                    $phlebotomist = DB::table('phlebotomists')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($phlebotomist)) {                                
                        $contentInsertPhlebotomist[] = [
                            'id' => $phlebotomist->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => is_int($content->phone) ? $content->phone : intval(str_replace("-", "", str_replace(" ", "", $content->phone))),
                            'ion_user_id' => $phlebotomist->user_id,
                            'img_url' => $phlebotomist->img_url,
                            'gender' => $phlebotomist->gender,
                            'dob' => $phlebotomist->dob,
                            'city' => $phlebotomist->city,
                            'pincode' => $phlebotomist->pincode,
                            'address' => $phlebotomist->address,
                            'dl_no' => $phlebotomist->dl_no,
                            'vehicle_no' => $phlebotomist->vehicle_no,
                            'working_city' => $phlebotomist->working_city,
                            'working_city_pincode' => $phlebotomist->working_city_pincode,
                            'phlebotomist_slug' => $phlebotomist->phlebotomist_slug,
                            'clinic_id' => $phlebotomist->clinic_id,
                            'is_active' => $phlebotomist->status,
                            // 'created_by' => 1,
                            'is_deleted' => ($phlebotomist->deleted_by ? '1' : '0'),
                            'date_of_creation' => $phlebotomist->created_at,
                            // 'updated_at' => $phlebotomist->date_of_creation,
                            // 'deleted_at' => ($phlebotomist->is_deleted != 0 ? date('Y-m-d H:i:s') : null),
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 13,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertPhlebotomist);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertPhlebotomist)) {
                    DB::transaction(function () use ($contentInsertPhlebotomist) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertPhlebotomist[0]), ['id']);
                        $this->dbConnectionInstance->table('phlebotomist')->upsert(
                            $contentInsertPhlebotomist,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for phlebotomist.', ['count' => count($contentInsertPhlebotomist)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    
    public function dataMigratePharmacist()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',8)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',8)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertPharmacist = [];
                // $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 8;
                    $pharmacist = DB::table('pharmacists')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($pharmacist)) {                                
                        $contentInsertPharmacist[] = [
                            'id' => $pharmacist->id,
                            'name' => $content->username,
                            'email' => $content->email,
                            'phone' => is_int($content->phone) ? $content->phone : intval(str_replace("-", "", str_replace(" ", "", $content->phone))),
                            'ion_user_id' => $pharmacist->user_id,
                            'img_url' => $pharmacist->img_url,
                            'address' => $pharmacist->address,
                            'clinic_id' => $pharmacist->clinic_id,
                            'is_active' => $pharmacist->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 7,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertPhlebotomist);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertPharmacist)) {
                    DB::transaction(function () use ($contentInsertPharmacist) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertPharmacist[0]), ['id']);
                        $this->dbConnectionInstance->table('pharmacist')->upsert(
                            $contentInsertPharmacist,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for pharmacist.', ['count' => count($contentInsertPharmacist)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    
    public function dataMigrateAgent()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',9)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',9)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertAgent = [];
                // $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 9;
                    $agent = DB::table('agents')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($agent)) {
                        $contentInsertAgent[] = [
                            'id' => $agent->id,
                            'name' => $content->username,
                            'email' => $content->email ? $content->email : '',
                            'phone' => is_int($content->phone) ? $content->phone : intval(str_replace("-", "", str_replace(" ", "", $content->phone))),
                            'ion_user_id' => $agent->user_id,
                            'address' => $agent->address,
                            'image' => $agent->image,
                            'designation' => $agent->designation,
                            'team' => $agent->team,
                            'is_active' => $agent->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 22,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertAgent);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertAgent)) {
                    DB::transaction(function () use ($contentInsertAgent) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertAgent[0]), ['id']);
                        $this->dbConnectionInstance->table('agent')->upsert(
                            $contentInsertAgent,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for agent.', ['count' => count($contentInsertAgent)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateCustomerCare()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->where('model_has_roles.role_id',10)
            // ->where('users.active',1)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',10)
                // ->where('users.active',1)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertCare = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $laravel_role_id = 10;
                    $care = DB::table('customer_cares')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($care)) {                                
                        $contentInsertCare[] = [
                            'id' => $care->id,
                            'name' => $content->username,
                            'email' => $content->email ? $content->email : '',
                            'phone' => is_int($content->phone) ? $content->phone : intval(str_replace("-", "", str_replace(" ", "", $content->phone))),
                            'ion_user_id' => $care->user_id,
                            'img_url' => $care->img_url,
                            'is_active' => $care->status,
                            // 'created_by' => 1,
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 14,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertCare);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertCare)) {
                    DB::transaction(function () use ($contentInsertCare) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertCare[0]), ['id']);
                        $this->dbConnectionInstance->table('customer_care')->upsert(
                            $contentInsertCare,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for customer_care.', ['count' => count($contentInsertCare)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateMymdDoctor()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
            ->where('model_has_roles.role_id',2)
            ->where('users.active',1)
            ->whereIn('doctors.doctor_type',[1,2,3])
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',2)
                ->where('users.active',1)
                ->whereIn('doctors.doctor_type',[1,2,3])
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertDoctor = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = strtotime($content->created_at);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'last_login' => strtotime($content->last_login),
                        'active' => ($content->status == 1 ? 1 : 0),
                        'created_on' => $user_created_at,
                    ];
                    // user details
                    $doctor = DB::table('doctors')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($doctor)) {
                        // $name = explode(" ",str_replace("Dr. ", "", $doctor->name));
                        $doctor_type = match ($doctor->doctor_type) {
                            1 => 3,
                            2 => 1,
                            3 => 2,
                            4 => 4,
                            default => null,
                        };
                        $contentInsertDoctor[] = [
                            'id' => $doctor->id,
                            'ion_user_id' => $doctor->user_id,
                            'itdose_doctorid' => $doctor->itdose_doctorid,
                            'name' => 'Dr. ' . $doctor->first_name . ($doctor->last_name ? ' ' . $doctor->last_name : ''),
                            'img_url' => $doctor->img_url,
                            'email' => $content->email,
                            'secret_key' => $doctor->secret_key,
                            'doctor_slug' => $doctor->doctor_slug,
                            'address' => $doctor->address,
                            'profile' => $doctor->profile,
                            'registration_no' => $doctor->registration_no,
                            'docor_mail_verification_frontend' => $doctor->docor_mail_verification_frontend,
                            // 'doctor_visit' => null,
                            'visit_price' => $doctor->visit_price,
                            'degree' => $doctor->degree,
                            'description' => $doctor->description,
                            'featured_doctor' => $doctor->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $doctor->speciality,
                            'key_procedure_performed' => $doctor->key_procedure_performed,
                            'status' => $doctor->status,
                            'doctor_esign' => $doctor->doctor_esign,
                            'data_source' => $doctor->data_source,
                            'manage_bill' => $doctor->manage_bill,
                            // 'created_by' => 1,
                            'is_deleted' => ($doctor->deleted_by ? 1 : 0),
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                            // 'deleted_at' => ($doctor->is_deleted == 1 ? date('Y-m-d H:i:s') : null),
                        ];
                    }
                    // role
                    $contentInsertRole[] = [
                        'group_id' => 4,
                        'user_id' => $content->id
                    ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('users')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                        $this->dbConnectionInstance->table('users_groups')->upsert(
                            $contentInsertRole,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                }
                if (!empty($contentInsertDoctor)) {
                    DB::transaction(function () use ($contentInsertDoctor) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertDoctor[0]), ['id']);
                        $this->dbConnectionInstance->table('doctor')->upsert(
                            $contentInsertDoctor,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for doctor.', ['count' => count($contentInsertDoctor)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    public function dataMigrateRefferDoctor()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
            ->where('model_has_roles.role_id',2)
            ->where('users.active',2)
            ->where('doctors.doctor_type',4)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',2)
                ->where('users.active',2)
                ->where('doctors.doctor_type',4)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                // $contentInsert = [];
                // $contentInsertRole = [];
                $contentInsertDoctor = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    // $user_created_at = strtotime($content->created_at);
                    // $contentInsert[] = [
                    //     'id' => $content->id,
                    //     'ip_address' => $content->ip_address,
                    //     'username' => $content->username,
                    //     'email' => $content->email,
                    //     'phone' => $content->phone,
                    //     'password' => $content->password,
                    //     'last_login' => strtotime($content->last_login),
                    //     'active' => ($content->status == 1 ? 1 : 0),
                    //     'created_on' => $user_created_at,
                    // ];
                    // user details
                    $doctor = DB::table('doctors')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($doctor)) {
                        // $name = explode(" ",str_replace("Dr. ", "", $doctor->name));
                        $doctor_type = match ($doctor->doctor_type) {
                            1 => 3,
                            2 => 1,
                            3 => 2,
                            4 => 4,
                            default => null,
                        };
                        $contentInsertDoctor[] = [
                            'id' => $doctor->id,
                            'ion_user_id' => null,
                            'itdose_doctorid' => $doctor->itdose_doctorid,
                            'name' => 'Dr. ' . $doctor->first_name . ($doctor->last_name ? ' ' . $doctor->last_name : ''),
                            'img_url' => $doctor->img_url,
                            'email' => $content->email,
                            'secret_key' => $doctor->secret_key,
                            'doctor_slug' => $doctor->doctor_slug,
                            'address' => $doctor->address,
                            'profile' => $doctor->profile,
                            'registration_no' => $doctor->registration_no,
                            'docor_mail_verification_frontend' => $doctor->docor_mail_verification_frontend,
                            // 'doctor_visit' => null,
                            'visit_price' => $doctor->visit_price,
                            'degree' => $doctor->degree,
                            'description' => $doctor->description,
                            'featured_doctor' => $doctor->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $doctor->speciality,
                            'key_procedure_performed' => $doctor->key_procedure_performed,
                            'status' => $doctor->status,
                            'doctor_esign' => $doctor->doctor_esign,
                            'data_source' => $doctor->data_source,
                            'manage_bill' => $doctor->manage_bill,
                            // 'created_by' => 1,
                            'is_deleted' => ($doctor->deleted_by ? 1 : 0),
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                            // 'deleted_at' => ($doctor->is_deleted == 1 ? date('Y-m-d H:i:s') : null),
                        ];
                    }
                    // role
                    // $contentInsertRole[] = [
                    //     'group_id' => 4,
                    //     'user_id' => $content->id
                    // ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                // if (!empty($contentInsert)) {
                //     DB::transaction(function () use ($contentInsert) {
                //         $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                //         $this->dbConnectionInstance->table('users')->upsert(
                //             $contentInsert,
                //             ['id'], // Unique columns to check for duplicates
                //             $columnsToUpdate // Columns to update if a duplicate is found
                //         );
                //     });
                //     Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                // }
                // if (!empty($contentInsertRole)) {
                //     DB::transaction(function () use ($contentInsertRole) {
                //         $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                //         $this->dbConnectionInstance->table('users_groups')->upsert(
                //             $contentInsertRole,
                //             ['id'], // Unique columns to check for duplicates
                //             $columnsToUpdate // Columns to update if a duplicate is found
                //         );
                //     });
                //     Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                // }
                if (!empty($contentInsertDoctor)) {
                    DB::transaction(function () use ($contentInsertDoctor) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertDoctor[0]), ['id']);
                        $this->dbConnectionInstance->table('doctor')->upsert(
                            $contentInsertDoctor,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for doctor.', ['count' => count($contentInsertDoctor)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    
    public function dataMigrateOtherDoctor()
    {
        $all_content_users = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
            ->where('model_has_roles.role_id',2)
            ->where('users.active',2)
            ->where('doctors.data_source',3)
            ->count();            
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = DB::table('users')
                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                ->leftJoin('doctors', 'users.id', '=', 'doctors.user_id')
                ->select('users.*','model_has_roles.role_id as role_id')
                ->where('model_has_roles.role_id',2)
                ->where('users.active',2)
                ->where('doctors.data_source',3)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                // $contentInsert = [];
                // $contentInsertRole = [];
                $contentInsertDoctor = [];
                
                foreach ($chunk_content_users as $content) {
                    // user
                    // $user_created_at = strtotime($content->created_at);
                    // $contentInsert[] = [
                    //     'id' => $content->id,
                    //     'ip_address' => $content->ip_address,
                    //     'username' => $content->username,
                    //     'email' => $content->email,
                    //     'phone' => $content->phone,
                    //     'password' => $content->password,
                    //     'last_login' => strtotime($content->last_login),
                    //     'active' => ($content->status == 1 ? 1 : 0),
                    //     'created_on' => $user_created_at,
                    // ];
                    // user details
                    $doctor = DB::table('doctors')
                        ->where('user_id', $content->id)
                        ->first();
                    if (!empty($doctor)) {
                        // $name = explode(" ",str_replace("Dr. ", "", $doctor->name));
                        $doctor_type = match ($doctor->doctor_type) {
                            1 => 3,
                            2 => 1,
                            3 => 2,
                            4 => 4,
                            default => null,
                        };
                        $contentInsertDoctor[] = [
                            'id' => $doctor->id,
                            'ion_user_id' => null,
                            'itdose_doctorid' => $doctor->itdose_doctorid,
                            'name' => 'Dr. ' . $doctor->first_name . ($doctor->last_name ? ' ' . $doctor->last_name : ''),
                            'img_url' => $doctor->img_url,
                            'email' => $content->email,
                            'secret_key' => $doctor->secret_key,
                            'doctor_slug' => $doctor->doctor_slug,
                            'address' => $doctor->address,
                            'profile' => $doctor->profile,
                            'registration_no' => $doctor->registration_no,
                            'docor_mail_verification_frontend' => $doctor->docor_mail_verification_frontend,
                            // 'doctor_visit' => null,
                            'visit_price' => $doctor->visit_price,
                            'degree' => $doctor->degree,
                            'description' => $doctor->description,
                            'featured_doctor' => $doctor->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $doctor->speciality,
                            'key_procedure_performed' => $doctor->key_procedure_performed,
                            'status' => 2,
                            'doctor_esign' => $doctor->doctor_esign,
                            'data_source' => null,
                            'manage_bill' => $doctor->manage_bill,
                            // 'created_by' => 1,
                            'is_deleted' => ($doctor->deleted_by ? 1 : 0),
                            // 'created_at' => $user_created_at,
                            // 'updated_at' => $user_created_at,
                            // 'deleted_at' => ($doctor->is_deleted == 1 ? date('Y-m-d H:i:s') : null),
                        ];
                    }
                    // role
                    // $contentInsertRole[] = [
                    //     'group_id' => 4,
                    //     'user_id' => $content->id
                    // ];
                }
                // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                // if (!empty($contentInsert)) {
                //     DB::transaction(function () use ($contentInsert) {
                //         $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                //         $this->dbConnectionInstance->table('users')->upsert(
                //             $contentInsert,
                //             ['id'], // Unique columns to check for duplicates
                //             $columnsToUpdate // Columns to update if a duplicate is found
                //         );
                //     });
                //     Log::info('Chunk reverse migrated successfully for users.', ['count' => count($contentInsert)]);
                // }
                // if (!empty($contentInsertRole)) {
                //     DB::transaction(function () use ($contentInsertRole) {
                //         $columnsToUpdate = array_diff(array_keys($contentInsertRole[0]), ['id']);
                //         $this->dbConnectionInstance->table('users_groups')->upsert(
                //             $contentInsertRole,
                //             ['id'], // Unique columns to check for duplicates
                //             $columnsToUpdate // Columns to update if a duplicate is found
                //         );
                //     });
                //     Log::info('Chunk reverse migrated successfully for users_groups.', ['count' => count($contentInsertRole)]);
                // }
                if (!empty($contentInsertDoctor)) {
                    DB::transaction(function () use ($contentInsertDoctor) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertDoctor[0]), ['id']);
                        $this->dbConnectionInstance->table('doctor')->upsert(
                            $contentInsertDoctor,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk reverse migrated successfully for doctor.', ['count' => count($contentInsertDoctor)]);
                }
                
            });
            // dd("Loop iteration completed for district table");
        }
        Log::info('Reverse Migration completed successfully.');
        dd("completed migration");
        // return back();
    }
    // public function dataMigrateOtherDoctor()
    // {
    //     $all_content_users = $this->dbConnectionInstance->table('doctor')
    //         ->where('status', 2)
    //         ->count();
    //     dd($all_content_users);
    //     $chunkSize_users = 10;
    //     $chunkCount_users = ceil($all_content_users / $chunkSize_users);

    //     for ($i = 0; $i <= $chunkCount_users; $i++) {
    //         $startId_users = $i * $chunkSize_users;
    //         $this->queryBuilder = $this->dbConnectionInstance->table('doctor')
    //             ->where('status', 2)
    //             ->orderBy('id')
    //             ->offset($startId_users)
    //             ->limit($chunkSize_users);

    //         $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
    //             // Initialize arrays for content and content tags
    //             // dd($chunk_content_users);
    //             $contentInsert = [];
    //             $contentInsertRole = [];
    //             $contentInsertDoctor = [];
    //             $max_user_id = DB::table('users')->max('id');
    //             foreach ($chunk_content_users as $key => $content) {
    //                 $user_id = DB::table('users')->where('username', $content->name)->value('id');
    //                 if(!$user_id){
    //                     $max_user_id++;
    //                     $user_id = $max_user_id;
    //                 }
    //                 // dd($user_id);
    //                 // user
    //                 $user_created_at = date('Y-m-d H:i:s');
    //                 $contentInsert[] = [
    //                     'id' => $user_id,
    //                     'username' => $content->name,
    //                     'active' => 2,
    //                     'status' => 0,
    //                     'created_by' => 1,
    //                     'created_at' => $user_created_at,
    //                 ];
                    
    //                 $name = explode(" ",str_replace("Dr. ", "", $content->name));
    //                 $doctor_type = match ($content->doctor_type) {
    //                     0 => 1,
    //                     1 => 2,
    //                     2 => 3,
    //                     default => 4,
    //                 };
                    
    //                 $contentInsertDoctor[] = [
    //                     'id' => $content->id,
    //                     'user_id' => $user_id,
    //                     'itdose_doctorid' => $content->itdose_doctorid,
    //                     'first_name' => $name[0] ?? '',
    //                     'last_name' => $name[1] ?? '',
    //                     'doctor_slug' => $content->doctor_slug,
    //                     'status' => 0,
    //                     'data_source' => 3,
    //                     'manage_bill' => $content->manage_bill,
    //                     'created_by' => 1,
    //                     'created_at' => $user_created_at,
    //                     'updated_at' => $user_created_at,
    //                 ];
    //                 // role
    //                 $contentInsertRole[] = [
    //                     'role_id' => 2,
    //                     'model_type' => 'Modules\Users\Models\User',
    //                     'model_id' => $user_id
    //                 ];
    //             }
    //             // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
    //             if (!empty($contentInsert)) {
    //                 $this->userService->dataMigrate($contentInsert);
    //             }
    //             if (!empty($contentInsertRole)) {
    //                 DB::transaction(function () use ($contentInsertRole) {
    //                     DB::table('model_has_roles')->upsert(
    //                         $contentInsertRole,
    //                         ['model_id'], // Unique columns to check for duplicates
    //                         ['role_id'] // Columns to update if a duplicate is found
    //                     );
    //                 });
    //             }
    //             if (!empty($contentInsertDoctor)) {
    //                 $this->doctorService->dataMigrate($contentInsertDoctor);
    //             }
    //             // dd('each loop completed');
    //         });
    //         // dd("Loop iteration completed for district table");
    //     }
    //     dd("Loop iteration completed");
    //     // return back();
    // }
    public function dataMigratePatient()
    {
        try {
            Log::info('Migration started for patient.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','patient')->select('reverse_count','limit')->first();
            $all_content = DB::table('patients')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = DB::table('patients')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'id' => $content->id,
                        'img_url' => $content->img_url,
                        'name' => $content->name,
                        'email' => $content->email,
                        'doctor' => $content->doctor_id,
                        'address' => $content->address,
                        'phone' => $content->phone,
                        'sex' => $content->sex,
                        'birthdate' => $content->birthdate,
                        // 'age' => $age,
                        // 'month' => $content->month,
                        // 'days' => $content->days,
                        'pincode' => $content->pincode,
                        // 'language' => $content->language,
                        'bloodgroup' => $content->bloodgroup,
                        'ion_user_id' => 0,
                        'add_date' => date('d/m/y',strtotime($content->created_at)),
                        'parent_id' => $content->parent_id ? $content->parent_id : 0,
                        'uhid_no' => $content->uhid_no,
                        'is_employee' => $content->is_employee,
                        // 'status' => 1,
                        // 'created_by' => 1,
                        // 'created_at' => $created_at,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('patient')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'patient')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for patient.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    // {
    //     $increment_migration = DB::table('temp_increment_migrations')->where('table_name','patient')->select('count','limit')->first();
    //     $startId = $increment_migration->limit*$increment_migration->count;
    //     $all_content = $this->dbConnectionInstance->table('patient')
    //         ->selectRaw('phone,count(*) as total')
    //         ->whereNotNull('phone')
    //         ->groupBy('phone')
    //         ->offset($startId)
    //         ->limit($increment_migration->limit)
    //         ->pluck('total','phone')->toArray();
    //     // dd($all_content);
    //     if (count($all_content) == 0) {
    //         dd("completed migration");
    //     }
    //     foreach ($all_content as $phone => $no_patient) {
    //         $chunkSize = $no_patient;
    //         $family_id = 'FI'.substr($phone, -4).random_int(1000, 9999);
    //         $this->queryBuilder = $this->dbConnectionInstance->table('patient')
    //             ->where('phone',$phone)
    //             ->orderBy('phone')
    //             ->orderBy('id');
    //             // ->offset($startId)
    //             // ->limit($chunkSize);
    //         // dd($this->queryBuilder->get());
    //         $this->queryBuilder->chunk($chunkSize, function ($chunk_content) use ($family_id) {
    //             // Initialize arrays for content and content tags
    //             $contentInsert = [];
    //             $parent_id;
    //             foreach ($chunk_content as $key =>$content) {
    //                 $language_id = match ($content->preferred_language) {
    //                     1 => 2,
    //                     2 => 1,
    //                     3 => 3,
    //                     default => 2,
    //                 };
    //                 $add_date = date('Y-m-d',strtotime('03/11/25'));
    //                 if ($content->birthdate) {
    //                     $birthdate = $content->birthdate;
    //                 }
    //                 else {
    //                     $age = $content->age ?? 0;
    //                     $add_date_cal = $add_date ?? date('Y-m-d');
    //                     $birthdate = $this->calculateDOB($age,$add_date_cal);
    //                 }
                    
    //                 $created_at = ($add_date ? date('Y-m-d H:i:s', strtotime($add_date)) : date('Y-m-d H:i:s'));
    //                 // parent patient
    //                 if ($key == 0) {
    //                     $parent_id = $content->id;
    //                     $contentInsert[] = [
    //                         'id' => $content->id,
    //                         'family_id' => $family_id,
    //                         'language_id' => $language_id,
    //                         'relationship_id' => null,
    //                         'img_url' => $content->img_url,
    //                         'name' => $content->name,
    //                         'email' => $content->email,
    //                         'doctor_id' => $content->doctor,
    //                         'address' => $content->address,
    //                         'phone' => $content->phone,
    //                         'sex' => $content->sex,
    //                         'birthdate' => $birthdate,
    //                         'pincode' => $content->pincode,
    //                         'bloodgroup' => $content->bloodgroup,
    //                         'parent_id' => null,
    //                         'uhid_no' => $content->uhid_no,
    //                         'is_employee' => $content->is_employee,
    //                         'status' => 1,
    //                         'created_by' => 1,
    //                         'created_at' => $created_at,
    //                     ];
    //                 }
    //                 else {// member patient
    //                     $contentInsert[] = [
    //                         'id' => $content->id,
    //                         'family_id' => $family_id,
    //                         'language_id' => $language_id,
    //                         'relationship_id' => 8,
    //                         'img_url' => $content->img_url,
    //                         'name' => $content->name,
    //                         'email' => $content->email,
    //                         'doctor_id' => $content->doctor,
    //                         'address' => $content->address,
    //                         'phone' => null,
    //                         'sex' => $content->sex,
    //                         'birthdate' => $birthdate,
    //                         'pincode' => $content->pincode,
    //                         'bloodgroup' => $content->bloodgroup,
    //                         'parent_id' => $parent_id,
    //                         'uhid_no' => $content->uhid_no,
    //                         'is_employee' => $content->is_employee,
    //                         'status' => 1,
    //                         'created_by' => 1,
    //                         'created_at' => $created_at,
    //                     ];
    //                 }
    //             }
    //             // dd($contentInsert);
    //             if (!empty($contentInsert)) {
    //                 $this->patientService->dataMigrate($contentInsert);
    //             }
    //             // dd('each loop completed');
    //         });
    //         // dd("Loop iteration completed for district table");
    //     }
    //     DB::table('temp_increment_migrations')->where('table_name','patient')->update([
    //         'count' => $increment_migration->count+1,
    //     ]);
    //     dd("Loop iteration completed");
    //     // return back();
    // }
    // private function calculateDOB($age,$add_date) {
    //     $age = intval($age);
    //     if (!is_int($age)) {
    //         $age = 0;
    //     }
    //     $diff = floor((strtotime(date('Y-m-d')) - strtotime($add_date)) / (60 * 60 * 24 * 365));
    //     $age = $age + $diff;
    //     $birthdate = date('Y-01-01', strtotime("-$age years"));
    //     return $birthdate;
    // }
    public function dataMigrateMembership()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_registrations')->select('reverse_count','limit')->first();

        $all_content = DB::table('membership_registrations')
            ->count();
        // dd($all_content);
        
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        if ($increment_migration->reverse_count * $chunkSize >= $all_content) {
            dd("completed migration");
        }
        // dd($chunkSize,$increment_migration->count);
        for ($i = $increment_migration->reverse_count; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            
            // dd($startId);
            $this->queryBuilder = DB::table('membership_registrations')
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize);
            // dd($this->queryBuilder->get());
            // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
                // Initialize arrays for content and content tags
                $contentInsert = [];
                $contentInsertBilling = [];
                foreach ($this->queryBuilder->get() as $content) {
                    // dd($content);
                    $card_type = DB::table('memberships')->where('id', $content->card_type)->value('name');
                    $contentInsert[] = [
                        'record_id' => $content->id,
                        'patient_id' => $content->patient_id,
                        // 'phone' => $phone ?? null,
                        // 'clinic_id' => $clinic_id ?? null,
                        'start_date' => $content->start_date,
                        'end_date' => $content->end_date,
                        'registration_no' => $content->registration_no,
                        // 'category_id' => $category_id,
                        'card_type' => $card_type,
                        'registration_date' => $content->registration_date,
                        'smart_card' => $content->smart_card,
                        'data_source' => $content->data_source,
                        'source' => $content->source ? $content->source : 0,
                        'pincode' => $content->pincode,
                        'remarks' => $content->remarks,
                        'is_renewal' => $content->is_renewal,
                        'is_complementory_availed' => $content->is_complementory_availed,
                        'complementory_diagnostic_test' => $content->complementory_diagnostic_test,
                        'complementory_diagnostic_no_of_occurrence' => $content->complementory_diagnostic_no_of_occurrence,
                        'homecollection_no_of_occurrence' => $content->homecollection_no_of_occurrence,
                        'status' => $content->status,
                        'mem_created_by' => $content->created_by,
                        'mem_created_at' => date('Y-m-d H:i', strtotime($content->created_at)),
                    ];
                    $payment_bill_id = DB::table('payment_bill_master')->where(['type' => 'MB','service_id' => $content->id])->value('id');
                    $payment = DB::table('payments')->where('bill_id', $payment_bill_id)->first();
                    // $payment_ids = $payment->payments->pluck('id')->toArray();
                    $payment_details = DB::table('payment_details')->where('payment_id', $payment->id)->select('amount','payment_mode','payment_details')->get();
                    // dd($payment_details);
                    if(!empty($payment_details)) {
                        foreach ($payment_details as $dtl) {
                            $contentInsertBilling[] = [
                                'patient_id' => $content->patient_id,
                                'mode_of_payment' => $dtl->payment_mode,
                                'payment_amount' => $dtl->amount,
                                'payment_details' => $dtl->payment_details ? $dtl->payment_details : '',
                                'registration_id' => $content->id,
                                'unique_bill_id' => '',
                                'clinic_id' => $content->clinic_id ? $content->clinic_id : 0,
                                'created_by' => $content->created_by,
                                'created_at' => date('Y-m-d H:i:s', strtotime($content->created_at)),
                                'status' => 0,
                                'credit_debit' => 1,
                                'openning_points' => 0,
                                'closing_points' => 0,
                            ];
                        }
                    }
                    // dd($payment);
                    if($payment->redeem_points) {
                        $contentInsertBilling[] = [
                            'patient_id' => $content->patient_id,
                            'mode_of_payment' => 'reward_points',
                            'payment_amount' => $payment->redeem_points,
                            'payment_details' => 'reward_points',
                            'registration_id' => $content->id,
                            'unique_bill_id' => '',
                            'clinic_id' => $content->clinic_id,
                            'created_by' => $content->created_by,
                            'created_at' => date('Y-m-d H:i:s', strtotime($content->created_at)),
                            'status' => 0,
                            'credit_debit' => 1,
                            'openning_points' => $payment->openning_points,
                            'closing_points' => $payment->closing_points,
                        ];
                    }
                    // dd($contentInsert,$contentInsertBilling);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('arogya_bandhu_registrations')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                }
                if (!empty($contentInsertBilling)) {
                    foreach ($contentInsertBilling as $key => $value) {
                        $check_exist = $this->dbConnectionInstance->table('arogya_bandhu_billing')->where(['registration_id' => $value['registration_id'],'mode_of_payment' => $value['mode_of_payment']])->count();
                        if ($check_exist > 0) {
                            $this->dbConnectionInstance->table('arogya_bandhu_billing')->where(['registration_id' => $value['registration_id'],'mode_of_payment' => $value['mode_of_payment']])
                            ->update($value);
                        }
                        else {
                            $this->dbConnectionInstance->table('arogya_bandhu_billing')->insert($value);
                        }
                    }
                }
                DB::table('temp_increment_migrations')
                    ->where('table_name', 'arogya_bandhu_registrations')
                    ->increment('reverse_count');
                // dd($contentInsert,$contentInsertBilling);
                // dd($contentInsert);
                // if (!empty($contentInsert)) {
                //     $this->billingService->dataMigrate($contentInsert);
                // }
                // dd('each loop completed');
            // });
            
        }
        // DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_registrations')->update([
        //     'reverse_count' => $increment_migration->reverse_count+1,
        // ]);
        dd("Loop iteration completed");
        // return back();
    }
    
    public function dataMigrateMembershipCard()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_smart_cards')->select('reverse_count','limit')->first();

        $all_content = DB::table('membership_registrations')
            ->where('smart_card',2)
            ->count();
        // dd($all_content);
        if ($increment_migration->reverse_count * $increment_migration->limit >= $all_content) {
            dd("completed migration");
        }
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        // dd($chunkSize,$increment_migration->count);
        for ($i = $increment_migration->reverse_count; $i <= $chunkCount; $i++) {
            $startId = $i * $chunkSize;
            
            // dd($startId);
            $this->queryBuilder = DB::table('membership_registrations')
                ->where('smart_card',2)
                // ->where('record_id',14418)
                ->orderBy('id')
                ->offset($startId)
                ->limit($chunkSize)
                ->get();
            // dd($this->queryBuilder->get());
            // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
                // Initialize arrays for content and content tags
                // $contentInsert = [];
                foreach ($this->queryBuilder as $content) {
                    // dd($content);
                    $card_type = DB::table('memberships')->where('id', $content->card_type)->value('name');
                    $payment_bill = DB::table('payment_bill_master')->where(['type' => 'MB-CARD','service_id' => $content->id])->first();
                    $payment = DB::table('payments')->where('bill_id', $payment_bill->id)->first();
                    // $payment_ids = $payment->payments->pluck('id')->toArray();
                    $payment_dtl = DB::table('payment_details')->where('payment_id', $payment->id)->select('amount','payment_mode','payment_details')->get();
                    // dd($payment_dtl);
                    $payment_mode = [];
                    $breakup_payment_amount = [];
                    $payment_details = [];
                    if(!empty($payment_dtl)) {
                        foreach ($payment_dtl as $dtl) {
                            array_push($payment_mode, $dtl->payment_mode);
                            array_push($breakup_payment_amount, $dtl->amount);
                            array_push($payment_details, $dtl->payment_details);
                        }
                    }
                    // dd(implode(',', $payment_mode),$breakup_payment_amount,$payment_details);
                    $contentInsertBilling = [
                        'date' => date('Y-m-d', strtotime($payment_bill->created_at ?? '')),
                        'amount' => $payment_bill->bill_amount,
                        'gross_total' => $payment_bill->total_amount,
                        'remarks' => $payment->remarks ?? '',
                        'category_name' => 'absmartcard',
                        'status' => $payment_bill->status == 1 ? 'Paid' : 'unpaid',
                        'payment_mode' => implode(',', $payment_mode),
                        'breakup_payment_amount' => implode(',', $breakup_payment_amount),
                        'payment_details' => implode(',', $payment_details),
                        'absmartcard_membershipid' => $content->id,
                        'arrogya_membership_no' => $card_type,
                        'created_by' => $payment_bill->created_by ?? '',
                        'created_at' => date('Y-m-d H:i:s', strtotime($payment_bill->created_at ?? '')),
                        'redeem_points' => $payment->redeem_points,
                        'openning_points' => $payment->openning_points,
                        'closing_points' => $payment->closing_points,
                        'breakuptotalamount' => '',
                        'appointment_id' => 0,
                        'sample_collection_id' => 0,
                        'receipt_no' => '',
                        'arrogya_active' => 0,
                        'offered_type' => 0,
                        'membership_discount' => '',
                        'campaign_discount' => 0,
                        'home_collection_charges' => 0,
                        'employee_discount' => 0
                    ];
                    
                    $check_exist = $this->dbConnectionInstance->table('payment')->where(['absmartcard_membershipid' => $contentInsertBilling['absmartcard_membershipid']])->count();
                    if ($check_exist > 0) {
                        $this->dbConnectionInstance->table('payment')->where(['absmartcard_membershipid' => $contentInsertBilling['absmartcard_membershipid']])
                        ->update($contentInsertBilling);
                    }
                    else {
                        $this->dbConnectionInstance->table('payment')->insert($contentInsertBilling);
                    }
                }
                if(count($this->queryBuilder) > 0) {
                    DB::table('temp_increment_migrations')
                        ->where('table_name', 'arogya_bandhu_smart_cards')
                        ->increment('reverse_count');
                }
            // });
            
        }
        dd("Loop iteration completed");
        // return back();
    }
    // private function paymentCardWithReward($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    // {
    //     // dd($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone);
    //     $data = [
    //         'bill_show_id' => $bill_id,
    //         'type' => $pay_type,//ServiceCategory->membership
    //         'service_id' => $service_id,
    //         'patient_id' => $patient_id,
    //         'bill_amount' => $bill_amount,
    //         'discount' => $discount,
    //         'total_amount' => $paymentData['total_amount'],
    //         'paid_amount' => $paymentData['paid_amount'],
    //         'due_amount' => 0,
    //         'created_by' => $paymentData['created_by'],
    //         'created_at' => $paymentData['created_at'],
    //         'updated_at' => $paymentData['created_at']
    //     ];
    //     // dd($service_id);
    //     $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
    //     $this->paymentBillService->setRequest($data);
    //     if($payment_bill_id){
    //         $this->paymentBillService->findById($payment_bill_id);
    //         $paymentBill = $this->paymentBillService->update();
    //     }
    //     else {
    //         $paymentBill = $this->paymentBillService->add();
    //     }
    //     // dd($paymentData['redeem_points']);
    //     if($paymentData['redeem_points'] > 0 || isset($paymentData['payment_modes'])){
    //         // payment create
    //         $data_payment = [
    //             'bill_id' => $paymentBill->id,
    //             'date' => date('Y-m-d',strtotime($paymentData['created_at'])),
    //             'recpit_no' => $this->paymentService->recpitIncrementId('myMD','MB-receipt',8),
    //             'status' => 'Paid',
    //             'amount' => $paymentData['paid_amount'],
    //             'redeem_points' => $paymentData['redeem_points'],
    //             'openning_points' => $paymentData['openning_points'],
    //             'closing_points' => $paymentData['closing_points'],
    //             'created_by' => $paymentData['created_by'],
    //             'created_at' => $paymentData['created_at'],
    //             'updated_at' => $paymentData['created_at']
    //         ];
    //         $payment_id = DB::table('payments')->where(['bill_id' => $paymentBill->id])->value('id');
    //         $this->paymentService->setRequest($data_payment);
    //         if($payment_id){
    //             $this->paymentService->findById($payment_id);
    //             $payment = $this->paymentService->update();
    //         }
    //         else {
    //             $payment = $this->paymentService->add();
    //         }
    //         // payment details
    //         $total_amount = 0;
    //         $total_discount = 0;
    //         // dd($paymentData);
    //         if ($paymentData['payment_modes'] != '') {
    //             DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
    //             $payment_modes = explode(',',$paymentData['payment_details'][0]->payment_mode);
    //             $amounts = explode(',',$paymentData['payment_details'][0]->breakup_payment_amount);
    //             $payment_details_arr = explode(',',$paymentData['payment_details'][0]->payment_details);
    //             foreach ($payment_modes as $key => $row) {
    //                 switch ($row) {
    //                     case 'credit_card':
    //                         $payment_details = $payment_details_arr[$key-1] ?? '';
    //                         break;
    //                     case 'debit_card':
    //                         $payment_details = $payment_details_arr[$key-1] ?? '';
    //                         break;
    //                     case 'upi':
    //                         $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
    //                         break;
    //                     default:
    //                         $payment_details = null;
    //                         break;
    //                 }
    //                 $data_payment_dtl = [
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $data_payment_dtl['payment_id'] = $payment->id;
    //                 $data_payment_dtl['payment_mode'] = $row;
    //                 $data_payment_dtl['amount'] = $amounts[$key] ?? 0;
    //                 $data_payment_dtl['payment_details'] = $payment_details;
    //                 $total_amount += $amounts[$key] ?? 0;
    //                 // dd($data_payment_dtl);
    //                 $this->paymentDetailService->setRequest($data_payment_dtl);
    //                 $this->paymentDetailService->add();
    //                 // dd($this->paymentDetailService);
    //             }
    //         }
    //         // reward points debit
    //         if(!empty($paymentData['reward_point_debit'])){
    //             foreach ($paymentData['reward_point_debit'] as $debit) {
    //                 $reward = [
    //                     'phone_no' => $phone,
    //                     'date' => $paymentData['created_at'],
    //                     'type' => $pay_type,
    //                     'point' => ($debit->point)*-1,
    //                     'credit_debit' => 2,
    //                     'is_redeem' => $debit->is_redeem,
    //                     'bill_id' => $bill_id,
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $reward_id = DB::table('reward_points')->where([
    //                     'phone_no' => $phone,
    //                     'type' => $pay_type,
    //                     'is_redeem' => $debit->is_redeem,
    //                     'bill_id' => $bill_id
    //                 ])->value('id');
    //                 $this->rewardService->setRequest($reward);
    //                 if($reward_id){
    //                     $this->rewardService->findById($reward_id);
    //                     $this->rewardService->update();
    //                 }
    //                 else {
    //                     $this->rewardService->add();
    //                 }
    //             }
                
    //         }
    //         // reward points credit
    //         // $percentage = $this->rewardService->getPercentage(2);
    //         // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
    //         if(!empty($paymentData['reward_point_credit'])){
    //             foreach ($paymentData['reward_point_credit'] as $credit) {
    //                 $reward = [
    //                     'phone_no' => $phone,
    //                     'date' => $paymentData['created_at'],
    //                     'type' => $pay_type,
    //                     'point' => $credit->point,
    //                     'credit_debit' => 1,
    //                     'is_redeem' => $credit->is_redeem,
    //                     'bill_id' => $bill_id,
    //                     // 'expiry' => $credit->expiry,
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $reward_id = DB::table('reward_points')->where([
    //                     'phone_no' => $phone,
    //                     'type' => $pay_type,
    //                     'is_redeem' => $credit->is_redeem,
    //                     'bill_id' => $bill_id
    //                 ])->value('id');
    //                 $this->rewardService->setRequest($reward);
    //                 if($reward_id){
    //                     $this->rewardService->findById($reward_id);
    //                     $this->rewardService->update();
    //                 }
    //                 else {
    //                     $this->rewardService->add();
    //                 }
    //             }
    //         }
    //     }
    //     // dd('payment done');
    //     return true;
    // }

    public function dataMigrateAppoinmentSchedule()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','time_schedules')->select('reverse_count','limit')->first();
        $all_content = DB::table('time_schedules')->count();//$this->dbConnectionInstance->
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        if ($increment_migration->reverse_count * $chunkSize >= $all_content) {
            dd("completed migration");
        }
        // for ($i = $increment_migration->reverse_count; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            
            $this->queryBuilder = DB::table('time_schedules')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                foreach ($chunk_contents as $content) {
                    $doctor_id = DB::table('doctors')->where('user_id', $content->doctor_id)->value('id');
                    // $timestamp = date('Y-m-d H:i:s');
                    switch ($content->status) {
                        case 1:
                            $status = 0;
                            break;
                        case 0:
                            $status = 1;
                            break;
                        default:
                            $status = 1;
                            break;
                    }
                    $contentInsert[] = [
                        'id' => $content->id,
                        'doctor' => $doctor_id ?? null,
                        'weekday' => $content->weekday,
                        'date' => $content->date,
                        's_time' => $content->s_time,
                        'e_time' => $content->e_time,
                        's_time_key' => $content->s_time_key,
                        'duration' => $content->duration,
                        'clinic' => $content->clinic_id,
                        'visit_price' => $content->show_visit_price,
                        'time_in' => $content->time_in,
                        'time_out' => $content->time_out,
                        'is_time' => $content->is_time,
                        'status' => $content->deleted_by ? 3 : $status,
                        // 'created_by' => 1,
                        // 'modified_by' => 1,
                        // 'deleted_by' => $content->status == 3 ? 1 : null,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp,
                        // 'deleted_at' => $content->status == 3 ? $timestamp : null
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('time_schedule')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'time_schedules')
                            ->increment('reverse_count');
                    });
                }
            }
            dd("Loop iteration completed");
        // }
        // dd("Loop iteration completed");
    }
    public function dataMigrateAppoinment()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointments')->select('reverse_count','limit')->first();
        $all_content = DB::table('appointments')->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);

        $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
        if ($startId >= $all_content) {
            dd("completed migration");
        }
        $this->queryBuilder = DB::table('appointments')
            ->orderBy('id')
            ->offset($startId)
            ->limit($all_content)->get();
        $chunk = $this->queryBuilder->chunk($chunkSize);
        // dd($chunk);
        foreach ($chunk as $chunk_contents) {
            $contentInsert = [];
            foreach ($chunk_contents as $content) {
                // dd(date('Y-m-d',$content->date));
                // $timestamp = $content->created_at;
                // $phone = $this->patientService->getParentPhone($content->patient);
                $doctor_id = DB::table('doctors')->where('user_id', $content->doctor_id)->value('id');
                // $schedule_id = DB::table('time_schedules')->where([
                //     'doctor_id' => $doctor_id,
                //     'clinic_id' => $content->clinic_id,
                //     'date' => date('Y-m-d',$content->date),
                //     's_time' => $content->s_time,
                //     'e_time' => $content->e_time,
                // ])->value('id');
                // dd($schedule_id);
                $contentInsert[] = [
                    'id' => $content->id,
                    'patient' => $content->patient_id,
                    // 'patient_phone' => $phone,
                    'doctor' => $doctor_id ?? 0,
                    'clinic_id' => $content->clinic_id,
                    'date' => strtotime($content->date),
                    'time_slot' => $content->time_slot,
                    // 'schedule_id' => $schedule_id ?? null,
                    'remarks' => $content->remarks,
                    'payment_status' => $content->payment_status,
                    // 'unique_bill_id' => $content->unique_bill_id,
                    'unique_queue_number' => $content->unique_queue_number,
                    'cancel_reason' => $content->cancel_reason,
                    'appointment_type' => $content->appointment_type,
                    'hand_write_prescription' => $content->hand_write_prescription,
                    'e_prescription_upload_fairbase' => $content->e_prescription_upload_fairbase,
                    'data_source' => $content->data_source,
                    'is_exist' => $content->is_exist,
                    'executive_name' => $content->executive_name,
                    'executive_name_remarks' => $content->executive_name_remarks,
                    // 'meeting_id' => $content->meeting_id,
                    'status' => $content->status,
                    'created_by' => $content->created_by ? $content->created_by : '',
                    'created_at' => $content->created_at ? $content->created_at : '',
                    'created_at_date' => '',
                    // 'updated_at' => $timestamp
                ];
                // dd($contentInsert);
            }
            if (!empty($contentInsert)) {
                DB::transaction(function () use ($contentInsert) {
                    $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                    $this->dbConnectionInstance->table('appointment')->upsert(
                        $contentInsert,
                        ['id'], // Unique columns to check for duplicates
                        $columnsToUpdate // Columns to update if a duplicate is found
                    );
                    DB::table('temp_increment_migrations')
                        ->where('table_name', 'appointments')
                        ->increment('reverse_count');
                });
            }
        }
        
        dd("Loop iteration completed");
    }
    // public function dataMigrateAppoinmentBill()
    // {
    //     $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointment_bill')->select('count','limit')->first();

    //     $all_content = $this->dbConnectionInstance->table('appointment as a')
    //         ->join('payment as p', 'p.appointment_id', '=', 'a.id')
    //         ->count();
    //     // dd($all_content);
    //     $chunkSize = $increment_migration->limit;
    //     $chunkCount = ceil($all_content / $chunkSize);
    //     // dd($chunkSize,$increment_migration->count);
    //     // for ($i = 0; $i <= $chunkCount; $i++) {
    //         $startId = $increment_migration->count * $chunkSize;
    //         if ($startId > $all_content) {
    //             dd("completed migration");
    //         }
    //         // dd($startId);
    //         $this->queryBuilder = $this->dbConnectionInstance->table('appointment as a')
    //             ->join('payment as p', 'p.appointment_id', '=', 'a.id')
    //             ->select('a.unique_bill_id','a.patient as patient_id', 'p.*')
    //             ->orderBy('p.id')
    //             ->offset($startId)
    //             ->limit($chunkSize);
    //         // dd($this->queryBuilder->get());
    //         // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
    //             // Initialize arrays for content and content tags
    //             // $contentInsert = [];
    //             foreach ($this->queryBuilder->get() as $content) {
    //                 $exciting_cnt = DB::table('payment_bill_master')
    //                         ->where(['service_id'=>$content->appointment_id,'type'=>'OPD'])
    //                         ->count();
    //                 // dd($exciting_cnt);
    //                 if ($exciting_cnt == 0) {
    //                     $service_id = $content->appointment_id;
    //                     $reward_point_debit = $this->dbConnectionInstance->table('reward_points')
    //                         ->where('bill_id', $service_id)
    //                         ->where('type', 1)
    //                         ->where('credit_debit', 2)
    //                         ->select('bill_id','point','credit_debit','is_redeem')
    //                         ->get();
    //                     $reward_point_credit = $this->dbConnectionInstance->table('reward_points')
    //                         ->where('bill_id', $service_id)
    //                         ->where('type', 1)
    //                         ->where('credit_debit', 1)
    //                         ->select('bill_id','point','credit_debit','is_redeem')
    //                         ->get();
    //                     // dd($reward_point_debit,$reward_point_credit);
    //                     $phone = $this->patientService->getParentPhone($content->patient_id);
                        
    //                     $created_by = $content->created_by ? $content->created_by : 1;
    //                     $created_at = $content->created_at ? date('Y-m-d H:i:s', strtotime($content->created_at)) : date('Y-m-d H:i:s', strtotime($content->date));
                        
    //                     // dd($created_by,$created_at);
    //                     // payment bill create
    //                     $paymentCardData = [
    //                         'total_amount' => $content->gross_total,
    //                         'paid_amount' => $content->gross_total,
    //                         'created_by' => $created_by,
    //                         'created_at' => $created_at,
    //                         'updated_at' => $created_at,
    //                         'redeem_points' => $content->redeem_points ?? null,
    //                         'openning_points' => $content->openning_points ?? null,
    //                         'closing_points' => $content->closing_points ?? null,
    //                         'payment_modes' => $content->payment_mode,
    //                         'payment_details' => $content,
    //                         'reward_point_debit' => $reward_point_debit->toArray(),
    //                         'reward_point_credit' => $reward_point_credit->toArray()
    //                     ];
    //                     // dd($paymentCardData);
    //                     $bill_id = $content->unique_bill_id ?? null;
    //                     $pay_type = config('billing.types.1');
    //                     $bill_amount = $content->amount;
    //                     $discount = $content->discount != '' ? $content->discount : 0;
    //                     // dd($bill_id,$pay_type,$bill_amount);
    //                     // payment here
    //                     $this->paymentAppointmentWithReward($paymentCardData,$bill_id,$pay_type,$bill_amount,$discount,$service_id,$content->patient_id,$phone);
                        
    //                     $data_appointment = [
    //                         'payment_status' => 'paid',
    //                         'modified_by' => $created_by,
    //                         'updated_at' => $created_at
    //                     ];
    //                     $this->appointmentService->findById($service_id);
    //                     $this->appointmentService->setRequest($data_appointment);
    //                     $this->appointmentService->update();
    //                 }
    //             }
    //             // dd($contentInsert);
    //             // if (!empty($contentInsert)) {
    //             //     $this->billingService->dataMigrate($contentInsert);
    //             // }
    //             // dd('each loop completed');
    //         // });
            
    //     // }
    //     DB::table('temp_increment_migrations')->where('table_name','appointment_bill')->update([
    //         'count' => $increment_migration->count+1,
    //     ]);
    //     dd("Loop iteration completed");
    //     // return back();
    // }
    // private function paymentAppointmentWithReward($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    // {
    //     // dd($paymentData['payment_modes']);
    //     $data = [
    //         'bill_show_id' => $bill_id,
    //         'type' => $pay_type,//ServiceCategory->membership
    //         'service_id' => $service_id,
    //         'patient_id' => $patient_id,
    //         'bill_amount' => $bill_amount,
    //         'discount' => $discount,
    //         'total_amount' => $paymentData['total_amount'],
    //         'paid_amount' => $paymentData['paid_amount'],
    //         'due_amount' => 0,
    //         'membership_registration_no' => $paymentData['payment_details']->arrogya_membership_no ?? null,
    //         'created_by' => $paymentData['created_by'],
    //         'created_at' => $paymentData['created_at'],
    //         'updated_at' => $paymentData['created_at']
    //     ];
    //     // dd($service_id);
    //     $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
    //     $this->paymentBillService->setRequest($data);
    //     if($payment_bill_id){
    //         $this->paymentBillService->findById($payment_bill_id);
    //         $paymentBill = $this->paymentBillService->update();
    //     }
    //     else {
    //         $paymentBill = $this->paymentBillService->add();
    //     }
    //     // dd($paymentBill);
    //     if($paymentData['redeem_points'] > 0 || isset($paymentData['payment_modes'])){
    //         // payment create
    //         $data_payment = [
    //             'bill_id' => $paymentBill->id,
    //             'date' => date('Y-m-d',strtotime($paymentData['created_at'])),
    //             'recpit_no' => $this->paymentService->recpitIncrementId('myMD','OPD-receipt',8),
    //             'status' => 'Paid',
    //             'amount' => $paymentData['paid_amount'],
    //             'redeem_points' => $paymentData['redeem_points'],
    //             'openning_points' => $paymentData['openning_points'],
    //             'closing_points' => $paymentData['closing_points'],
    //             'created_by' => $paymentData['created_by'],
    //             'created_at' => $paymentData['created_at'],
    //             'updated_at' => $paymentData['created_at']
    //         ];
    //         $payment_id = DB::table('payments')->where(['bill_id' => $paymentBill->id])->value('id');
    //         $this->paymentService->setRequest($data_payment);
    //         if($payment_id){
    //             $this->paymentService->findById($payment_id);
    //             $payment = $this->paymentService->update();
    //         }
    //         else {
    //             $payment = $this->paymentService->add();
    //         }
    //         // payment details
    //         $total_amount = 0;
    //         $total_discount = 0;
    //         // dd($payment);
    //         if ($paymentData['payment_modes'] != '' && $paymentData['total_amount'] > 0) {
    //             DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
    //             $payment_modes = explode(',',$paymentData['payment_details']->payment_mode);
    //             $amounts = explode(',',$paymentData['payment_details']->breakup_payment_amount);
    //             $payment_details_arr = explode(',',$paymentData['payment_details']->payment_details);
    //             // dd($payment_modes,$amounts,$payment_details_arr);
    //             foreach ($payment_modes as $key => $row) {
    //                 switch ($row) {
    //                     case 'credit_card':
    //                         $payment_details = $payment_details_arr[$key-1] ?? '';
    //                         break;
    //                     case 'debit_card':
    //                         $payment_details = $payment_details_arr[$key-1] ?? '';
    //                         break;
    //                     case 'upi':
    //                         $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
    //                         break;
    //                     default:
    //                         $payment_details = null;
    //                         break;
    //                 }
    //                 $data_payment_dtl = [
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $data_payment_dtl['payment_id'] = $payment->id;
    //                 $data_payment_dtl['payment_mode'] = $row;
    //                 $data_payment_dtl['amount'] = $amounts[$key] ?? 0;
    //                 $data_payment_dtl['payment_details'] = $payment_details;
    //                 $total_amount += $amounts[$key] ?? 0;
    //                 // dd($data_payment_dtl);
    //                 $this->paymentDetailService->setRequest($data_payment_dtl);
    //                 $this->paymentDetailService->add();
    //                 // dd($this->paymentDetailService);
    //             }
    //         }
    //         // reward points debit
    //         if(!empty($paymentData['reward_point_debit'])){
    //             foreach ($paymentData['reward_point_debit'] as $debit) {
    //                 $reward = [
    //                     'phone_no' => $phone,
    //                     'date' => $paymentData['created_at'],
    //                     'type' => $pay_type,
    //                     'point' => ($debit->point)*-1,
    //                     'credit_debit' => 2,
    //                     'is_redeem' => $debit->is_redeem,
    //                     'bill_id' => $bill_id,
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $reward_id = DB::table('reward_points')->where([
    //                     'phone_no' => $phone,
    //                     'type' => $pay_type,
    //                     'is_redeem' => $debit->is_redeem,
    //                     'bill_id' => $bill_id
    //                 ])->value('id');
    //                 $this->rewardService->setRequest($reward);
    //                 if($reward_id){
    //                     $this->rewardService->findById($reward_id);
    //                     $this->rewardService->update();
    //                 }
    //                 else {
    //                     $this->rewardService->add();
    //                 }
    //             }
                
    //         }
    //         // reward points credit
    //         // $percentage = $this->rewardService->getPercentage(2);
    //         // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
    //         if(!empty($paymentData['reward_point_credit'])){
    //             foreach ($paymentData['reward_point_credit'] as $credit) {
    //                 $reward = [
    //                     'phone_no' => $phone,
    //                     'date' => $paymentData['created_at'],
    //                     'type' => $pay_type,
    //                     'point' => $credit->point,
    //                     'credit_debit' => 1,
    //                     'is_redeem' => $credit->is_redeem,
    //                     'bill_id' => $bill_id,
    //                     // 'expiry' => $credit->expiry,
    //                     'created_by' => $paymentData['created_by'],
    //                     'created_at' => $paymentData['created_at'],
    //                     'updated_at' => $paymentData['created_at']
    //                 ];
    //                 $reward_id = DB::table('reward_points')->where([
    //                     'phone_no' => $phone,
    //                     'type' => $pay_type,
    //                     'is_redeem' => $credit->is_redeem,
    //                     'bill_id' => $bill_id
    //                 ])->value('id');
    //                 $this->rewardService->setRequest($reward);
    //                 if($reward_id){
    //                     $this->rewardService->findById($reward_id);
    //                     $this->rewardService->update();
    //                 }
    //                 else {
    //                     $this->rewardService->add();
    //                 }
    //             }
    //         }
    //     }
    //     // dd('payment done');
    //     return true;
    // }
    public function dataMigrateAppoinmentVital()
    {
        try {
            Log::info('Migration started for appointment_vitals.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointment_vitals')->select('reverse_count','limit')->first();
            $all_content = DB::table('appointments as a')
            ->join('vitals as p', 'p.appointment_id', '=', 'a.id')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            if ($startId >= $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('appointments as a')
                ->join('vitals as p', 'p.appointment_id', '=', 'a.id')
                ->select('p.*')
                ->orderBy('p.id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd(date('Y-m-d',$content->date));
                    $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'vitals_id' => $content->id,
                        'patient_id' => $content->patient_id,
                        'appointment_id' => $content->appointment_id,
                        'blood_pressure' => $content->blood_pressure,
                        'heart_rate' => $content->heart_rate,
                        'temperature' => $content->temperature,
                        'spo2' => $content->spo2,
                        'height' => $content->height,
                        'weight' => $content->weight,
                        'bmi' => $content->bmi,
                        'blood_group' => $content->blood_group,
                        'head_circumference' => $content->head_circumference,
                        // 'status' => 1,
                        // 'created_by' => 1,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp
                    ];
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('patient_vitals')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'appointment_vitals')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for patient.', ['count' => count($contentInsert)]);
                }
                // if (!empty($contentInsert)) {
                //     $this->vitalsService->dataMigrate($contentInsert);
                //     Log::info('Chunk migrated successfully.', ['count' => count($contentInsert)]);
                // }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateAppoinmentPrescription()
    {
        try {
            Log::info('Migration started for prescription_template.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','prescriptions')->select('reverse_count','limit')->first();
            $all_content = DB::table('appointments as a')
            ->join('prescriptions as p', 'p.appointment_id', '=', 'a.id')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            if ($startId >= $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('appointments as a')
                ->join('prescriptions as p', 'p.appointment_id', '=', 'a.id')
                ->select('p.*')
                ->orderBy('p.id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                // $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $medicine_details = DB::table('prescription_childs')
                        ->select('type_id','name','frequency','days','instruction','notes')
                        ->where('prescription_id', $content->id)
                        ->where('type', 'M')
                        ->orderBy('id', 'asc')
                        ->get();
                    $medicine = [];
                    foreach ($medicine_details as $row) {
                        $each_medicine = [$row->name,$row->frequency,$row->days,$row->instruction,$row->notes];
                        $medicine[] = implode('***', $each_medicine);
                    }
                    $medicine = implode('***###', $medicine).'***###';
                    // dd($medicine);
                    $medicine_ids = DB::table('prescription_childs')
                        ->where('prescription_id', $content->id)
                        ->where('type', 'M')
                        ->orderBy('id', 'asc')
                        ->pluck('type_id')
                        ->toArray();
                    $medicine_ids = array_map(function($item) {
                        return is_null($item) ? "new" : $item;
                    }, $medicine_ids);
                    $lab_test = DB::table('prescription_childs')
                        ->where('prescription_id', $content->id)
                        ->where('type', 'T')
                        ->orderBy('id', 'asc')
                        ->pluck('type_id')->toArray();
                    $lab_test_name = DB::table('tests')->whereIn('id', $lab_test)->pluck('test_name')->toArray();
                    $doctor_id = DB::table('doctors')->where('user_id', $content->doctor_id)->value('id');
                    $patientname = DB::table('patients')->where('id', $content->patient_id)->value('name');
                    $doctorname = DB::table('users')->where('id', $content->doctor_id)->value('username');
                    // dd($lab_test_name);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'date' => $content->date,
                        'patient' => $content->patient_id,
                        'doctor' => $doctor_id ?? 0,
                        'appointment_id' => $content->appointment_id,
                        'symptom' => $content->symptom,
                        'advice' => $content->advice,
                        'diagnosis_examination' => $content->diagnosis_examination,
                        'next_followup_date' => $content->next_followup_date != '0000-00-00' ? $content->next_followup_date : null,
                        'medicine' => $medicine == '***###' ? null : $medicine,
                        'note' => $content->chief_complaints,
                        'patientname' => $patientname,
                        'doctorname' => $doctorname,
                        'lab_test' => empty($lab_test_name) ? '' : implode(',',$lab_test_name),
                        'labtest_id' => empty($lab_test) ? '' : json_encode($lab_test),
                        'final_lab_array' => empty($lab_test) ? '' : implode(',',$lab_test),
                        'med_nm_id' => empty($medicine_ids) ? '' : json_encode($medicine_ids),
                        'template_id' => $content->template_id,
                        'observation' => $content->observation,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('prescription')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'prescriptions')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk reverse migrated successfully for prescription_template.', ['count' => count($contentInsert)]);
                }

                // dd('hiii');
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureBrands()
    {
        try {
            Log::info('Migration started for master_brands.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_brands')->select('reverse_count','limit')->first();
            $all_content = DB::table('master_brands')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('master_brands')
                ->orderBy('brand_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'brand_id' => $content->brand_id,
                        'c_brand_code' => $content->c_brand_code,
                        'c_brand_name' => $content->c_brand_name,
                        'added_on' => $content->added_on,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('master_brand')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_brands')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for master_brands.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureHsns()
    {
        try {
            Log::info('Migration started for master_hsns.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_hsns')->select('reverse_count','limit')->first();
            $all_content = DB::table('master_hsns')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('master_hsns')
                ->orderBy('hsn_code')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'hsn_code' => $content->hsn_code,
                        'hsn_sac_code' => $content->hsn_sac_code,
                        'hsn_sac_name' => $content->hsn_sac_name,
                        'hsn_sac_flag' => $content->hsn_sac_flag,
                        'added_on' => $content->added_on,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('master_hsn')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_hsns')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for master_hsns.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureSuppliers()
    {
        try {
            Log::info('Migration started for master_suppliers.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_suppliers')->select('reverse_count','limit')->first();
            $all_content = DB::table('master_suppliers')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('master_suppliers')
                ->orderBy('supplier_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    // $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'supplier_id' => $content->supplier_id,
                        'c_supplier_code' => $content->c_supplier_code,
                        'supplier_name' => $content->supplier_name,
                        'added_on' => $content->added_on,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('master_supplier')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_suppliers')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for master_suppliers.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    // public function dataMigrateCsqureMaster()
    // {
    //     try {
    //         Log::info('Migration started for Csqure Master.');
    //         $increment_migration = DB::table('temp_increment_migrations')->where('table_name','pharmacy_sales_reg_line_masters')->select('count','limit')->first();
    //         $all_content = $this->dbConnectionInstance->table('cron_executed_rows')
    //         ->count();
    //         // dd($all_content);
    //         $chunkSize = $increment_migration->limit;
    //         $chunkCount = ceil($all_content / $chunkSize);

    //         $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
    //         if ($startId > $all_content) {
    //             // dd("completed migration");
    //             Log::info('completed migration');
    //             dd("completed migration");
    //         }
    //         $this->queryBuilder = $this->dbConnectionInstance->table('cron_executed_rows')
    //             ->orderBy('record_id')
    //             ->offset($startId)
    //             ->limit($all_content)->get();
    //         $chunk = $this->queryBuilder->chunk($chunkSize);
    //         // dd($chunk);
    //         foreach ($chunk as $chunk_contents) {
    //             $contentInsert = [];
    //             foreach ($chunk_contents as $content) {
    //                 $date = $content->date;
    //                 $year = '20' . substr($date, 0, 2);
    //                 $month = substr($date, 2, 2);
    //                 $day = substr($date, 4, 2);
    //                 $formattedDate = "$year-$month-$day";
    //                 if (checkdate($month, $day, $year)) {
    //                     $timestamp = date('Y-m-d H:i:s',strtotime($formattedDate));
    //                 }
    //                 else {
    //                     $formattedDate = $content->date;
    //                     $timestamp = date('Y-m-d H:i:s',strtotime($content->date));
    //                 }
    //                 // dd($formattedDate);
    //                 $contentInsert[] = [
    //                     'id' => $content->record_id,
    //                     'upload_date' => $formattedDate,
    //                     'created_by' => 1,
    //                     'created_at' => $timestamp,
    //                     'updated_at' => $timestamp
    //                 ];
    //             }
    //             if (!empty($contentInsert)) {
    //                 $this->uploadCsquareService->dataMigrate($contentInsert);
    //                 Log::info('Chunk migrated successfully for prescription.', ['count' => count($contentInsert)]);
    //             }
    //         }
    //         Log::info('Migration completed successfully.');
    //         dd("Loop iteration completed");
    //     } catch (\Exception $e) {
    //         Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
    //         dd('Error during migration: ' . $e->getMessage());
    //     }
    // }
    public function dataMigrateCsqure()
    {
        try {
            Log::info('Migration started for Csqure.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','pharmacy_sales_reg_line_details')->select('reverse_count','limit')->first();
            $all_content = DB::table('pharmacy_sales_reg_line_details')//master_reporting
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = DB::table('pharmacy_sales_reg_line_details')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'reporting_id' => $content->id,
                        'batch_no' => $content->batch_no,
                        'invoice_no' => $content->invoice_no,
                        'lc_code' => $content->lc_code,
                        'payment_mode' => $content->payment_mode,
                        'customer_registration_no' => $content->customer_registration_no,
                        'item_code' => $content->item_code,
                        'category_class' => $content->category_class,
                        'branch_code' => $content->branch_code,
                        'invoice_type' => $content->invoice_type,
                        'expiry' => $content->expiry,
                        'mf' => $content->mf,
                        'pk_qty' => $content->pk_qty,
                        'pur_rate' => $content->pur_rate,
                        'loose_eff_pur_rate' => $content->loose_eff_pur_rate,
                        'pack_eff_pur_rate' => $content->pack_eff_pur_rate,
                        'sale_rate' => $content->sale_rate,
                        'mrp' => $content->mrp,
                        'mrp_val' => $content->mrp_val,
                        'item_profit' => $content->item_profit,
                        'st_disc' => $content->st_disc,
                        'disc' => $content->disc,
                        'disc_amt' => $content->disc_amt,
                        'tax_percentage' => $content->tax_percentage,
                        'tax' => $content->tax,
                        'amount' => $content->amount,
                        'cust_code' => $content->cust_code,
                        'customer_name' => $content->customer_name,
                        'customer_contact' => $content->customer_contact,
                        'supplier_code' => $content->supplier_code,
                        'supplier_bill_no' => $content->supplier_bill_no,
                        'supplier_bill_date' => $content->supplier_bill_date,
                        'invoice_date' => $content->invoice_date,
                        'invoice_time' => $content->invoice_time,
                        'ref_invoice_no' => $content->ref_invoice_no,
                        'csv_upload_date' => $content->csv_upload_date,
                        'branch_sh_name' => $content->branch_sh_name,
                        'cgst_amount' => $content->cgst_amount,
                        'sgst_amount' => $content->sgst_amount,
                        'card_number' => $content->card_number,
                        'category_type' => $content->category_type,
                        'medicine_name' => $content->medicine_name,
                        'sms_coupon_flag' => $content->sms_coupon_flag,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('master_reporting')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'pharmacy_sales_reg_line_details')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Csqure.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateOrderMedicine()
    {
        try {
            Log::info('Migration started for order medicine.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','order_medicines')->select('reverse_count','limit')->first();
            $all_content = DB::table('order_medicines')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId >= $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 10000){
                $all_content = 10000;
            }
            $this->queryBuilder = DB::table('order_medicines')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($master_id);
                    // if (in_array($content->data_source,[1])) {
                    //     $data_source = 4;
                    // }
                    // elseif (in_array($content->data_source,[2])) {
                    //     $data_source = 2;
                    // }
                    // else {
                    //     $data_source = 3;
                    // }
                    $data_source = 0; // data_source doesn't match
                    $medicine_ids = DB::table('order_child_medicines')
                        ->where('order_id', $content->id)
                        ->orderBy('id', 'asc')
                        ->pluck('medicine_id')
                        ->toArray();
                    $medicine_quantity = DB::table('order_child_medicines')
                        ->where('order_id', $content->id)
                        ->orderBy('id', 'asc')
                        ->pluck('quantity')
                        ->toArray();
                    // dd($medicine_quantity);
                    // $timestamp = $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient' => $content->patient_id,
                        'medicine_id' => empty($medicine_ids) ? '' : implode(',', $medicine_ids),
                        'medicine_quantity' => empty($medicine_quantity) ? '' : implode(',', $medicine_quantity),
                        // 'patient_phone' => $patient_phone ?? null,
                        'type_of_collection' => $content->type_of_collection,
                        'clinic' => $content->clinic_id,
                        'building_no' => $content->building_no,
                        'full_address' => $content->full_address,
                        'landmark' => $content->landmark,
                        'city' => $content->city,
                        'pincode' => $content->pincode,
                        'prescription_upload' => $content->prescription_upload ?? '',
                        'data_source' => $data_source,
                        'source' => $content->source ?? 0,
                        'remarks' => $content->remarks,
                        'latitude' => $content->latitude ?? '',
                        'longitude' => $content->longitude ?? '',
                        'status' => $content->status,
                        'reject_remarks' => $content->reject_remarks,
                        'is_accept' => $content->is_accept,
                        'created_by' => $content->created_by,
                        'created_at' => $content->created_at,
                        'totalAmt' => $content->total_amount,
                        'amount_recieved' => '',
                        'assign_clinic' => 0,
                        'address' => ''
                    ];
                    // dd($contentInsert);
                }
                
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('order_medicine')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'order_medicines')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk reverse migrated successfully for order medicine.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBatchGenerator()
    {
        try {
            Log::info('Migration started for Diagnastic Batch Generator.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_batch_generators')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_batch_generators')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_batch_generators')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    if ($content->clinic_slug != '') {
                        $clinic_id = DB::table('clinics')
                            ->whereRaw('UPPER(SUBSTRING(clinic_name, 1, 4)) = ?', [strtoupper(substr($content->clinic_slug, 0, 4))])
                            ->value('id');
                        // dd($clinic_id);
                        // $timestamp = date('Y-m-d H:i:s');
                        $contentInsert[] = [
                            'id' => $content->id,
                            // 'clinic_id' => $clinic_id ?? null,
                            'clinic_slug' => $content->clinic_slug,
                            'incrementor' => $content->incrementor,
                            // 'created_at' => $timestamp,
                            // 'updated_at' => $timestamp
                        ];
                    }
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_batchGenerator')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_batch_generators')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Batch Generator.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticHomecollectionCharge()
    {
        try {
            Log::info('Migration started for Diagnastic Home Collection Charge.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_homecollection_charges')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_homecollection_charges')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_homecollection_charges')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                        // dd($clinic_id);
                    // $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'area_range' => $content->area_range,
                        'max_distance' => $content->max_distance,
                        'charges' => $content->charges,
                        'status' => $content->status,
                        // 'created_by' => 1,
                        // 'created_at' => $timestamp,
                        // 'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_homecollection_charges')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_homecollection_charges')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Home Collection Charge.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticSampleType()
    {
        try {
            Log::info('Migration started for Diagnastic sample type.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_types')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_types')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_types')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                        // dd($clinic_id);
                    $timestamp = $content->created_at ? $content->created_at : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_name' => $content->sample_name,
                        'container_name' => $content->container_name,
                        'color' => $content->color,
                        'colorname' => $content->colorname,
                        'is_active' => $content->is_active,
                        'created_by' => $content->created_by,
                        'created_at' => $content->created_at,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_type')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_types')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic sample type.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnastic()
    {
        try {
            Log::info('Migration started for Diagnastic.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collections')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collections')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = DB::table('sample_collections')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $doctor_id = DB::table('doctors')->where('user_id', $content->doctor_id)->value('id') ?? 0;
                    $phlebo_id = DB::table('phlebotomists')->where('user_id', $content->phlebo_id)->value('id') ?? 0;
                    $gross_amount = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $content->id)->sum('amount') ?? 0;
                    $discount_amount = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $content->id)->sum('discount') ?? 0;
                    $deposite_amount = 0;
                    $net_amount = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $content->id)->sum('net_amount') ?? 0;
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient' => $content->patient_id,
                        'test_id' => $content->test_id,
                        'date_of_collection' => $content->date_of_collection,
                        'type_of_collection' => $content->type_of_collection,
                        'appointment_type' => $content->appointment_type,
                        'clinic' => $content->clinic_id,
                        'clinic_assign' => $content->clinic_assign,
                        'building_no' => $content->building_no,
                        'full_address' => $content->full_address,
                        'landmark' => $content->landmark,
                        'city' => $content->city,
                        'pincode' => $content->pincode,
                        'phlebo_assign_status' => $content->phlebo_assign_status,
                        'prev_assignment_history' => $content->prev_assignment_history,
                        'phlebo_id' => $phlebo_id,
                        'prescription_upload' => $content->prescription_upload,
                        'gross_amount' => $gross_amount,
                        'discount_amount' => $discount_amount,
                        'deposite_amount' => $deposite_amount,
                        'net_amount' => $net_amount,
                        'unique_bill_id' => $content->unique_bill_id,
                        'unique_queue_number' => $content->unique_queue_number,
                        'data_source' => $content->data_source,
                        'source' => $content->source,
                        'coupon_code' => $content->coupon_code,
                        'coupon_status' => $content->coupon_status,
                        'date_of_extends' => $content->date_of_extends,
                        'unique_id' => $content->unique_id,
                        'doctor_id' => $doctor_id,
                        'doctor_name' => $content->doctor_name,
                        'remarks' => $content->remarks,
                        'latitude' => $content->latitude,
                        'longitude' => $content->longitude,
                        'offered_type' => $content->offered_type,
                        'offered_id' => $content->offered_id,
                        'is_homecollection' => $content->is_homecollection,
                        'hc_quantity_arearange' => $content->hc_quantity_arearange,
                        'patient_esign' => $content->patient_esign,
                        'fully_paid' => $content->fully_paid,
                        // 'doctor_aviled_offer' => $content->doctor_aviled_offer,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'date_of_entry' => date('Y-m-d', strtotime($content->created_at)),
                        'date_time_of_entry' => $content->created_at,
                        'executive_name' => '',
                        'executive_name_remarks' => '',
                        'packege_type' => '',
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collections')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupTest()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Test.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_tests')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_breakup_tests')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_collection_breakup_tests')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_collection_id' => $content->sample_collection_id ? $content->sample_collection_id : 0,
                        'unique_id' => $content->unique_id ? $content->unique_id : '',
                        'package_id' => $content->package_id ? $content->package_id : 0,
                        'test_id' => $content->test_id ? $content->test_id : 0,
                        'itdose_testid' => $content->itdose_testid ? $content->itdose_testid : 0,
                        'test_code' => $content->test_code ? $content->test_code : '',
                        'sample_type' => $content->sample_type ? $content->sample_type : '',
                        'sin_no' => $content->sin_no ? $content->sin_no : '' ,
                        'vial_qty' => $content->vial_qty ? $content->vial_qty : 0,
                        'transferred_to' => $content->transferred_to,
                        'is_urgent' => $content->is_urgent ? $content->is_urgent : 0,
                        'fieldboy' => $content->fieldboy ? $content->fieldboy : 0,
                        'courier_details' => $content->courier_details ? $content->courier_details : '',
                        'docketno' => $content->docketno ? $content->docketno : '',
                        'batchno' => $content->batchno ? $content->batchno : '',
                        'sin_created_by' => $content->sin_created_by ? $content->sin_created_by : 0,
                        'sin_created_on' => $content->sin_created_on,
                        'is_centrifuge' => $content->is_centrifuge,
                        'segregation_created_by' => $content->segregation_created_by ? $content->segregation_created_by : 0,
                        'segregation_created_on' => $content->segregation_created_on,
                        'transfer_by' => $content->transfer_by ? $content->transfer_by : 0,
                        'transfer_on' => $content->transfer_on,
                        'sample_reject_status' => $content->sample_reject_status,
                        // 'test_delete_status' => $content->test_delete_status,
                        'report_delivery_date' => $content->report_delivery_date ? $content->report_delivery_date : '',
                        'reportGenerated' => $content->reportGenerated ? $content->reportGenerated : '',
                        'itdose_test_status' => $content->itdose_test_status ? $content->itdose_test_status : '',
                        'sample_status' => $content->status,
                        'created_by' => $content->created_by,
                        'modified_by' => $content->modified_by ? $content->modified_by : 0,
                        'test_delete_status' => $content->deleted_by == 1 ? 2 : 1,
                        'created_on' => $content->created_at,
                        'modified_on' => $content->updated_at
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_breakup_test')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_breakup_tests')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Test.', ['count' => count($contentInsert)]);
                }
                // private $diagnosticService;
                // private $diagnosticBreakupTestService;
                // private $diagnosticBreakupBillService;
                // private $diagnosticPhleboAssignmentService;
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupBill()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Bill.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_bills')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_breakup_bills')
            ->whereNotNull('item_id')
            ->whereNull('home_collection_id')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_collection_breakup_bills')
                ->whereNotNull('item_id')
                ->whereNull('home_collection_id')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'collection_id' => $content->sample_collection_id,
                        'item_id' => $content->item_id,
                        'amount' => $content->amount,
                        'discount' => $content->discount,
                        'net_amount' => $content->net_amount,
                        'test_delete_status' => $content->deleted_by == 1 ? 2 : 1,
                        'payment_id' => 0,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_breakup_bill')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_breakup_bills')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Bill.', ['count' => count($contentInsert)]);
                }
                // private $diagnosticService;
                // private $diagnosticBreakupTestService;
                // private $diagnosticBreakupBillService;
                // private $diagnosticPhleboAssignmentService;
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    // public function dataMigrateDiagnasticPayment()
    // {
    //     $increment_migration = DB::table('temp_increment_migrations')->where('table_name','diagnastic_payment')->select('count','limit')->first();

    //     $all_content = $this->dbConnectionInstance->table('sample_collection as s')
    //         ->join('payment as p', 'p.sample_collection_id', '=', 's.id')
    //         ->select('p.sample_collection_id')
    //         ->groupBy('p.sample_collection_id')
    //         ->get();
    //     $all_content = count($all_content);
    //     // dd($all_content);
    //     $chunkSize = $increment_migration->limit;
    //     $chunkCount = ceil($all_content / $chunkSize);
    //     // dd($chunkSize,$increment_migration->count);
    //     // for ($i = 0; $i <= $chunkCount; $i++) {
    //         $startId = $increment_migration->count * $chunkSize;
    //         if ($startId > $all_content) {
    //             dd("completed migration");
    //         }
    //         // dd($startId);
    //         $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection as s')
    //             ->join('payment as p', 'p.sample_collection_id', '=', 's.id')
    //             // ->select('s.id as sample_id','s.unique_bill_id','s.patient as patient_id', 'p.*')
    //             // ->where('p.sample_collection_id', 24741)
    //             ->select('p.sample_collection_id')
    //             ->groupBy('p.sample_collection_id')
    //             ->orderBy('p.sample_collection_id')
    //             ->offset($startId)
    //             ->limit($chunkSize);
    //         // dd($this->queryBuilder->get());
    //         // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
    //             // Initialize arrays for content and content tags
    //             // $contentInsert = [];
    //             foreach ($this->queryBuilder->get() as $content) {
    //                 $payment_dtl = $this->dbConnectionInstance->table('payment as p')
    //                 ->join('sample_collection as s', 's.id', '=', 'p.sample_collection_id')
    //                 ->select('s.id as sample_id','s.unique_bill_id','s.patient as patient_id', 'p.*')
    //                 ->where('p.sample_collection_id', $content->sample_collection_id)
    //                 ->orderBy('p.id')
    //                 ->get();
    //                 // dd($payment_dtl);
    //                 $service_id = $content->sample_collection_id;
    //                 $reward_point_debit = $this->dbConnectionInstance->table('reward_points')
    //                     ->where('bill_id', $service_id)
    //                     ->where('type', 2)
    //                     ->where('credit_debit', 2)
    //                     ->select('bill_id','point','credit_debit','is_redeem')
    //                     ->get();
    //                 $reward_point_credit = $this->dbConnectionInstance->table('reward_points')
    //                     ->where('bill_id', $service_id)
    //                     ->where('type', 2)
    //                     ->where('credit_debit', 1)
    //                     ->select('bill_id','point','credit_debit','is_redeem')
    //                     ->get();
    //                 // dd($service_id,$reward_point_debit,$reward_point_credit);
    //                 $phone = $this->patientService->getParentPhone($payment_dtl[0]->patient_id);
                    
    //                 // payment bill create
    //                 $paymentData = [
    //                     'payment_dtl' => $payment_dtl,
    //                     'reward_point_debit' => $reward_point_debit,
    //                     'reward_point_credit' => $reward_point_credit
    //                 ];
    //                 // dd($paymentData);
    //                 $bill_id = $payment_dtl[0]->unique_bill_id ?? null;
    //                 $pay_type = config('billing.types.2');
    //                 // payment here
    //                 $this->paymentDiagnasticWithReward($paymentData,$bill_id,$pay_type,$service_id,$payment_dtl[0]->patient_id,$phone);
                    
    //             }
    //             // dd($contentInsert);
    //             // if (!empty($contentInsert)) {
    //             //     $this->billingService->dataMigrate($contentInsert);
    //             // }
    //             // dd('each loop completed');
    //         // });
            
    //     // }
    //     DB::table('temp_increment_migrations')->where('table_name','diagnastic_payment')->update([
    //         'count' => $increment_migration->count+1,
    //     ]);
    //     dd("Loop iteration completed");
    //     // return back();
    // }
    // private function paymentDiagnasticWithReward($paymentData,$bill_id,$pay_type,$service_id,$patient_id,$phone)
    // {
    //     $breakup_bill = SampleCollectionBreakupBill::where('sample_collection_id', $service_id)->get();
    //     $payment_paid_amount = 0;
    //     $payment_refund_amount = 0;
    //     $pluck_payment_paid = $paymentData['payment_dtl']->where('status', '!=', 'refund')->pluck('breakuptotalamount');
    //     $pluck_payment_refund = $paymentData['payment_dtl']->where('status', 'refund')->pluck('breakuptotalamount');
    //     foreach ($pluck_payment_paid as $value) {
    //         $payment_paid_amount += intval($value);
    //     }
    //     foreach ($pluck_payment_refund as $value) {
    //         $payment_refund_amount += intval($value);
    //     }
    //     // dd($payment_paid_amount,$payment_refund_amount);
    //     $reward_redeem = $paymentData['reward_point_debit']->where('is_redeem', 2)->sum('point');
    //     $paid_amount = $payment_paid_amount + $reward_redeem - $payment_refund_amount;
    //     $due_amount = ($breakup_bill->sum('net_amount') ?? 0) - $paid_amount;
    //     // dd($paid_amount,$due_amount);
    //     $data = [
    //         'bill_show_id' => $bill_id,
    //         'type' => $pay_type,//ServiceCategory->membership
    //         'service_id' => $service_id,
    //         'patient_id' => $patient_id,
    //         'bill_amount' => $breakup_bill->sum('amount') ?? 0,
    //         'discount' => $breakup_bill->sum('discount') ?? 0,
    //         'total_amount' => $breakup_bill->sum('net_amount') ?? 0,
    //         'paid_amount' => intval($paid_amount),
    //         'due_amount' => intval($due_amount),
    //         'membership_registration_no' => $paymentData['payment_dtl'][0]->arrogya_membership_no ?? null,
    //         'created_by' => $paymentData['payment_dtl'][0]->created_by,
    //         'created_at' => $paymentData['payment_dtl'][0]->created_at,
    //         'updated_at' => $paymentData['payment_dtl'][0]->created_at
    //     ];
    //     $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
    //     $this->paymentBillService->setRequest($data);
    //     if($payment_bill_id){
    //         $this->paymentBillService->findById($payment_bill_id);
    //         $paymentBill = $this->paymentBillService->update();
    //     }
    //     else {
    //         $paymentBill = $this->paymentBillService->add();
    //     }
    //     // dd($paymentBill);
    //     foreach ($paymentData['payment_dtl'] as $key => $row) {
    //         // $sample_collection = SampleCollection::where('id', $row->sample_id)->first();
    //         if($row->redeem_points > 0 || isset($row->payment_mode)){
    //             // payment create
    //             $data_payment = [
    //                 'bill_id' => $paymentBill->id,
    //                 'date' => date('Y-m-d',strtotime($row->created_at)),
    //                 'recpit_no' => $this->paymentService->recpitIncrementId('myMD','DG-receipt',8),
    //                 'status' => $row->status == 'refund' ? 'Refund' : 'Paid',
    //                 'amount' => $row->breakuptotalamount,
    //                 'redeem_points' => $row->redeem_points,
    //                 'openning_points' => $row->openning_points,
    //                 'closing_points' => $row->closing_points,
    //                 'created_by' => $row->created_by,
    //                 'created_at' => $row->created_at,
    //                 'updated_at' => $row->created_at
    //             ];
                
    //             $payment_id = DB::table('payments')->where(Arr::except($data_payment, ['recpit_no','openning_points','closing_points','updated_at']))->value('id');
    //             // dd($payment_id,Arr::except($data_payment, ['recpit_no','created_by','created_at','updated_at']));
    //             $this->paymentService->setRequest($data_payment);
    //             if($payment_id){
    //                 $this->paymentService->findById($payment_id);
    //                 $payment = $this->paymentService->update();
    //             }
    //             else {
    //                 $payment = $this->paymentService->add();
    //             }
    //             // dd($row);
    //             // payment details
    //             $total_amount = 0;
    //             $total_discount = 0;
    //             // dd($payment);
    //             if ($row->payment_mode != '') {
    //                 DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
                    
    //                 $payment_modes = explode(',',$row->payment_mode);
    //                 $amounts = explode(',',$row->breakup_payment_amount);
    //                 $payment_details_arr = explode(',',$row->payment_details);
    //                 // dd($payment_modes,$amounts,$payment_details_arr);
    //                 foreach ($payment_modes as $key => $row_mode) {
    //                     switch ($row_mode) {
    //                         case 'credit_card':
    //                             $payment_details = $payment_details_arr[$key-1] ?? '';
    //                             break;
    //                         case 'debit_card':
    //                             $payment_details = $payment_details_arr[$key-1] ?? '';
    //                             break;
    //                         case 'upi':
    //                             $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
    //                             break;
    //                         default:
    //                             $payment_details = null;
    //                             break;
    //                     }
    //                     $data_payment_dtl = [
    //                         'created_by' => $row->created_by,
    //                         'created_at' => $row->created_at,
    //                         'updated_at' => $row->created_at
    //                     ];
    //                     $data_payment_dtl['payment_id'] = $payment->id;
    //                     $data_payment_dtl['payment_mode'] = $row_mode == 'reward_points' ? 'refund_reward_points' : $row_mode;
    //                     $data_payment_dtl['amount'] = intval($amounts[$key] ?? 0);
    //                     $data_payment_dtl['payment_details'] = $payment_details;
    //                     $total_amount +=intval($amounts[$key] ?? 0);
    //                     // dd($data_payment_dtl);
    //                     $this->paymentDetailService->setRequest($data_payment_dtl);
    //                     $this->paymentDetailService->add();
    //                     // dd($this->paymentDetailService);
    //                 }
    //             }
                
    //         }
            
    //     }
    //     // reward points debit
    //     if(!empty($paymentData['reward_point_debit']->toArray())){
    //         foreach ($paymentData['reward_point_debit']->toArray() as $debit) {
    //             $reward = [
    //                 'phone_no' => $phone,
    //                 'date' => $row->created_at,
    //                 'type' => $pay_type,
    //                 'point' => ($debit->point)*-1,
    //                 'credit_debit' => 2,
    //                 'is_redeem' => $debit->is_redeem,
    //                 'bill_id' => $bill_id,
    //                 'created_by' => $paymentData['payment_dtl'][0]->created_by,
    //                 'created_at' => $paymentData['payment_dtl'][0]->created_at,
    //                 'updated_at' => $paymentData['payment_dtl'][0]->created_at
    //             ];
    //             $reward_id = DB::table('reward_points')->where([
    //                 'phone_no' => $phone,
    //                 'type' => $pay_type,
    //                 'is_redeem' => $debit->is_redeem,
    //                 'bill_id' => $bill_id
    //             ])->value('id');
    //             $this->rewardService->setRequest($reward);
    //             if($reward_id){
    //                 $this->rewardService->findById($reward_id);
    //                 $this->rewardService->update();
    //             }
    //             else {
    //                 $this->rewardService->add();
    //             }
    //         }
            
    //     }
    //     // reward points credit
    //     // $percentage = $this->rewardService->getPercentage(2);
    //     // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
    //     if(!empty($paymentData['reward_point_credit'])){
    //         foreach ($paymentData['reward_point_credit'] as $credit) {
    //             $reward = [
    //                 'phone_no' => $phone,
    //                 'date' => $row->created_at,
    //                 'type' => $pay_type,
    //                 'point' => $credit->point,
    //                 'credit_debit' => 1,
    //                 'is_redeem' => $credit->is_redeem,
    //                 'bill_id' => $bill_id,
    //                 // 'expiry' => $credit->expiry,
    //                 'created_by' => $paymentData['payment_dtl'][0]->created_by,
    //                 'created_at' => $paymentData['payment_dtl'][0]->created_at,
    //                 'updated_at' => $paymentData['payment_dtl'][0]->created_at
    //             ];
    //             $reward_id = DB::table('reward_points')->where([
    //                 'phone_no' => $phone,
    //                 'type' => $pay_type,
    //                 'is_redeem' => $credit->is_redeem,
    //                 'bill_id' => $bill_id
    //             ])->value('id');
    //             $this->rewardService->setRequest($reward);
    //             if($reward_id){
    //                 $this->rewardService->findById($reward_id);
    //                 $this->rewardService->update();
    //             }
    //             else {
    //                 $this->rewardService->add();
    //             }
    //         }
    //     }
        
    //     // dd($paymentBill);
    //     // dd('payment done');
    //     return true;
    // }
    public function dataMigrateDiagnasticPhleboAssignment()
    {
        try {
            Log::info('Migration started for Diagnastic Phlebo Assignment.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','phlebo_assignments')->select('reverse_count','limit')->first();
            $all_content = DB::table('phlebo_assignments')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('phlebo_assignments')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $role_id = null;
                    $user_id = null;
                    if($content->role_id == 7){
                        $role_id = 1;
                        $user_id = DB::table('phlebotomists')->where('user_id', $content->phlebo_id)->value('id') ?? null;
                    }
                    elseif ($content->role_id == 6) {
                        $role_id = 2;
                        $user_id = DB::table('nurses')->where('user_id', $content->phlebo_id)->value('id') ?? null;
                    }
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_id' => $content->sample_id,
                        'role' => $role_id,
                        'phlebo_id' => $user_id,
                        'schedule_time' => date('H:i', strtotime($content->schedule_time)),
                        'remarks' => $content->remarks,
                        'test_count' => $content->test_count,
                        'collected_testcount' => $content->collected_testcount,
                        'handover_testcount' => $content->handover_testcount,
                        'actual_date_time_of_collection' => $content->actual_date_time_of_collection,
                        'temp_of_bag_at_collection' => $content->temp_of_bag_at_collection,
                        'payment_status' => $content->payment_status,
                        'amount' => $content->amount,
                        'mode_of_payment' => $content->mode_of_payment,
                        'collection_status' => $content->collection_status,
                        'date_time_at_handover' => $content->date_time_at_handover,
                        'temp_of_bag_at_handover' => $content->temp_of_bag_at_handover,
                        'amount_at_handover' => $content->amount_at_handover,
                        'handover_status' => empty($content->handover_status) ? '4' : $content->handover_status,
                        'collected_by' => $content->collected_by,
                        'assign_by' => $content->created_by,
                        'assign_date' => date('Y-m-d', strtotime($content->created_at)),
                        'assign_date_time' => $content->created_at,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('phlebo_assignment')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'phlebo_assignments')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Phlebo Assignment.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticApiLog()
    {
        try {
            Log::info('Migration started for Diagnastic Api Log.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_api_logs')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_api_logs')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = DB::table('sample_collection_api_logs')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'request_id' => $content->request_id,
                        'workorderid' => $content->work_order_id,
                        'api_link' => $content->api_link,
                        'jsondata' => $content->playload_json,
                        'return_jsondata' => $content->response_json,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'created_at' => $content->created_at,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_api_error_log')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_api_logs')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Api Log.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticRecollection()
    {
        try {
            Log::info('Migration started for Diagnastic Recollection.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_barcode_resubmissions')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_barcode_resubmissions')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_collection_barcode_resubmissions')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'workorderid' => $content->work_order_id,
                        'breakuptestid' => $content->breakup_test_id,
                        'test_id' => $content->test_id,
                        'itdose_testid' => $content->itdose_test_id,
                        'sample_type' => $content->sample_type,
                        'sin_no' => $content->sin_no,
                        'status' => $content->status,
                        'request_by' => $content->created_by,
                        'updated_by' => $content->modified_by,
                        'request_at' => $content->created_at,
                        'updated_at' => $content->updated_at
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_barcode_resubmission')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_barcode_resubmissions')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Recollection.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupTestItdose()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Test Itdose.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_test_itdoses')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_breakup_test_itdoses')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_collection_breakup_test_itdoses')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'id' => $content->id,
                        'workorder_id' => $content->workorder_id,
                        'itdose_test_id' => $content->itdose_test_id,
                        'test_code' => $content->test_code,
                        'status' => $content->status,
                        'created_on' => $content->created_at
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_api_status')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_breakup_test_itdoses')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Test Itdose.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticEstimation()
    {
        try {
            Log::info('Migration started for Diagnastic Estimation.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_estimations')->select('reverse_count','limit')->first();
            $all_content = DB::table('sample_collection_estimations')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('sample_collection_estimations')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient_id' => $content->patient_id,
                        'full_address' => $content->full_address ? $content->full_address : '',
                        'land_mark' => $content->land_mark ? $content->land_mark : '',
                        'city' => $content->city ? $content->city : '',
                        'pincode' => $content->pincode ? $content->pincode : '',
                        'test_id' => $content->test_id,
                        'collection_id' => $content->collection_id,
                        'clinic_id' => $content->clinic_id,
                        'subtotal' => $content->sub_total,
                        'discount' => $content->discount ? $content->discount : 0,
                        'grosstotal' => $content->gross_total,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'created_on' => $content->created_at,
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('sample_collection_estimation')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_estimations')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Estimation.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCampaign()
    {
        try {
            Log::info('Migration started for order medicine.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','campaign_masters')->select('reverse_count','limit')->first();
            $all_content = DB::table('campaign_masters')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = DB::table('campaign_masters')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                // dd($chunk_contents);
                foreach ($chunk_contents as $content) {
                    //c "Diagnosis Campaign"=>1, "Medicine Campaign"=>2, "none"=>3
                    //l "Diagnosis Campaign"=>1, "Medicine Campaign"=>2, "Diagnostic & Medicine (Both)"=>3, "OTC Product Campaign"=> 4
                    //c "default"=>0 ,"Self Care Product"=>1, "OTC Product"=>2, "Medicine"=>3
                    //l "default"=>0, "Self Care Product"=>1, "OTC Food Product"=>2
                    if ($content->status == 0) {//0:inactive,1:active   active=>1, inactive=>2
                        $status = 2;
                    }
                    else {
                        $status = 1;
                    }
                    $campaign_type = 0;
                    $medicine_type = 0;
                    $diagnostic_uses = 0;
                    $discount_type = 0;
                    $discount = 0;
                    $diagnostic_test = null;
                    $test_individual = 1;
                    $diagnostic_package = null;
                    $package_discount_type = 0;
                    $package_discount = 0;
                    $package_individual = 1;

                    switch ($content->medicine_type) {
                        case 0:
                            if($content->campaign_type == 2){
                                $medicine_type = 3;
                                $discount_type = $content->medicine_discount_type;
                                $discount = $content->medicine_discount;
                            }
                            else{
                                $medicine_type = 0;
                            }
                            break;
                        case 1:
                            $campaign_type = 2;
                            $medicine_type = 1;
                            $discount_type = $content->self_otc_discount_type;
                            $discount = $content->self_otc_discount;
                            break;
                        case 2:
                            $campaign_type = 2;
                            $medicine_type = 2;
                            $discount_type = $content->food_otc_discount_type;
                            $discount = $content->food_otc_discount;
                            break;
                        default:
                            $medicine_type = $content->medicine_type;
                            break;
                    }
                    switch ($content->campaign_type) {
                        case 1:
                            $campaign_type = 1;
                            $test_cnt = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',1)->count();
                            $pkg_cnt = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',2)->count();
                            // dd($content->id,$test_cnt,$pkg_cnt);
                            if($test_cnt > 0 && $pkg_cnt == 0){//only test
                                $diagnostic_uses = 2;
                                $child = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',1)->first();
                                if($child){
                                    $diagnostic_test = $child->test_ids == '["all"]' ? null : $child->test_ids;
                                    $test_individual = $child->discount_for;
                                    $discount_type = $child->discount_type;
                                    $discount = $child->discount;
                                }
                            }
                            elseif ($test_cnt == 0 && $pkg_cnt > 0) {//only package
                                $diagnostic_uses = 3;
                                $child = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',2)->first();
                                if($child){
                                    $diagnostic_package = $child->test_ids == '["all"]' ? null : $child->test_ids;
                                    $package_discount_type = $child->discount_type;
                                    $package_discount = $child->discount;
                                    $package_individual = $child->discount_for;
                                }
                            }
                            elseif ($test_cnt > 0 && $pkg_cnt > 0) {//test & package
                                $diagnostic_uses = 1;
                                $child = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',1)->first();
                                if($child){
                                    $diagnostic_test = $child->test_ids == '["all"]' ? null : $child->test_ids;
                                    $test_individual = $child->discount_for;
                                    $discount_type = $child->discount_type;
                                    $discount = $child->discount;
                                }
                                $child = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',2)->first();
                                if($child){
                                    $diagnostic_package = $child->test_ids == '["all"]' ? null : $child->test_ids;
                                    $package_discount_type = $child->discount_type;
                                    $package_discount = $child->discount;
                                    $package_individual = $child->discount_for;
                                }
                            }
                            // test
                            break;
                        case 2:
                            $campaign_type = 2;
                            break;
                        default:
                            $campaign_type = 3;
                            break;
                    }
                    
                    // dd($content);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'campaign_name' => $content->campaign_name,
                        'is_for_coupon' => $content->is_for_coupon,
                        'start_date' => $content->start_date,
                        'date_of_expery' => $content->end_date,
                        'status' => $status,
                        'created_by' => $content->created_by,
                        'updated_by' => $content->modified_by,
                        'created_at' => $content->created_at,
                        'updated_at' => $content->updated_at,
                        'campaign_type' => $campaign_type,
                        'url_slag' => $content->url_slag,
                        'diagnostic_test' => $diagnostic_test,
                        'clinic' => $content->clinic == '["all"]' ? null : $content->clinic,
                        'image_path' => $content->image_path,
                        'medicine_type' => $medicine_type,
                        'applicable_for' => $content->applicable_for,
                        'coupon_expeiry_date' => $content->coupon_expeiry_date,
                        'discount_type' => $discount_type,
                        'discount' => $discount,
                        'test_individual' => $test_individual,
                        'is_hc_free' => $content->is_hc_free,
                        'diagnostic_package' => $diagnostic_package,
                        'package_discount_type' => $package_discount_type,
                        'package_discount' => $package_discount,
                        'diagnostic_uses' => $diagnostic_uses,
                        'package_individual' => $package_individual,                        
                        'min_limit' => $content->min_limit,
                        'type_of_expeiry' => 2,
                        'duration_of_expery' => 0,                
                    ];
                    // dd($contentInsert);
                }
                // dd($contentInsert);
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('campaign_master')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'campaign_masters')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk migrated successfully for campaign_master.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateRewardPoints()
    {
        try {
            Log::info('Migration started for reward points.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','reward_points')->select('reverse_count','limit')->first();
            $all_content = DB::table('reward_points')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->reverse_count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = DB::table('reward_points')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // "Default"=>0,
                    // 1:opd,2:diagnostic,3:membership,4:ABsmartcard,5:diagnostic_it_dose,6:diagnostic_manual_entry,7:csqarepharmacyreport,
                    // "Backend Pharmacy"=>8,9=>diagnostic dispute test,10=>froentend diagnostic
                    switch($content->type){
                        case 'OPD':
                            $type = 1;
                            break;
                        case 'DG':
                            $type = 2;
                            break;
                        case 'MB':
                            $type = 3;
                            break;
                        case 'MB-CARD':
                            $type = 4;
                            break;
                        case 'diagnostic_it_dose':
                            $type = 5;
                            break;
                        case 'diagnostic_manual_entry':
                            $type = 6;
                            break;
                        case 'csqarepharmacyreport':
                            $type = 7;
                            break;
                        case 'backendpharmacy':
                            $type = 8;
                            break;
                        case 'diagnostic_dispute_test':
                            $type = 9;
                            break;
                        case 'frontend_diagnostic':
                            $type = 10;
                            break;
                        default:
                            $type = 0;
                            break;
                    }
                    $contentInsert[] = [
                        'id' => $content->id,
                        'phone_no' => $content->phone_no,
                        'date' => $content->date,
                        'type' => $type,
                        'point' => $content->point,
                        'credit_debit' => $content->credit_debit,
                        'is_redeem' => $content->is_redeem,
                        'bill_id' => $content->bill_id ? $content->bill_id : '',
                        'it_dose_transtaction_id' => $content->it_dose_transtaction_id ? $content->it_dose_transtaction_id : 0,
                        'csqare_invNo' => $content->csqare_invNo ? $content->csqare_invNo : '',
                        // 'expiry' => $content->expiry,
                        'status' => $content->status
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        $this->dbConnectionInstance->table('reward_points')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'reward_points')
                            ->increment('reverse_count');
                    });
                    Log::info('Chunk reverse migrated successfully for reward_points.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Reverse Migration completed successfully.');
            dd("completed migration");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
}
