<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Payment extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'bill_id',
        'date',
        'amount',
        // 'discount',
        // 'gross_total',
        'remarks',
        'redeem_points',
        'openning_points',
        'closing_points',
        'recpit_no',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function paymentBillMaster(): BelongsTo
    {
        return $this->belongsTo(PaymentBillMaster::class, 'bill_id', 'id');
    }
    public function paymentBillSample(): BelongsTo
    {
        return $this->paymentBillMaster()->select('id','service_id')->where('type','DG');
    }
    public function paymentDetails(): HasMany
    {
        return $this->hasMany(PaymentDetail::class, 'payment_id', 'id');
    }
    public function paymentDetailCash()
    {
        return $this->paymentDetails()->where('payment_mode','cash');
    }
    public function paymentDetailDebitCard()
    {
        return $this->paymentDetails()->where('payment_mode','debit_card');
    }
    public function paymentDetailCreditCard()
    {
        return $this->paymentDetails()->where('payment_mode','credit_card');
    }
    public function paymentDetailUpi()
    {
        return $this->paymentDetails()->where('payment_mode','upi');
    }
    public function paymentDetailRewardPoint()
    {
        return $this->paymentDetails()->where('payment_mode','refund_reward_points');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    
}
