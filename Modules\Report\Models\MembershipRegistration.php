<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;


class MembershipRegistration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'id',
        'patient_id',
        'phone',
        'clinic_id',
        'start_date',
        'end_date',
        'registration_no',
        'category_id',
        'card_type',
        'registration_date',
        'smart_card',
        'data_source',
        'pincode',
        'remarks',
        'is_renewal',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];

    public function clinics(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }
    public function verifyMemberships(): HasMany
    {
        return $this->hasMany(VerifyMembership::class, 'membership_id', 'id');
    }
    public function patients(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }

    public function memberships(): BelongsTo
    {
        return $this->belongsTo(Membership::class, 'card_type', 'id');
    }
    public function pharmacySalesRegLineDetails(): HasMany
    {
        return $this->hasMany(PharmacySalesRegLineDetail::class, 'customer_contact', 'phone');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function paymentBill(): HasMany
    {
        return $this->hasMany(PaymentBillMaster::class, 'service_id', 'id')
            ->whereIn('type', ['MB']);
    }
}
