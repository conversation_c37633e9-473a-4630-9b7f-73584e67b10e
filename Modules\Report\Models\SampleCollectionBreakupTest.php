<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SampleCollectionBreakupTest extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'sample_collection_id',
        'unique_id',
        'package_id',
        'test_id',
        'itdose_testid',
        'test_code',
        'sample_type',
        'sin_no',
        'vial_qty',
        'transferred_to',
        'is_urgent',
        'fieldboy',
        'courier_details',
        'docketno',
        'batchno',
        'sin_created_by',
        'sin_created_on',
        'is_centrifuge',
        'segregation_created_by',
        'segregation_created_on',
        'transfer_by',
        'transfer_on',
        'itdose_test_status',
        'sample_reject_status',
        // 'test_delete_status',
        'report_delivery_date',
        'reportGenerated',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $dates = ['deleted_at'];
    public function sampleCollection(): BelongsTo
    {
        return $this->belongsTo(SampleCollection::class, 'sample_collection_id', 'id')->with('patient:id,name,uhid_no','clinic:id,clinic_name','assignAllPhlebotomists:id,amount');
        
    }

    
    public function test()
    {
        return $this->belongsTo(Test::class, 'test_id', 'id');
    }
    public function sinCreatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sin_created_by', 'id');
    }
    public function segregationCreatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'segregation_created_by', 'id');
    }
    public function transferBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'transfer_by', 'id');
    }
}
