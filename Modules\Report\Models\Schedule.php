<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Schedule extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'time_schedules';
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'doctor_id',
        'weekday',
        'date',
        's_time',
        'e_time',
        's_time_key',
        'duration',
        'clinic_id',
        'visit_price',
        'show_visit_price',
        'time_in',
        'time_out',
        'is_time',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function clinics(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }    
    public function users(): BelongsTo
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')
            ->with('scheduleDoctors:user_id,doctor_type,speciality');
    }
    public function doctor(): BelongsTo
    {
        return $this->belongsTo(Doctor::class, 'doctor_id', 'id');
        // ->with('speciality:id,speciality'); // eager load speciality
    }
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class, 'schedule_id', 'id');
    }
    public function completedAppointments()
    {
        return $this->appointments()->where('status', 8);
    }
}
