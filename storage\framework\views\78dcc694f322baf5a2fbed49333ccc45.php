<?php if(count($phlebo_list) > 0): ?>
    <?php $__currentLoopData = $phlebo_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $phlebo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $isChecked = ($selectedRadioId === "phlebo_assign_id_".$phlebo->id) ? 'checked' : '';
        ?>
        <tr>
            <td><span><?php echo e($phlebo->username); ?></span></td>
            <td class="<?php echo e($phlebo->count_7_9 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_7_9); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,1" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
            <td class="<?php echo e($phlebo->count_9_11 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_9_11); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,2" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id2_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id2_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
            <td class="<?php echo e($phlebo->count_11_13 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_11_13); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,3" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id3_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id3_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
            <td class="<?php echo e($phlebo->count_13_15 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_13_15); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,4" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id4_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id4_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
            <td class="<?php echo e($phlebo->count_15_17 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_15_17); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,5" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id5_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id5_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
            <td class="<?php echo e($phlebo->count_17_19 >= 6 ? 'disabledclass' : ''); ?>"><?php echo e($phlebo->count_17_19); ?>

                <div class="form-check p-0 border-0 mb-0 schdule-check">
                    <input class="form-check-input btn-check districtclass lng_check" value="<?php echo e($phlebo->id); ?>,6" type="radio"
                        name="phlebo_assign_id" id="phlebo_assign_id6_<?php echo e($phlebo->id); ?>" <?php echo e($isChecked); ?>>
                    <label class="btn py-1 px-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                        for="phlebo_assign_id6_<?php echo e($phlebo->id); ?>">
                        <svg id="fi_17554905" enable-background="new 0 0 100 100" viewBox="0 0 100 100"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="m31.5346928 72.2310867c8.3144627-12.7819633 38.8950882-45.6819611 64.2554245-62.7737541 1.0770569-.7258902 2.2932892.6990004 1.4002457 1.6421041-24.092659 25.4432659-49.8449974 54.2226334-64.635025 79.108345-.4120026.6932373-1.4065876.7176208-1.8370857.0357132-7.442505-11.7888745-13.8256322-28.7346067-27.5073496-33.9273824-1.0163932-.3857613-.9187098-1.8227577.1381836-2.077404 13.1113319-3.1590233 19.2550297 8.054306 28.185606 17.9908295v.0015487z">
                            </path>
                        </svg>
                    </label>
                </div>
            </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <tr>
        <td colspan="7">No phlebotomists found for this pincode.</td>
    </tr>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/diagnostic/api/phleboList.blade.php ENDPATH**/ ?>