<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;

class MedicineOrderReportExport implements FromView, ShouldAutoSize
{
    protected $data;
     protected $status_list;
    // Accept data via constructor
    public function __construct($data,$status_list)
    {
        // dd($data,'hii');
        $this->data = $data;
        $this->status_list = $status_list;
    }
    public function view(): View
    {
        $data = $this->data;
         $status_list = $this->status_list;
        // dd($data);
        return view('report::medicineorder.export.medicineorder', compact('data','status_list'));
    }
}
