<?php

namespace Modules\Report\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Modules\Users\Services\UserService;
use Illuminate\Http\Response;
use Modules\Report\Services\SchedulewiseDoctorService;
use Illuminate\Support\Str;
use App\Exports\SchedulewiseDoctorReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class SchedulewiseDoctorReportController extends Controller
{
    private $schedulewiseDoctorService;
    private $userService;
    public function __construct(SchedulewiseDoctorService $schedulewiseDoctorService, UserService $userService)
    {
        $this->schedulewiseDoctorService = $schedulewiseDoctorService;
        $this->userService = $userService;
    }
    public function index(Request $request)
    {

        $data = [
            'clinic_list' => $this->schedulewiseDoctorService->allClinics()->toArray(),
            'doctor_type' => $this->schedulewiseDoctorService->allDoctorTypes()->toArray(),
            'doctor_list' => $this->userService->allUserWithRole('Doctor')->toArray(),

        ];
       
        return view('report::schedulewisedoctor.index', compact('data'));
    }
    public function scheduleWiseDoctor(Request $request)
    {
        try {
            $request['with'] = [
                'clinics' => 'id,clinic_name',
                'users' => 'id,username as doctor_name',
            ];
            $withFunc = $request['withFunc'];
            $withFunc = [
                'completedAppointments as completed_appointments_count' => [
                    'method' => 'withCount',
                ]
            ];
            if (isset($request['filter']['doctor_type'])) {
                $withFunc['users.scheduleDoctors'] = [
                    'method' => 'whereHas',
                    'column' => 'doctor_type',
                    'condition' => '=',
                    'value' => $request['filter']['doctor_type']['value']
                ];
            }
            $request->merge([
                'withFunc' => $withFunc
            ]);
            $this->schedulewiseDoctorService->setRequest($request->except('filter.report_type', 'filter.doctor_type'));
            $this->schedulewiseDoctorService->findAll();
            $this->response['success']  = true;
            $data = $this->schedulewiseDoctorService->getRows();
            $report_type = $request['filter']['report_type']['value'] ?? 1;
            // dd($data);
            $this->response['tbody'] = view('report::schedulewisedoctor.api.list', compact('data', 'report_type'))->render();
            $this->response['tfoot'] = $this->schedulewiseDoctorService->paginationCustom();

            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function ScheduleWiseDoctorExport(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.schedulewisedoctor.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function schedulewiseDoctorExportLink()
    {
        
        $request = request('req');
        $request['with'] = [
            'clinics' => 'id,clinic_name',
            'users' => 'id,username'
        ];
        $request['pagination'] = [];
   
        $this->schedulewiseDoctorService->setRequest($request);
        $data = $this->schedulewiseDoctorService->findAll()->entity->get()->toArray();
        // dd($data);
        $data = Excel::download(new SchedulewiseDoctorReportExport($data), 'schedule-wisedoctor.xlsx');
        return $data;
    }
}
