<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        $timeIn = isset($row['time_in']) ? strtotime($row['time_in']) : null;
        $timeOut = isset($row['time_out']) ? strtotime($row['time_out']) : null;

        $timeSpent = '';
        if ($timeIn && $timeOut) {
            $diffInSeconds = $timeOut - $timeIn;
            $hours = floor($diffInSeconds / 3600);
            $minutes = floor(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $timeSpent = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e($row['users']['doctor_name'] ?? ''); ?></td>
        <td><?php echo e($row['users']['schedule_doctors']['specialitys']['speciality'] ?? ''); ?></td>
        <td><?php echo e($row['users']['schedule_doctors']['doctor_type']['title'] ?? ''); ?></td>
        <td><?php echo e($row['clinics']['clinic_name'] ?? ''); ?></td>
        <td><?php echo e($row['date'] ?? ''); ?></td>
        <?php if($report_type == 1): ?>
            <td><?php echo e($row['s_time']); ?> to <?php echo e($row['e_time']); ?></td>
            <td><?php echo e($row['time_in'] ?? ''); ?></td>
            <td><?php echo e($row['time_out'] ?? ''); ?></td>
        <?php endif; ?>
        <td><?php echo e($timeSpent); ?></td>
        <td><?php echo e($row['completed_appointments_count'] ?? 0); ?></td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/schedulewisedoctor/api/list.blade.php ENDPATH**/ ?>