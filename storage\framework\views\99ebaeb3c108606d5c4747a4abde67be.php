<?php $__env->startSection('title'); ?>
    <?php echo e(config('appointment.title', 'Appointment')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="">
            <?php if(!$id): ?>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card mb-3">
                                <div
                                    class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                                    <div class="header-title">
                                        <h5 class="h5 mb-0">Search Patient</h5>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row justify-content-center">
                                        <div class="form-group col-lg-6 col-md-6 col-12">
                                            <div class="row row-cols-auto align-items-center justify-content-center">
                                                <label for="exampleInputEmail1"
                                                    class="fw-bold col-sm-12 form-label p-0 text-center">Search with Patient
                                                    Name or Phone No</label>
                                                <div class="col-12 col-sm-12">
                                                    <input type="text" class="form-control" name="search_patient"
                                                        id="search_patient" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-1 justify-content-center mb-5">
                                            <a href="<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>">
                                                <button type="button" class="btn btn-gray"
                                                    data-bs-dismiss="modal">Close</button>
                                            </a>

                                            <button onclick="searchPatient()" type="button" name="button"
                                                class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Search</button>
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-12">
                                            <div class="Table-custom-padding1 table-responsive">
                                                <table id="data-list-family"
                                                    class="table table-sm datatable_desc placeholder-glow"
                                                    data-toggle="data-table" style="display: none;">
                                                    <thead>
                                                        <tr>
                                                            <th>
                                                                Name
                                                            </th>
                                                            <th>
                                                                Gender
                                                            </th>
                                                            <th>
                                                                Membership
                                                            </th>
                                                            <th>
                                                                Action
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php echo $__env->make('admin.custom.loading', [
                                                            'td' => 4,
                                                            'action' => 1,
                                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <td colspan="5" class="border-0">
                                                                <button
                                                                    class="btn btn-primary d-flex gap-1 align-items-center justify-content-center placeholder"
                                                                    type="button"></button>
                                                            </td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body placeholder-glow data-add" id="data-add-patient" style="display: none;">
                                <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4, 4, 4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>
                            <div class="card-body placeholder-glow data-add" id="data-add-member" style="display: none;">
                                <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body placeholder-glow p-0" id="data-add-edit" style="display: none">

                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <div class="header-title">
                            <h4 class="card-title">Update Appointment</h4>
                        </div>
                    </div>
                    <div class="card-body placeholder-glow" id="data-add-edit">
                        <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [3, 3, 3, 3]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        <?php if($id): ?>
            addAppointment(0, 0);
        <?php endif; ?>
        <?php if(!$id): ?>
            $(document).ready(function() {
                refreshToken();
            });
        <?php endif; ?>
        $(document).ready(function() {
            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
        $(document).on("change", "#date", function() {
            let date = $(this).val();
            let today = new Date();
            let formattedToday = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
            if (date === formattedToday) {
                $('#status option[value="3"]').prop('disabled', false);
            } else {
                if ($('#status').val() == '3') {
                    $('#status').val('1');
                }
                $('#status option[value="3"]').prop('disabled', true);
            }
        });

        function validatePhoneNumber(phone) {
            const errors = {};

            if (!phone) {
                errors.phone = 'The phone number field is required.';
            } else if (!/^[0-9]{10}$/.test(phone)) {
                errors.phone = 'The phone number must be 10 digits.';
            }

            return errors;
        }

        function searchPatient() {
            let phone = $('#search_patient').val();
            if (phone == '') {
                alert('Please enter phone number');
                return false;
            }
            const validationErrors = validatePhoneNumber(phone);
            if (Object.keys(validationErrors).length > 0) {
                alert(validationErrors.phone);
                return false;
            }
            $('#data-add-edit').html('');
            $('.data-add').hide();
            let datajson = {
                "phone": phone
            };
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('appointment.url') . 'listFamily'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#data-list-family').show();
                },
                success: function(data) {
                    $('#data-list-family tbody').html(data.tbody);
                    $('#data-list-family tfoot').html(data.tfoot);
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }

        function showForm(stat, parent_id, phone) {
            $('.data-add').hide();
            let redirectUrl = "<?php echo e(route('appointment.addForm')); ?>";
            setRedirectUrl(redirectUrl);
            if (stat == 0) {
                let ht_id = '#data-add-member';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'addFamily/'); ?>" + parent_id;
                setListUrl(url); // api url for show table
            } else {
                let ht_id = '#data-add-patient';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'create'); ?>" + "?parent_id=" + parent_id + "&phone=" + phone;
                setListUrl(url); // api url for show table
            }
            $(ht_id).show();
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }

        function addAppointment(patient_id, phone) {
            // return false;
            $('.data-add').hide();
            let redirectUrl = "<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            $(ht_id).show();
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('appointment.url') . 'edit/' . $id : config('appointment.url') . 'create'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let filter = {
                "patient_id": patient_id,
                "phone": phone
            };
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }

        function addTeleAppointment(patient_id, phone, clinic_id) {
            // return false;
            $('.data-add').hide();
            let redirectUrl = "<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            $(ht_id).show();
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('appointment.url') . 'createTeleConsultation'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let filter = {
                "patient_id": patient_id,
                "phone": phone,
                "clinic_id": clinic_id
            };
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
            // if (clinic_id != 0) {
            //     changeClinicTele(clinic_id);
            // }
        }

        function addVideoAppointment(patient_id, phone, clinic_id) {
            $('.data-add').hide();
            let redirectUrl = "<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            $(ht_id).show();
            setId(ht_id);
            let url = "<?php echo e(config('appointment.url') . 'createVideoConsultation'); ?>";
            setListUrl(url);
            let method = 'POST';
            setMethod(method);
            let filter = {
                "patient_id": patient_id,
                "phone": phone,
                "clinic_id": clinic_id
            };
            setFilter(filter);
            getForm();
        }
        // time slot
        let show_id = '';
        let filterSlot = {};

        async function changeDoctor(data) {
            show_id = data.getAttribute('data-show');
            $('#clinic_id').html('<option value="">Select</option>');
            $('#date').html('<option value="">Select</option>');
            $('#time_slot').html('<option value="">Select</option>');
            filterSlot = {
                "filter": {
                    "doctor_id": data.value
                },
                "select": "clinic_id",
                "groupby": "clinic_id",
                "collect": {
                    "name": {
                        "table": "clinics",
                        "condition": "id",
                        "jsonValue": "clinic_id",
                        "select": "clinic_name"
                    }
                }
            };
            // availableSlot();
            const selectDoctor = await availableSlot();
            if (selectDoctor) {
                if (selectDoctor.user_clinic_id) {
                    $('#clinic_id').val(selectDoctor.user_clinic_id).trigger('change.select2');
                    $('#clinic_id').parent().addClass('pe-none');
                } else if (selectDoctor.slot_count == 1) {
                    let firstValidOption = $('#clinic_id option').filter(function() {
                        return $(this).val().trim() !== '';
                    }).first().val();
                    $('#clinic_id').val(firstValidOption).trigger('change.select2');
                }
            }
        }
        async function changeClinic(data) {
            show_id = data.getAttribute('data-show');
            $('#date').html('<option value="">Select</option>');
            $('#time_slot').html('<option value="">Select</option>');
            filterSlot = {
                "filter": {
                    "doctor_id": $('#doctor_id').val(),
                    "clinic_id": data.value
                },
                "date_filter": cur_date(),
                "select": "date as name",
                "groupby": "date"
            };
            const selectClinic = await availableSlot();
            // console.log(selectClinic);

            if (selectClinic) {
                if (selectClinic.slot_count == 1) {
                    let firstValidOption = $('#date option').filter(function() {
                        return $(this).val().trim() !== '';
                    }).first().val();
                    $('#date').val(firstValidOption).trigger('change.select2');
                }
            }
        }
        // function changeClinicTele(data) {
        //     show_id = 'time_slot_tele';
        //     $('#time_slot_tele').html('<option value="">Select</option>');
        //     filterSlot = {
        //         "filter": {
        //             "clinic_id": data,
        //             "date": '<?php echo e(date('Y-m-d')); ?>'
        //         },
        //         "select":"CONCAT(s_time, ' To ', e_time) AS name, id as schedule_id"
        //     };
        //     availableSlot();
        //     // console.log(show_id, data.name, data.value, filterSlot);
        // }
        async function changeDate(data) {
            show_id = data.getAttribute('data-show');
            $('#time_slot').html('<option value="">Select</option>');
            filterSlot = {
                "filter": {
                    "doctor_id": $('#doctor_id').val(),
                    "clinic_id": $('#clinic_id').val(),
                    "date": data.value
                },
                "select": "CONCAT(s_time, ' To ', e_time) AS name, id as schedule_id"
            };
            availableSlot();
            // console.log(show_id, data.name, data.value, filterSlot);
        }

        async function availableSlot(data) {
            // console.log(filter,data);
            let slot_res;
            const response = await $.ajax({
                type: "POST",
                url: "<?php echo e(config('appointment.url') . 'listSlot'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filterSlot), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#' + show_id).css("background-color", "#000000");
                    $('#' + show_id).prop('disabled', true);
                },
                success: function(data) {
                    $('#' + show_id).html(data.slots);
                    slot_res = data.list;
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    $('#' + show_id).prop('disabled', false);
                }
            });
            return slot_res;
        }

        function cur_date() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based, so add 1
            const day = String(today.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            return formattedDate;
        }
    </script>
    <?php if(null !== request('pid')): ?>
        <script>
            var pid = "<?php echo e(request('pid')); ?>";
            var phone = "<?php echo e(request('phone')); ?>";
            var ab_clinic = "<?php echo e(request('ab_clinic')); ?>";
            var status = "<?php echo e(request('status')); ?>";
            $('#search_patient').val(phone);
            searchPatient();
            if (status == 'Appointment') {
                addAppointment(pid, phone);
            } else if (status == 'Tele') {
                addTeleAppointment(pid, phone, ab_clinic);
            } else if (status == 'Video') {
                addVideoAppointment(pid, phone, ab_clinic);
            }
            const targetElement = document.getElementById("data-add-edit");
            targetElement.scrollIntoView({
                behavior: "smooth",
                block: "center"
            });
        </script>
    <?php endif; ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/add.blade.php ENDPATH**/ ?>