<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Report\Models\Payment;
use Modules\Report\Models\PaymentBillMaster;


class SampleCollection extends Model
{
    use HasFactory, SoftDeletes;
    protected $appends = ['offered_name']; #
    protected $fillable = [
        'id',
        'patient_id',
        'patient_phone',
        'test_id',
        'date_of_collection',
        'type_of_collection',
        'appointment_type',
        'clinic_id',
        'clinic_assign',
        'building_no',
        'full_address',
        'landmark',
        'city',
        'pincode',
        'phlebo_assign_status',
        'prev_assignment_history',
        'phlebo_id',
        'prescription_upload',
        'unique_bill_id',
        'unique_queue_number',
        'data_source',
        'source',
        'coupon_code',
        'coupon_status',
        'date_of_extends',
        'unique_id',
        'doctortype',
        'doctor_id',
        'doctor_name',
        'remarks',
        'latitude',
        'longitude',
        'offered_type',
        'offered_id',
        'is_homecollection',
        'hc_quantity_arearange',
        'patient_esign',
        'fully_paid',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $casts = [
        'test_id' => 'array', // Cast JSON to PHP array
    ];
    protected $dates = ['deleted_at'];
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }


    public function samplereportclinic()
{
    return $this->belongsTo(Clinic::class, 'clinic_assign', 'clinic_id');
}

    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }
    public function userDoctors(): BelongsTo
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id')->with('diagnosticDoctor:user_id,itdose_doctorid,doctor_type,speciality');
    }
    
    public function assignPhlebotomists(): HasOne
    {
        return $this->hasOne(PhleboAssignment::class, 'sample_id', 'id')->with('userPhlebotomists:id,username')->orderBy('id', 'desc');
    }
    public function assignAllPhlebotomists(): HasMany
    {
        return $this->hasMany(PhleboAssignment::class, 'sample_id', 'id');
    }
    public function diagnosticBill(): HasMany
    {
        return $this->hasMany(SampleCollectionBreakupBill::class, 'sample_collection_id', 'id');
    }
    public function diagnosticBillItem(): HasMany
    {
        return $this->hasMany(SampleCollectionBreakupBill::class, 'sample_collection_id', 'id')->whereNotNull('item_id');
    }
    public function diagnosticBillItemName(): HasMany
    {
        return $this->hasMany(SampleCollectionBreakupBill::class, 'sample_collection_id', 'id')->with('test:id,test_code,test_name,delivery_date')->whereNotNull('item_id');
    }
    public function diagnosticBillHC(): HasMany
    {
        return $this->hasMany(SampleCollectionBreakupBill::class, 'sample_collection_id', 'id')->whereNotNull('home_collection_id');
    }
    public function diagnosticBreakupItem(): HasMany
    {
        return $this->hasMany(SampleCollectionBreakupTest::class, 'sample_collection_id', 'id');
    }
    public function diagnosticBreakupReportedTest()
    {
        return $this->diagnosticBreakupItem()->whereNotNull('reportGenerated');
    }
    public function diagnosticBreakupItemTest()
    {
        return $this->diagnosticBreakupItem()
            ->select(
                'sample_collection_breakup_tests.test_code',
                'sample_collection_breakup_tests.itdose_test_status',
                'sample_collection_breakup_tests.status',
                'tests.test_name as test_name',
                'tests.sample_type as test_sample_type'
            )
            ->join('tests', 'tests.id', '=', 'sample_collection_breakup_tests.test_id');
    }
    public function diagnosticBreakupItemGroupSample()
    {
        return $this->diagnosticBreakupItem()
            ->select('tests.sample_type')
            ->join('tests', 'tests.id', '=', 'sample_collection_breakup_tests.test_id')
            ->groupBy('tests.sample_type');
    }
    public function diagnosticBreakupCollectionAt()
    {
        return $this->diagnosticBreakupItem()->select('sin_created_on')->orderByRaw("ISNULL(sin_created_on), sin_created_on ASC");
    }
    public function diagnosticBreakupTransferAt()
    {
        return $this->diagnosticBreakupItem()->select('transfer_on')->orderByRaw("ISNULL(transfer_on), transfer_on ASC");
    }
    public function diagnosticBreakupCollectionBy()
    {
        return $this->diagnosticBreakupItem()->select('users.username as sin_created_by_name')
            ->join('users', 'users.id', '=', 'sin_created_by')
            ->whereNotNull('sin_created_by')
            // ->with('sinCreatedBy:id,username')
            ->orderByRaw("ISNULL(sin_created_on), sin_created_on ASC");
    }
    public function diagnosticBreakupItemStatus()
    {
        return $this->diagnosticBreakupItem()->where('status', 1);
    }
    public function diagnosticApiLogsStatus(): HasMany
    {
        $api_link = SampleCollectionApiTemplate::where('id', 2)->value('api_link');
        return $this->hasMany(SampleCollectionApiLog::class, 'work_order_id', 'unique_id')
            ->where(['status' => 1, 'api_link' => $api_link]);
    }
    public function diagnosticApiLogsStatusCreated(): HasMany
    {
        $api_link = SampleCollectionApiTemplate::where('id', 2)->value('api_link');
        return $this->hasMany(SampleCollectionApiLog::class, 'work_order_id', 'unique_id')
            ->where(['status' => 1, 'api_link' => $api_link, 'created_by' => auth()->user()->id]);
    }
    public function sampleCollectionBreakupItem()
    {
        $depID = ['ULTRASOUND', 'X-RAY', 'CT SCAN', 'MRI', 'DENTAL', 'CARDIOLOGY'];
        $sptype = ['Slide/Block'];
        $statusarray = ['0', '1', '2'];
        return $this->diagnosticBreakupItem()->with('test')
            ->whereIn('status', $statusarray)
            ->whereHas('test', function ($query) use ($depID, $sptype) {
                $query->whereNotIn('department_id', $depID)
                    ->whereNotIn('sample_type', $sptype);
            });
    }
    public function getBarcodeSampleType()
    {
        $statusarray = ['0', '1', '2'];
        return $this->diagnosticBreakupItem()
            ->select('sample_type', 'sin_no')
            ->whereNotNull('sin_no')
            ->whereNotIn('status', $statusarray)
            ->orderBy('created_at', 'asc');
    }
    public function sampleRecollectionBreakupItem()
    {
        $depID = ['ULTRASOUND', 'X-RAY', 'CT SCAN', 'MRI', 'DENTAL', 'CARDIOLOGY'];
        $sptype = ['Slide/Block'];
        $statusarray = [0, 1, 2, 3];
        return $this->diagnosticBreakupItem()->with('test')
            ->where('sample_reject_status', 2)
            ->whereIn('status', $statusarray)
            ->whereHas('test', function ($query) use ($depID, $sptype) {
                $query->whereNotIn('department_id', $depID)
                    ->whereNotIn('sample_type', $sptype);
            });
    }
    public function diagnosticBreakupItemMergePackage()
    {
        $test = $this->diagnosticBreakupItem()->pluck('test_id')->toArray();
        $package = $this->diagnosticBreakupItem()->where('package_id', '!=', 0)->groupBy('package_id')->pluck('package_id')->toArray();
        return array_merge($test, $package);
    }
    public function diagnosticBreakupItemGroup()
    {
        $uniquePackageIds = $this->diagnosticBreakupItem()->where('package_id', '!=', 0)->pluck('package_id')->unique()->toArray();
        $queryTest = $this->diagnosticBreakupItem()
            ->select('package_id', 'report_delivery_date', 'is_urgent')
            ->where('package_id', 0);
        $query = $this->diagnosticBreakupItem()
            ->select('package_id', 'report_delivery_date', 'is_urgent')
            ->whereIn('package_id', $uniquePackageIds)
            ->groupBy('package_id', 'report_delivery_date', 'is_urgent');

        $mergedQuery = $queryTest->union($query);
        return $mergedQuery;
    }

    // public function diagnosticDueBill(): HasMany
    // {
    //     return $this->hasMany(Payment::class, 'service_id', 'id')->where('type', 'DG');
    // }

    // public function diagnosticDueBillReward(): HasMany
    // {
    //     return $this->hasMany(Payment::class, 'service_id', 'id')->where(['type'=>'DG','payment_mode'=>'reward_points']);
    // }
    public function paymentBillReward(): HasOne
    {
        return $this->hasOne(PaymentBillMaster::class, 'service_id', 'id')
            ->select('id','service_id')->withSum('payments as redeem_point_sum','redeem_points')->where('type','DG');
    }
    public function paymentBillRefund(): HasOne
    {
        return $this->hasOne(PaymentBillMaster::class, 'service_id', 'id')
            ->select('id','service_id')->withSum('payments as refund_amount_sum','amount')->where('type','DG')->where('status','Refund');
    }
    public function paymentBill(): HasOne
    {
        return $this->hasOne(PaymentBillMaster::class, 'service_id', 'id')->with('payments', 'payments.paymentDetails')->where('type', 'DG');
    }
    
    public function tests()
    {
        return $this->hasMany(SampleCollectionBreakupBill::class, 'sample_collection_id', 'id')->select('sample_collection_id', 'item_id')->whereNotNull('item_id')->with('test:id,test_name');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    // public function doctor(): BelongsTo
    // {
    //     return $this->belongsTo(Doctor::class,  'user_id', 'doctor_id');
    // }

    public function doctor(): BelongsTo
{
    return $this->belongsTo(Doctor::class, 'doctor_id', 'id');
}

    //discount type
    public function membership()
    {
        return $this->belongsTo(Membership::class, 'offered_id', 'id')->select('id', 'name');;
    }

    public function campaign()
    {
        return $this->belongsTo(CampaignMaster::class, 'offered_id', 'id')->select('id', 'campaign_name');;
    }

    public function getOfferedNameAttribute()
    {
        if ($this->offered_type == 1 && $this->membership) {
            return $this->membership->name;
        }

        if ($this->offered_type == 2 && $this->campaign) {
            return $this->campaign->campaign_name;
        }

        if ($this->offered_type == 3) {
            return 'Employee Discount';
        }


        return null;
    }


    //get details of sample collection breakup item
    public function collectedBy()
    {
        return $this->hasManyThrough(
            User::class,
            SampleCollectionBreakupTest::class,
            'sample_collection_id',
            'id',
            'id',
            'sin_created_by'
        )->select('users.username')->distinct();
    }

  /* public function refundPayments()
{
    return $this->hasManyThrough(
        Payment::class,
        PaymentBillMaster::class,
        'service_id',   
        'bill_id',      
        'id',           
        'id'           
    )->where('status', 'Refund');
}*/


}
