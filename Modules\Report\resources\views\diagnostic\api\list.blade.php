@foreach ($data['rows'] as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
         <td>{{ date('Y-m-d H:i:s', strtotime($row['created_at'])) ?? '' }}</td>
        <td>{{ $row['unique_id'] ?? '' }}</td>
        <td>{{ $row['date_of_collection'] ?? '' }}</td>
        <td>{{ $row['clinic_name'] ?? '' }}</td>
        <td>{{ date('Y-m-d H:i:s', strtotime($row['created_at'])) ?? '' }}</td>
        <td>{{ $visit_type[$row['type_of_collection']] ?? '' }}</td>
        <td>{{ $row['created_by']['username'] ?? '' }}</td>
        <td>
            <table class="table table-sm m-0">
                @foreach ($row['tests'] as $testItem)
                    @if (isset($testItem['test']['test_name']))
                        <tr>
                            <td class="word-wrap wht-space-custom w-75">
                                {{ $testItem['test']['test_name'] }}
                            </td>
                        </tr>
                    @endif
                @endforeach
            </table>
        </td>
        <td>{{ $row['patient_name'] ?? '' }}</td>
        <td>{{ Helper::ageCalculator($row['patient_birthdate'] ?? '') }}</td>
        <td>{{ $row['patient_gender'] ?? '' }}</td>
        <td>{{ $row['patient_mobile'] ?? '' }}</td>
        <td>{{ $row['doctor_name'] ?? '' }}</td>

        <td>{{ $diagnostic_docotor_type[$row['doctortype']] ?? '' }}</td>

        <td>{{ $row['user_doctors']['diagnostic_doctor']['itdose_doctorid'] ?? '' }}</td>
        <td>

            @if ($row['appointment_type'] == 1)
                {{ 'Walking' }}
            @else
                {{ 'Campaign' }}
            @endif

        </td>

        <td>{{ $row['offered_name'] ?? '' }}</td>
        <td>
            <table class="table table-sm m-0">

                @foreach ($row['collected_by'] as $rows)
                    @if (isset($rows['sin_created_by_name']))
                        <tr>
                            <td class="word-wrap wht-space-custom w-75">
                                {{ $rows['sin_created_by_name'] ?? '' }}
                            </td>
                        </tr>
                    @endif
                @endforeach

            </table>
        </td>
        <td>{{ $diagnostic_status_list[$row['status']] ?? '' }}</td>
        <td>{{ $row['bill_amount_sum'] ?? '0' }}</td>
        <td> {{ $row['discount_sum'] ?? '0' }} </td>
        <td>{{ $row['payment_bill_reward']['redeem_point_sum'] ?? 0 }}</td>
        <td>{{ $row['home_collection_sum'] ?? '0' }}</td>
        <td>{{ $row['total_amount_sum'] }}</td>
        <td>{{ $row['paid_amount_sum'] }}</td>
        <td>{{ $row['payment_bill_refund']['refund_amount_sum'] ?? 0 }}</td>
        <td>{{ $row['due_amount_sum'] }}</td>
    </tr>
@endforeach
