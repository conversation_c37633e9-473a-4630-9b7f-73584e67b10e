<?php if(count($list['patients']) > 0): ?>
    <?php $__currentLoopData = $list['patients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            // membership
            $btn_pay = 0;
            $btn_renew = 0;
            $service_id = 0;
            $dateToCheck = date('Y-m-d');
            $endDate = '';
            $category_id = '';
            // diagnostic
            $service = '';
            // appointment
            $check_membership = 0;
            $check_video_consult = 0;
            $check_limit = 0;
            $ab_clinic = 0;
        ?>
        <tr>
            <td><?php echo e($row->name); ?></td>

            <td><?php echo e($row->sex); ?></td>
            <td class="abwrap">
                <?php if(count($row->membershipRegistrations) > 0): ?>
                    <?php $__currentLoopData = $row->membershipRegistrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($row2->is_renewal == 1 && in_array($row2->status, [3, 4, 6])): ?>
                            <div>
                                <?php echo e($row2->memberships->name); ?><br>
                                <?php if($row2->status == 3): ?>
                                    <?php
                                        // membership
                                        $btn_pay++;
                                        if (strtotime($endDate) > strtotime($row2->end_date) || $endDate == '') {
                                            $endDate = $row2->end_date;
                                        }
                                        if (
                                            strtotime($dateToCheck) >=
                                                strtotime(
                                                    date('Y-m-d', strtotime('-1 month', strtotime($row2->end_date))),
                                                ) &&
                                            strtotime($dateToCheck) <= strtotime($row2->end_date)
                                        ) {
                                            $service_id = $row2->id;
                                        }
                                        if ($row2->memberships->category_id == 1) {
                                            $category_id = $row2->memberships->category_id;
                                        }
                                        // diagnostic
                                        $service = $row2->memberships->name;
                                        // appointment
                                        $check_ab_tc = DB::table('appointments')
                                            ->where('patient_id', $row2->patient_id)
                                            ->where('appointment_type', 2)
                                            ->whereBetween('date', [$row2->start_date, $row2->end_date])
                                            ->count();
                                        if ($row2->card_type == 2) {
                                            $check_membership = 1;
                                            $check_video_consult = 1;
                                        } elseif ($row2->card_type == 1) {
                                            $ab_clinic = $row2->clinic_id;
                                            if ($check_ab_tc >= 2) {
                                                $check_limit = 1;
                                            } else {
                                                $check_membership = 1;
                                            }
                                            $check_video_consult = 1;
                                        }
                                    ?>
                                    Expiry Date: <?php echo e($row2->end_date); ?>

                                <?php elseif($row2->status == 4): ?>
                                    <?php
                                        $btn_renew++;
                                        $service_id = $row2->id;
                                    ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Expired</span>
                                <?php elseif($row2->status == 6): ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Upcoming</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </td>
            <td>
                <?php if(array_intersect(['view_billing'], $permissionPage)): ?>
                    <div class="d-flex flex-nowrap gap-1 mb-1">
                        
                        <?php
                            $startDate = date('Y-m-d', strtotime('-1 month', strtotime($endDate)));
                        ?>
                        <?php if(strtotime($dateToCheck) >= strtotime($startDate) && strtotime($dateToCheck) <= strtotime($endDate)): ?>
                            <a href="<?php echo e(route('billing.addForm', ['pid' => $row->id, 'phone' => $phone, 'service_id' => $service_id])); ?>"
                                class="btn btn-sm btn-primary d-flex gap-1 py-2 px-3 align-items-center">Membership
                                Renew</a>
                        <?php elseif($btn_renew > 0): ?>
                            <a href="<?php echo e(route('billing.addForm', ['pid' => $row->id, 'phone' => $phone, 'service_id' => $service_id])); ?>"
                                class="btn btn-sm btn-primary d-flex gap-1 py-2 px-3 align-items-center">Membership
                                Renew</a>
                        <?php else: ?>
                            <?php if(
                                ($category_id == 1 && $list['otc_check'] == 0) ||
                                    ($category_id != 1 && $list['otc_check'] == 1) ||
                                    ($btn_pay == 0 && $btn_renew == 0 && $list['otc_check'] == 0)): ?>
                                <a href="<?php echo e(route('billing.addForm', ['pid' => $row->id, 'phone' => $phone, 'service_id' => $service_id])); ?>"
                                    class="btn btn-sm btn-primary d-flex gap-1 py-2 px-3 align-items-center">Membership</a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                <?php if(array_intersect(['view_diagnostic'], $permissionPage)): ?>
                    <div class="d-flex flex-nowrap gap-1 mb-1">
                        
                        <a href="<?php echo e(route('diagnostic.addForm', ['pid' => $row->id, 'phone' => $phone, 'service' => $service])); ?>"
                            class="btn btn-sm btn-gray d-flex gap-1 py-2 px-2 align-items-center">Diagnostic</a>
                    </div>
                <?php endif; ?>
                <?php if(array_intersect(['view_appointment', 'view_today_appointment'], $permissionPage)): ?>
                    <div class="d-flex flex-nowrap gap-1 mb-1">
                        
                        <a href="<?php echo e(route('appointment.addForm', ['pid' => $row->id, 'phone' => $phone, 'status' => 'Appointment'])); ?>"
                            class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center">Appointment</a>
                        <?php if($check_membership == 1): ?>
                            <a href="<?php echo e(route('appointment.addForm', ['pid' => $row->id, 'phone' => $phone, 'ab_clinic' => $ab_clinic, 'status' => 'Tele'])); ?>"
                                class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center">Tele
                                Appointment</a>
                        <?php endif; ?>
                        <?php if($check_limit == 1): ?>
                            <span class="badge rounded-pill bg-dark py-2 px-3 mt-2">Limit Reached</span>
                        <?php endif; ?>
                        <?php if($check_video_consult == 1): ?>
                            <a href="<?php echo e(route('appointment.addForm', ['pid' => $row->id, 'phone' => $phone, 'ab_clinic' => $ab_clinic, 'status' => 'Video'])); ?>"
                                class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center">Video
                                Appointment</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                <?php if(array_intersect(['view_pharmacy_order'], $permissionPage)): ?>
                    <div class="d-flex flex-nowrap gap-1 mb-1">
                        
                        <a href="<?php echo e(route('pharmacy.order.addForm', ['pid' => $row->id, 'phone' => $phone, 'service' => $service])); ?>"
                            class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center">Order
                            Medicine</a>
                    </div>
                <?php endif; ?>
            </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <tr>
        <td colspan="5" class="border-0">
            No patients found
        </td>
    </tr>
<?php endif; ?>


<?php /**PATH C:\wamp64\www\mymd-care\Modules/Patient\resources/views/patientSearch/api/listFamily.blade.php ENDPATH**/ ?>