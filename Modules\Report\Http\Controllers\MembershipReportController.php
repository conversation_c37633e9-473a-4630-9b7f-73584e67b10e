<?php

namespace Modules\Report\Http\Controllers;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\MembershipService;
use Illuminate\Support\Str;
use App\Exports\MembershipReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class MembershipReportController extends Controller
{
    private $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }
    public function index(Request $request)
    {
        $data = [
            'status_list' => config('report.membership_status'),
            'clinic_list' => $this->membershipService->allClinics()->toArray(),
            'memberlist_list' => $this->membershipService->allMembers()->toArray(),
        ];
        return view('report::membership.index', compact('data'));
    }
    public function membership(Request $request)
    {
        try {

            $request['with'] = [
                "patients" => "id,name,sex,birthdate",
                "memberships" => "id,name",
                "clinics" => "id,clinic_name",
                "createdBy" => "id,username",
                "paymentBill" => "id,service_id,total_amount"
            ];
            $this->membershipService->setRequest($request);
            $this->membershipService->findAll();
            $this->response['success']  = true;
            $data = $this->membershipService->collectionRows();
            $statusLabels = config('report.membership_status');
            $walkin_count = $this->membershipService->dataSourcecount('Walkin');
            $Agent_count = $this->membershipService->dataSourcecount('Agent');
            $WalkInPharmacy_count = $this->membershipService->dataSourcecount('WalkIn-Pharmacy');
            $CCE_count = $this->membershipService->dataSourcecount('CCE');
            $CCE_Membership_count = $this->membershipService->dataSourcecount('CCE_Membership');
            $this->response['customFunc'] = [
                'walkin' => $walkin_count,
                'Agent_count' => $Agent_count,
                'WalkInPharmacy_count' => $WalkInPharmacy_count,
                'CCE_count' => $CCE_count,
                'CCE_Membership_count' => $CCE_Membership_count,
            ];

            $this->response['tbody'] = view('report::membership.api.list', compact('data', 'statusLabels'))->render();
            $this->response['tfoot'] = $this->membershipService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function exportMembership(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.membership.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function exportMembershipLink()
    {
        $request = request('req');
        $request['with'] = [
            "patients" => "id,name,sex,birthdate",
            "memberships" => "id,name",
            "clinics" => "id,clinic_name",
            "createdBy" => "id,username",
            "paymentBill" => "id,service_id,total_amount"
        ];
        $request['pagination'] = [];
        $this->membershipService->setRequest($request);
        $data = $this->membershipService->findAll()->entity->get()->toArray();
        $statusLabels = config('report.membership_status');
        $data = Excel::download(new MembershipReportExport($data, $statusLabels), 'Membership-Report.xlsx');
        return $data;
    }
}
