<?php $__env->startSection('title'); ?>
    <?php echo e(config('report.title', 'OPD Appointment Report')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="conatiner-fluid content-inner p-3">
        <div class="row">

            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body   justify-content-between align-items-center">


                        <div class="header-title mb-3">
                            <h4 class="card-title mb-3 mb-md-0">OPD Appointment Report</h4>
                        </div>

                        <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                            <div class="d-flex flex-lg-nowrap flex-wrap gap-2 ">
                                <div class="form-group mb-0 w-100">
                                    <label class="mb-1 d-flex gap-2 align-items-center">
                                        <span>Show</span>
                                        <select id="perPageCount" class="form-select form-select-sm px-1">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>entries</span>
                                    </label>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select
                                        class="select2-basic-single js-states form-select form-select-sm pe-5 search-change"
                                        style="min-width:190px;" id="data_source" name="data_source" style="width: 100%;">
                                        <option value="">Select Data Source</option>

                                        <option value="1">Walkin OPD</option>
                                        <option value="2">Website</option>
                                        <option value="3">Mobile application</option>
                                        <option value="4">Request appointment page</option>
                                        <option value="5">International Womens Day</option>
                                        <option value="6">Campaign page (Rs. 10)</option>
                                        <option value="7">Tele-Consultation</option>
                                        <option value="8">Womens Joint Pain Screening</option>
                                        <option value="9">AnnualHealthCheckUp</option>

                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select name="clinic_id" class="select2-multpl-custom1 form-select search-change"
                                        data-style="py-0" style="width: 100%; min-width:200px;">
                                        <option value="">Filter By Clinic</option>
                                        <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="form-group mb-0 w-100">
                                    <input type="text" style="min-width:270px;" placeholder="Please select a date range"
                                        id="date_range" name="date"
                                        class="form-control form-control-sm flatpickr-input active search-date-range"
                                        readonly="readonly">
                                </div>
                            </div>
                            <input type="search" class="form-control form-control-sm search_change" value=""
                                placeholder="Search by Name or Phone No" data-index="0,1">
                            <button class="btn btn-sm btn-primary d-inline-flex align-items-center justify-content-center"
                                type="button" onclick="searchNamePhone()">Search</button>
                            <a href=""><button type="button" class="btn btn-sm btn-primary">Reset</button></a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV(this)">Export</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="collapse row" id="collapseExample">
            <div class="col-lg-7">
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between">
                        <div class="header-title">
                            <h5 class="h5">Activity Report</h5>
                        </div>
                    </div>
                    <div class="card-body py-0 text-center">
                        <div id="line_apexcharts"></div>
                        <h6 class="mb-3"><small>

                            </small></h6>
                    </div>
                </div>
            </div>
        </div>

        <!------Report box ends here-------->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="Table-custom-padding1 table-responsive ">

                        <table id="data-list" class="table table-sm table-sm0 table-striped table-hover w-100 dataTable "
                            data-page-length='25'>

                            <thead>
                                <tr>
                                    <th class="word-wrap wht-space-custom">ID</th>
                                    <th class="word-wrap wht-space-custom">Created At</th>
                                    <th class="word-wrap wht-space-custom">Patient</th>
                                    <th class="word-wrap wht-space-custom">Patient Phone</th>
                                    <th class="word-wrap wht-space-custom">Membership</th>
                                    <th class="word-wrap wht-space-custom">Doctor</th>
                                    <th class="word-wrap wht-space-custom">Apt Date</th>
                                    <th class="word-wrap wht-space-custom">Apt Time</th>
                                    <th class="word-wrap wht-space-custom">Clinic Name</th>
                                    <th class="word-wrap wht-space-custom">Status</th>
                                    <th class="word-wrap wht-space-custom">Data Source</th>
                                    <th class="word-wrap wht-space-custom">Executive Name</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php echo $__env->make('admin.custom.loading', ['td' => 13, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tbody>
                            <tfoot>
                                <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre loading data ends here -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $("#date_range").flatpickr({
            mode: "range",
              maxDate: "today"
        });
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id

            let url = "<?php echo e(config('report.url') . 'opdappointment'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "patients.name",
                "patients.phone",
                //  "registration_no"
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
               

                "filter": {
                    /* "date": {
                        "type": "eq",
                        "value": "<?php echo e(date('Y-m-d')); ?>"
                      // "value" : "<?php echo e(date('Y-m-d', strtotime('-30 day'))); ?>"
                    }*/
                },
              
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });


        function searchNamePhone() {
            let searchBy = $('.search_change').attr('data-index');
            let value = $('.search_change').val();
            let arraycollumn = searchBy.split(',').map(item => sortCollumns[Number(item)]);
            filter["filtermulti"] = {};
            filter["filtermulti"][value] = {
                "type": "like",
                "fields": arraycollumn
            };
            getList();
        }

      

         function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('report.url') . 'opdappointmentexport'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/opdappointment/index.blade.php ENDPATH**/ ?>