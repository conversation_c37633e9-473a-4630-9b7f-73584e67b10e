@extends('admin.layouts.app')
@section('title')
    {{ config('settings.title_review_survey', 'Review Survey') }}
@endsection
@section('meta')
@endsection
@section('style')
    <link href="https://unpkg.com/survey-core@2.0.0/survey-core.min.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="https://unpkg.com/survey-core@2.0.0/survey.core.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/survey-js-ui@2.0.0/survey-js-ui.min.js"></script>
    <style>
        .svc-creator__banner {
            display: none !important;
        }
    </style>
@endsection
@section('content')
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">{{ $id ? 'Update' : 'Add' }} Review Survey</h4>
                </div>
                <div id="formContainer" class="d-flex gap-2 align-items-center">
                    <label for="languageSelector" style="white-space: pre;">Select Language:</label>
                    <select id="languageSelector" name="language" class="form-select form-select-sm" required>
                        <option value="en">English</option>
                        <option value="hi">Hindi</option>
                        <option value="bn">Bengali</option>
                    </select>
                </div>
            </div>
            <div class="card-body placeholder-glow" id="data-add-edit">
                {{-- @include('admin.custom.loadingForm', ['fields' => [4, 4]]) --}}
            </div>
        </div>
    </div>
@endsection
@section('modal')
@endsection
@push('scripts')
    <link href="https://unpkg.com/survey-creator-core@2.0.0/survey-creator-core.min.css" type="text/css" rel="stylesheet">
    <script src="https://unpkg.com/survey-creator-core@2.0.0/survey-creator-core.min.js"></script>
    <script src="https://unpkg.com/survey-creator-js@2.0.0/survey-creator-js.min.js"></script>
    <script nonce="dynamicNonce">
        let surveyDataJson;
        let creator;

        function survey(dataJson) {            
            surveyDataJson = dataJson;
            const creatorOptions = {
                showLogicTab: true,
                isAutoSave: true
            };
            creator = new SurveyCreator.SurveyCreator(creatorOptions);
            creator.render(document.getElementById("surveyCreator"));
            creator.text = surveyDataJson;
            console.log(surveyDataJson, 'surveyDataJson', creator);
            
        }

        function submitSurvey() {
            const languageSelector = document.getElementById("languageSelector");
            const language = languageSelector.value;
            const surveyJson = creator.text;
            const surveyData = JSON.parse(surveyJson);
            const hasQuestions = surveyData.pages && surveyData.pages.length > 0 && surveyData.pages[0]
                .elements && surveyData.pages[0].elements.length > 0;
            if (!hasQuestions) {
                swal("", "Please add at least one question to the survey.", "error");
                return;
            }

            if (!surveyData.hasOwnProperty('title') || surveyData.title.trim() === "") {
                swal("", "Please insert a survey title.", "error");
                return;
            }
            $('#language').val(language);
            $('#survey_title').val(surveyData.title);
            $('#survey_settings_json').val(surveyJson);
            $('#submitForm').submit();
        }
        // function survey(dataJson) {
        //     const surveyDataJson = dataJson;

        //     const creatorOptions = {
        //         showLogicTab: true,
        //         isAutoSave: true
        //     };

        //     creator = new SurveyCreator.SurveyCreator(creatorOptions);

        //     // document.addEventListener("DOMContentLoaded", function() {
        //         // Render the Survey Creator
        //         creator.render(document.getElementById("surveyCreator"));
        //         // console.log(surveyDataJson, 'surveyDataJson', creator);

        //         // Pre-load the survey JSON into the editor
        //         creator.text = surveyDataJson;

        //         const languageSelector = document.getElementById("languageSelector");
        //         const submitBtn = document.getElementById("submitBtn");

        //         submitBtn.addEventListener("click", function() {
        //             const language = languageSelector.value;
        //             const surveyJson = creator.text;

        //             // Validation: Ensure at least one question is added
        //             const surveyData = JSON.parse(surveyJson);
        //             const hasQuestions = surveyData.pages && surveyData.pages.length > 0 && surveyData.pages[0]
        //                 .elements && surveyData.pages[0].elements.length > 0;

        //             if (!hasQuestions) {
        //                 swal("", "Please add at least one question to the survey.", "error");
        //                 // alert("Please add at least one question to the survey.");
        //                 return;
        //             }

        //             if (!surveyData.hasOwnProperty('title') || surveyData.title.trim() === "") {

        //                 swal("", "Please insert a survey title.", "error");
        //                 // alert("Please insert a survey title.");
        //                 return;
        //             }

        //             // Prepare the data to send
        //             const surveyDataToSave = {
        //                 survey_title: surveyData.title, // Extracting title from survey JSON
        //                 language: language,
        //                 survey_settings_json: surveyJson
        //             };
        //             console.log(surveyDataToSave, 'surveyDataToSave');

        //             // AJAX request using Fetch API
        //             // fetch("{{ config('settings.url_review_survey') . 'add' }}", {
        //             //     method: "POST",
        //             //     headers: {
        //             //         "Content-Type": "application/json",
        //             //     },
        //             //     body: JSON.stringify(surveyDataToSave),
        //             // })
        //             // .then(response => response.json())
        //             // .then(data => {
        //             //     if (data.success) {

        //             //         creator.JSON = {};

        //             //         swal("", "Survey saved successfully!", "success");
        //             //         // alert("Survey saved successfully!");
        //             //         //console.log("Server Response:", data);
        //             //     } else {
        //             //         swal("", "Failed to save survey. Please try again.", "error");
        //             //         // alert("Failed to save survey. Please try again.");
        //             //         console.error("Server Error:", data);
        //             //     }
        //             // })
        //             // .catch(error => {

        //             //     swal("", "An error occurred while saving the survey", "error");
        //             //     // alert("An error occurred while saving the survey.");
        //             //     console.error("Fetch Error:", error);
        //             // });
        //         });
        //     // });
        // }
    </script>
    <script>
        $(document).ready(function() {
            let redirectUrl = "{{ route('reviewSurvey.index') }}";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url =
                "{{ $id ? config('settings.url_review_survey') . 'edit/' . $id : config('settings.url_review_survey') . 'create' }}";
            setListUrl(url); // api url for show table
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
    </script>
@endpush
