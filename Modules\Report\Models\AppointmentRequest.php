<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AppointmentRequest extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'patient_id',
        'patient_phone',
        'doctor_id',
        'clinic_id',
        'date',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];

    public function clinics(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }
    public function patients(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }
    public function patientWithMembership(): BelongsTo
    {
        return $this->patients()->with('membershipRegistrationOpd:id,patient_id,card_type,registration_no,end_date');
    }
    public function userDoctors(): BelongsTo
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
