<?php

namespace Modules\Report\Models;

use App\Models\User as AuthUser;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends AuthUser
{
 public function scheduleDoctors(): HasOne
    {
        return $this->hasOne(Doctor::class, 'user_id', 'id')->with('doctorType:id,title','specialitys:id,speciality');
    }
    public function diagnosticDoctor(): HasOne
    {
        return $this->hasOne(Doctor::class, 'user_id', 'id');
    }
}
