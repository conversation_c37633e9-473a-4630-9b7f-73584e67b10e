<?php $__env->startSection('title'); ?>
    <?php echo e(config('prescription.title', 'Prescription')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body placeholder-glow" id="data-add-edit">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title placeholder"></h4>
                    </div>
                </div>
                <?php echo $__env->make('prescription::prescription.loading', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal " tabindex="-1" id="previousPrescriptionModal">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header  py-2">
                    <h5 class=" fw-bold fs-6">Previous Prescriptions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="Table-custom-padding1 table-responsive">
                        <table id="previousPrescriptionData" class="table table-sm datatable_desc placeholder-glow"
                            data-toggle="data-table">
                            <thead>
                                <tr>
                                    <th>
                                        SL No
                                    </th>
                                    <th>
                                        Date
                                    </th>
                                    <th>
                                        Chief Complaints
                                    </th>
                                    <th>
                                        Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php echo $__env->make('admin.custom.loading', ['td' => 4, 'action' => 1], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tbody>
                            <tfoot>
                                <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer py-1">
                    <button type="button" class="btn btn-sm btn-gray text-white" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        // previewPrescription
        function previewPrescription() {
            var appo_id = "<?php echo e($id); ?>";
            var form = $('#submitForm').closest('form');
            var url = "<?php echo e(route('prescription.previewPrescription')); ?>" + '?appo_id=' + appo_id + '&' + form.serialize();
            window.open(url, '_blank');
        }
        let treatment_loading = document.getElementById('treatment_loading').innerHTML;
        let dataBodyLoading = document.getElementById('data-add-edit').innerHTML;
        let filterPrescription = {};
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('prescription.url') . 'createPrescription/' . $id : ''); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            filterPrescription = {
                "prescription_id": "<?php echo e($prescription_id ? $prescription_id : ''); ?>",
                "pp": "<?php echo e(request('pp')); ?>",
            };
            setFilter(filterPrescription); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function searchPreviousPrescription(id) {
            document.getElementById('data-add-edit').innerHTML = dataBodyLoading;
            $('#previousPrescriptionModal').modal('hide');
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('prescription.url') . 'createPrescription/' . $id : ''); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            filterPrescription['pp'] = id;
            setFilter(filterPrescription); // set filter [where, pagination, sort]
            getForm();
        }

        function previousPrescriptionList() {
            let ht_id = '#previousPrescriptionData';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('prescription.url') . 'listPreviousPrescription/' . $id : ''); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let prescription_id = $("#prescription_id").val();
            let filter = {
                "filter": {
                    "id": {
                        "type": "noteq",
                        "value": prescription_id,
                    }
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": perPage,
                    "offset": 0
                },
                "sort": {
                    "date": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList();
        }

        function bmiCalculate() {
            const weight = parseFloat(document.getElementById('weight').value);
            const height = parseFloat(document.getElementById('height').value);

            if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
                document.getElementById('bmi').value = 0;
                return;
            }

            // Calculate BMI
            const bmi = (weight / (height * height)) * 10000;
            document.getElementById('bmi').value = bmi.toFixed(2);
        }

        function medicineSearch(value) {
            let id = value.split("||")[0];
            let name = value.split("||")[1];
            let rand = id;
            htmlMedicine(rand, id, name);
        }

        function addMoreMedicine() {
            let id = '';
            let name = '';
            let rand = getRandomInt(1000, 9999);
            htmlMedicine(rand, id, name);
        }

        function htmlMedicine(rand, id, name) {
            let ht_data = '<section class="row" id="row-' + rand + '">';
            ht_data += '<div class="form-group col-md-4  mb-2">';
            ht_data += '<div class="col-md-12">';
            ht_data += '<label class="form-label h8 text-gray d-block d-md-none"> Medicine</label>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-12">';
            ht_data += '<input type="hidden" name="medicine_ids[]" value="' + id + '">';
            ht_data += '<input type="text" name="medicines[]" value="' + name +
                '" class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0 w-100" placeholder="Medicine Name" required="">';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="form-group col-md-1 col-4 ps-md-0  pe-md-0">';
            ht_data += '<div class="col-md-12">';
            ht_data += '<label class="form-label h8 text-gray d-block d-md-none">Frequency</label>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-12">';
            ht_data +=
                '<select name="frequencys[]" class="form-select border-0 border-lightGray h8 border-bottom bg-transparent px-1 py-1 fs-8 rounded-0 w-100">';
            ht_data += '<option selected="" disabled="">Select</option>';
            ht_data += '<option value="1+1+1">1+1+1</option>';
            ht_data += '<option value="1+1+0">1+1+0</option>';
            ht_data += '<option value="0+1+1">0+1+1</option>';
            ht_data += '<option value="1+0+1">1+0+1</option>';
            ht_data += '<option value="1+0+0">1+0+0</option>';
            ht_data += '<option value="0+0+1">0+0+1</option>';
            ht_data += '<option value="0+1+0">0+1+0</option>';
            ht_data += '</select>';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="form-group col-md-1 col-3  pe-md-0 ps-0 ps-md-3">';
            ht_data += '<div class="col-md-12">';
            ht_data += '<label class="form-label h8 text-gray d-block d-md-none">Days</label>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-12">';
            ht_data +=
                '<input type="number" name="day_intervals[]" class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0" value="" min="1" style="width:100%;" data-style="py-0">';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="form-group col-md-2 col-5 ps-0 ps-md-3">';
            ht_data += '<div class="col-md-12">';
            ht_data += '<label class="form-label h8 text-gray d-block d-md-none">Instruction</label>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-12">';
            ht_data +=
                '<select name="instructions[]" class="form-select form-select-sm border-0 border-lightGray h8 border-bottom bg-transparent px-1 py-1 fs-8 rounded-0 w-100">';
            ht_data += '<option selected="" disabled="">Select</option>';
            ht_data += '<option value="After Meal">After Meal</option>';
            ht_data += '<option value="Before Meal">Before Meal</option>';
            ht_data += '<option value="After Breakfast">After Breakfast</option>';
            ht_data += '<option value="Before Breakfast">Before Breakfast</option>';
            ht_data += '<option value="After Lunch">After Lunch</option>';
            ht_data += '<option value="Before Lunch">Before Lunch</option>';
            ht_data += '<option value="After Dinner">After Dinner</option>';
            ht_data += '<option value="Before Dinner">Before Dinner</option>';
            ht_data += '<option value="In Empty Stomach">In Empty Stomach</option>';
            ht_data += '<option value="Not Applicable">Not Applicable</option>';
            ht_data += '</select>';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="form-group col-md-3 ps-md-0  pe-md-0 mb-0">';
            ht_data += '<div class="col-md-12">';
            ht_data += '<label class="form-label h8 text-gray d-block d-md-none">Notes</label>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-12">';
            ht_data +=
                '<textarea rows="1" name="note_lists[]" class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0" placeholder="Notes" style="width:100%;" data-style="py-0"></textarea>';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="del col-md-1 align-items-center justify-content-end d-flex">';
            ht_data += '<button onclick="removeMedicine(' + rand +
                ')" type="button" class="btn btn-white icon-16 p-0 rounded-circle d-flex align-items-center justify-content-center mt-2 pt-1" style="">';
            ht_data += '<svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">';
            ht_data +=
                '<path opacity="0.4" d="M19.643 9.48851C19.643 9.5565 19.11 16.2973 18.8056 19.1342C18.615 20.8751 17.4927 21.9311 15.8092 21.9611C14.5157 21.9901 13.2494 22.0001 12.0036 22.0001C10.6809 22.0001 9.38741 21.9901 8.13185 21.9611C6.50477 21.9221 5.38147 20.8451 5.20057 19.1342C4.88741 16.2873 4.36418 9.5565 4.35445 9.48851C4.34473 9.28351 4.41086 9.08852 4.54507 8.93053C4.67734 8.78453 4.86796 8.69653 5.06831 8.69653H18.9388C19.1382 8.69653 19.3191 8.78453 19.4621 8.93053C19.5953 9.08852 19.6624 9.28351 19.643 9.48851Z" fill="currentColor"></path>';
            ht_data +=
                '<path d="M21 5.97686C21 5.56588 20.6761 5.24389 20.2871 5.24389H17.3714C16.7781 5.24389 16.2627 4.8219 16.1304 4.22692L15.967 3.49795C15.7385 2.61698 14.9498 2 14.0647 2H9.93624C9.0415 2 8.26054 2.61698 8.02323 3.54595L7.87054 4.22792C7.7373 4.8219 7.22185 5.24389 6.62957 5.24389H3.71385C3.32386 5.24389 3 5.56588 3 5.97686V6.35685C3 6.75783 3.32386 7.08982 3.71385 7.08982H20.2871C20.6761 7.08982 21 6.75783 21 6.35685V5.97686Z" fill="currentColor"></path>';
            ht_data += '</svg>';
            ht_data += '</button>';
            ht_data += '</div>';
            ht_data += '</section>';
            $('#medicine_box').append(ht_data);
            // console.log(rand,id,name,ht_data);
        }

        function removeMedicine(id) {
            $('#row-' + id).remove();
        }

        function nextFollowDate(val) {
            var today = new Date();
            if (!isNaN(val)) {
                today.setDate(today.getDate() + parseInt(val));
                val = formatDate(today);
            } else {
                var monthsToAdd = parseInt(val.replace('M', ''));
                today.setMonth(today.getMonth() + monthsToAdd);
                val = formatDate(today);
            }
            document.getElementById("next_followup_date").value = val;
        }

        function templateUsed(template_id, el) {
            $('.template_used').removeClass('text-primary');
            $(el).addClass('text-primary');
            // console.log(template_id, el);
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('prescription.url') . 'getTemplate'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "template_id": template_id
                }), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    document.getElementById('treatment_loading').innerHTML = treatment_loading;
                },
                success: function(data) {
                    // console.log(data);
                    document.getElementById('treatment_loading').innerHTML = data.form;
                    $('.select2-multpl-custom1').select2();
                    $('#cancel-templt-btn').show();
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                }
            });
        }

        function clearTemplate() {
            console.log('clear');
            $('#medicine_box').html('');
            $('#test_ids').val(null).trigger('change');
            $('#advice').html('');
            $('#cancel-templt-btn').hide();
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Prescription\resources/views/prescription/addPrescription.blade.php ENDPATH**/ ?>