<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Settings\Http\Requests\ReviewSurveyRequest;
use Modules\Settings\Services\ReviewSurveyService;
use Modules\Settings\Services\ReviewSurveyResultService;
use Modules\Settings\Services\ReviewSurveyResultChildService;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use App\Services\ExportCSVService;

class ReviewSurveyController extends Controller
{
    private $reviewSurveyService;
    private $reviewSurveyResultService;
    private $reviewSurveyResultChildService;

    public function __construct(ReviewSurveyService $reviewSurveyService,ReviewSurveyResultService $reviewSurveyResultService,ReviewSurveyResultChildService $reviewSurveyResultChildService)
    {
        $this->reviewSurveyService = $reviewSurveyService;
        $this->reviewSurveyResultService = $reviewSurveyResultService;
        $this->reviewSurveyResultChildService = $reviewSurveyResultChildService;
    }
    public function index(Request $request)
    {
        return view('settings::reviewSurvey.index');
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        return view('settings::reviewSurvey.add',compact('id'));
    }
    public function viewResponse($id,Request $request)
    {
        $id = base64_decode(base64_decode($id));
        $reviewSurvey = $this->reviewSurveyService->findById($id);
        return view('settings::reviewSurvey.viewResponse',compact('id','reviewSurvey'));
    }
    public function list(Request $request)
    {
        try {
            $this->reviewSurveyService->setRequest($request);
            $this->reviewSurveyService->findAll();
            $this->response['success']  = true;
            $data = $this->reviewSurveyService->getRows();
            $permissionPage = $this->getPermissionList();
            $this->response['tbody'] = view('settings::reviewSurvey.api.list',compact('data','permissionPage'))->render();
            $this->response['tfoot'] = $this->reviewSurveyService->paginationCustom();
            $this->response['headerAction'] = view('settings::reviewSurvey.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function create(Request $request)
    {
        // dd(auth()->user()->can('view_today_appointment'));
        $this->response['success']  = true;
        $this->response['data']     = [];
        $id = null;
        $data = [];
        $this->response['form'] = view('settings::reviewSurvey.api.addEdit',compact('id','data'))->render();
        return response()->json($this->response);
    }

    public function add(ReviewSurveyRequest $request)
    {
        try {
            $request->validated();
            $request->merge([
                'created_by' => $this->createdBy(),
                'survey_settings_json' => json_encode(json_decode($request->survey_settings_json)),
                'url_slag' => Hash::make(Str::slug($request->survey_title))
            ]);
            // dd($request->url_slag);
            $this->reviewSurveyService->setRequest($request);
            $this->reviewSurveyService = $this->reviewSurveyService->add();
            $this->response['success']  = true;
            $this->response['message']  = 'Review Survey has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function edit($id)
     {
        try {
            $reviewSurvey = $this->reviewSurveyService->findById($id);
            if (!$reviewSurvey) {
                $this->response['success']    = false;
                $this->response['message']  = 'Review Survey not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $data = $reviewSurvey;
            // dd($data['survey_settings_json']);
            $this->response['form'] = view('settings::reviewSurvey.api.addEdit',compact('id','data'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
     }
    public function update($id,ReviewSurveyRequest $request)
    {
        try {
            $request->validated();

            $reviewSurvey = $this->reviewSurveyService->findById($id);
            if (!$reviewSurvey) {
                $this->response['success']  = false;
                $this->response['message']  = 'Review Survey not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'survey_settings_json' => json_encode(json_decode($request->survey_settings_json))
            ]);
            $this->reviewSurveyService->setRequest($request);
            $this->reviewSurveyService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Review Survey has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }

    public function detail($id)
    {
        try {
            $reviewSurvey = $this->reviewSurveyService->findById($id);
            if (!$reviewSurvey) {
                $this->response['success']    = false;
                $this->response['message']  = 'Speciality not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $data = $reviewSurvey;
            $this->response['data'] = view('settings::reviewSurvey.api.detail',compact('data'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updateStatus($id,Request $request)
    {
        try {
            $reviewSurvey = $this->reviewSurveyService->findById($id);
            if (!$reviewSurvey) {
                $this->response['success']  = false;
                $this->response['message']  = 'Review Survey not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->reviewSurveyService->setRequest($request);
            $this->reviewSurveyService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Review Survey has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updateShortLink(Request $request)
    {
        try {
            $reviewSurvey = $this->reviewSurveyService->findById($request->id);
            if (!$reviewSurvey) {
                $this->response['success']  = false;
                $this->response['message']  = 'Review Survey not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->reviewSurveyService->setRequest($request);
            $this->reviewSurveyService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Short Link has been updated successfully!';
            $this->response['data']     = [];
            $this->response['res_data'] = $reviewSurvey;
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function viewResponseList(Request $request)
    {
        try {
            $request['with'] = [
                'patient' => 'id,name,phone,parent_id',
                'reviewsSurveyMaster' => 'id,survey_title',
                'reviewsSurveyResultChild' => 'id,result_id,question_no,question,answer',
            ];
            $this->reviewSurveyResultService->setRequest($request);
            $this->reviewSurveyResultService->findAll();
            $this->response['success']  = true;
            $data = $this->reviewSurveyResultService->getRows();
            // dd($data);
            $source_type_list = config('settings.source_type_list');
            $this->response['tbody'] = view('settings::reviewSurvey.api.viewResponseList',compact('data','source_type_list'))->render();
            $this->response['tfoot'] = $this->reviewSurveyResultService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function view($url_slag,Request $request)
    {
        try {
            $source_type = $request->sTy ? $request->sTy : null;
            $source_id = $request->sId ? $request->sId : null;
            $patient_id = $request->pId ? $request->pId : null;
            $reviewSurvey = $this->reviewSurveyService->findByOtherId('url_slag',$url_slag);
            if (!$reviewSurvey) {
                $this->response['success']  = false;
                $this->response['message']  = 'Review Survey not found!';
                return "<script>
                    alert('Review Survey not found!');
                    window.close();
                </script>";
            }
            $data = [
                'reviewSurvey' => $reviewSurvey,
                'patient_id' => $patient_id,
                'source_type' => $source_type,
                'source_id' => $source_id
            ];
            // dd($data);
            return view('settings::reviewSurvey.view',compact('data'));
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function submitPatientSurvey(Request $request)
    {
        try {
            $reviewSurvey = $this->reviewSurveyService->findByOtherId('url_slag',$request->url_slag);
            if (!$reviewSurvey) {
                $this->response['success']  = false;
                $this->response['message']  = 'Review Survey not found!';
                return response()->json($this->response);
            }
            // dd($request->all(),$reviewSurvey);
            $request->merge([
                'survey_id' => $reviewSurvey->id ?? 0,
                'patient_id' => $request->patient_id ? base64_decode(base64_decode($request->patient_id)) : 0,
                'created_by' => $this->createdBy()
            ]);
            // dd($request->all(),$reviewSurvey);
            $this->reviewSurveyResultService->setRequest($request->except('survey_results'));
            $result = $this->reviewSurveyResultService->add();
            if($request->survey_results && $result){
                $survey_question_data = [];
                $survey_settings_json = json_decode($reviewSurvey->survey_settings_json)->pages;
                foreach ($survey_settings_json as $key => $row) {
                    foreach ($row->elements as $key2 => $row2) {
                        $survey_question_data[$row2->name] = $row2->title;
                    }
                }
                foreach ($request->survey_results as $key => $row) {
                    $request->merge([
                        'result_id' => $result->id,
                        'question_no' => $key,
                        'question' => $survey_question_data[$key] ?? '',
                        'answer' => $row,
                        'created_by' => $this->createdBy()
                    ]);
                    $this->reviewSurveyResultChildService->setRequest($request->except('survey_results'));
                    $this->reviewSurveyResultChildService->add();
                }
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Review Survey has been submitted successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function exportResponse(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('reviewSurvey.exportResponseLink',['req' => $request->except('pagination')]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function exportResponseLink(Request $request)
    {
        try {
            $request = request('req');
            $request['with'] = [
                'patient' => 'id,name,phone,parent_id,birthdate',
                'reviewsSurveyMaster' => 'id,survey_title,survey_settings_json',
                'reviewsSurveyResultChild' => 'id,result_id,question_no,question,answer',
            ];
            $this->reviewSurveyResultService->setRequest($request);
            $entity = $this->reviewSurveyResultService->findAll()->entity;
            $reviewMaster = $entity->first()->reviewsSurveyMaster;
            $reviewHeader = json_decode($reviewMaster->survey_settings_json)->pages;
            // dd($reviewHeader);
            $header = [
                'SL No',
                'Patient Name',
                'Service Type',
                'Service Date',
                'Contact',
                'Added On',
                // 'View Response'
            ];
            $body = [
                '{id}',
                '{patient.name}',
                // 'f@config("settings.source_type_list")[{source_type}]@',
                'f@date("Y-m-d", strtotime("{created_at}"))@ to f@config("settings.source_type_list")[{source_type}]@',
                '{source_id}',
                '{patient.name}',
                // '(f)Helper::ageCalculator({patient.birthdate})',
                // '{patient.name}',
                // '{users.schedule_doctors.doctor_type.title}',
                // '{clinics.clinic_name}',
                // '{date}',
                // '{s_time} to {e_time}',
                // '{time_in}',
                // '{time_out}'
            ];
            $row = $entity->skip(0)->take(1)->get()->toArray()[0];
            $bodyCsv = [];
            foreach ($body as $keyVal) {
                $converted = preg_replace_callback('/\{([\w\.]+)\}/', function ($matches) use ($row) {
                    try {
                        $parts = explode('.', $matches[1]);
                        $temp = $row;
                        foreach ($parts as $part) {
                            if (!isset($temp[$part])) return '';
                            $temp = $temp[$part];
                        }
                        return is_scalar($temp) ? $temp : json_encode($temp);
                    } catch (\Throwable $e) {
                        return '';
                    }
                }, $keyVal);
                // if (strpos($converted, '(f)', 0) !== false) {
                //     // dd($converted);
                //     $converted = str_replace('(f)', '', $converted);
                //     $converted = eval('return '.$converted.';') ?? '';
                // }
                if(strpos($converted, 'f@', 0) !== false){
                    dd($converted);
                    $converted = str_replace('f@', '', $converted);
                    $converted = str_replace('@', '', $converted);
                    $converted = eval('return '.$converted.';') ?? '';
                }
                array_push($bodyCsv,$converted);
            }
            dd($bodyCsv);
            $fileName = ($reviewMaster->survey_title ?? 'review-survey').'-response.csv';
            $exportCSVService = new ExportCSVService($entity,$header,$body,$fileName);
            return $exportCSVService->export();
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
}
