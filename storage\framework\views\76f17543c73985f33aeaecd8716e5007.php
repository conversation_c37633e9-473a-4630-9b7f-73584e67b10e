<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>

        <td><?php echo e($row['sample_collectionbreakup']['unique_id'] ?? ''); ?></td>

        <td><?php echo e($row['sample_collectionbreakup']['date_of_collection'] ?? ''); ?></td>


        <td><?php echo e($row['sample_collectionbreakup']['clinic']['clinic_name'] ?? ''); ?></td>

        <td><?php echo e($row['sample_collectionbreakup']['created_at'] ?? ''); ?></td>
        <td><?php echo e($visit_type[$row['sample_collectionbreakup']['type_of_collection']] ?? ''); ?></td>

        <td><?php echo e($row['test']['test_name'] ?? ''); ?></td>
       <td><?php echo e($row['test']['department_id'] ?? ''); ?></td>
       
        <td><?php echo e($row['sample_collectionbreakup']['patient']['name'] ?? ''); ?></td>
        <td><?php echo e(Helper::ageCalculator($row['sample_collectionbreakup']['patient']['birthdate'] ?? '')); ?></td>
        <td><?php echo e($row['sample_collectionbreakup']['patient']['sex'] ?? ''); ?></td>
        <td><?php echo e($row['sample_collectionbreakup']['patient']['phone'] ?? ''); ?></td>
        <td><?php echo e($row['sample_collectionbreakup']['doctor_name'] ?? ''); ?></td>

        <td><?php echo e($diagnostic_docotor_type[$row['sample_collectionbreakup']['doctortype']] ?? ''); ?></td>

        <td><?php echo e($row['user_doctors']['schedule_doctor']['itdose_doctorid'] ?? '*????'); ?></td>
       

             <td><?php echo e($row['sample_collectionbreakup']['offered_name'] ?? ''); ?></td>

       

        <td></td>
       <td><?php echo e($row['amount'] ?? '0'); ?></td>
        <td><?php echo e($row['discount'] ?? ''); ?></td>
        <td> <?php echo e($row['net_amount'] ?? ''); ?></td>
      
     

    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/diagnostic/api/InvestigatationWiseList.blade.php ENDPATH**/ ?>