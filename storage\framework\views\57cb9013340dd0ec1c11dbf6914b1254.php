<form class="clearfix" method="post"
    action="<?php echo e($id ? config('speciality.url') . 'update/' . $id : config('speciality.url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <div class="row">
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Name</label>
            <input type="text" class="form-control form-control-sm" name="speciality"
                value="<?php echo e($id ? $data['speciality'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Image Upload</label>
            <div class=" input-group input-group-sm d-flex fs-4">
                <input type="file" class="form-control form-control-sm bg-light text-gray h7" name="files"
                    value="" onchange="previewImage(this, 'previewImage')">
                <div id="previewImage" class="d-inline-flex align-items-center justify-content-center bg-light">
                     <?php if($id && $data['speciality_image'] != ''): ?>
                        <img src="<?php echo e(Storage::disk('gcs')->url($data['speciality_image'])); ?>" alt="Preview"
                            style="height: 36px;width: auto;">
                    <?php endif; ?>
                </div>
            </div>
            <?php if($id && $data['speciality_image'] != ''): ?>
                <span class="h9"><?php echo e(basename($data['speciality_image'])); ?></span>
            <?php endif; ?>
        </div>
        <div class="form-group col-md-12">
            <button type="submit" name="submit"
                class="btn btn-primary text-white"><?php echo e($id ? 'Update' : 'Submit'); ?></button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Speciality\resources/views/speciality/api/addEdit.blade.php ENDPATH**/ ?>