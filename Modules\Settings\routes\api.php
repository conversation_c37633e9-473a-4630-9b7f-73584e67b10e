<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Settings\Http\Controllers\SettingsController;
use Modules\Settings\Http\Controllers\UploadCsquareController;
use Modules\Settings\Http\Controllers\ReviewSurveyController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:api'])->prefix('uploadCsquare')->group(function () {
    Route::middleware(['permission:view_setting_csquare'])->post('/list', [UploadCsquareController::class, 'list']);
    Route::middleware(['permission:create_setting_csquare'])->get('/create', [UploadCsquareController::class, 'create']);
    Route::middleware(['permission:create_setting_csquare'])->post('/add', [UploadCsquareController::class, 'add']);
    // Route::post('/update/{id}', [UploadCsquareController::class, 'update']);
    // Route::get('/edit/{id}', [UploadCsquareController::class, 'edit']);
    // Route::get('/detail/{id}', [UploadCsquareController::class, 'detail']);
    // Route::post('/updateStatus/{id}', [UploadCsquareController::class, 'updateStatus']);
    // Route::get('/delete/{id}', [UploadCsquareController::class, 'delete']);
});

Route::middleware(['auth:api'])->prefix('reviewSurvey')->group(function () {
    Route::middleware(['permission:view_review_survey'])->post('/list', [ReviewSurveyController::class, 'list']);
    Route::middleware(['permission:create_review_survey'])->get('/create', [ReviewSurveyController::class, 'create']);
    Route::middleware(['permission:create_review_survey'])->post('/add', [ReviewSurveyController::class, 'add']);
    Route::middleware(['permission:edit_review_survey'])->post('/update/{id}', [ReviewSurveyController::class, 'update']);
    Route::middleware(['permission:edit_review_survey'])->get('/edit/{id}', [ReviewSurveyController::class, 'edit']);
    Route::middleware(['permission:info_review_survey'])->get('/detail/{id}', [ReviewSurveyController::class, 'detail']);
    Route::middleware(['permission:change_status_review_survey'])->post('/updateStatus/{id}', [ReviewSurveyController::class, 'updateStatus']);
    Route::middleware(['permission:edit_review_survey'])->post('/updateShortLink', [ReviewSurveyController::class, 'updateShortLink']);
    Route::middleware(['permission:info_review_survey'])->post('/viewResponseList', [ReviewSurveyController::class, 'viewResponseList']);
    Route::middleware(['permission:info_review_survey'])->post('/exportResponse', [ReviewSurveyController::class, 'exportResponse']);
});

Route::post('/reviewSurvey/submitPatientSurvey', [ReviewSurveyController::class, 'submitPatientSurvey']);