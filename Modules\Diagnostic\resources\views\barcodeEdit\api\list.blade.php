@foreach ($data['rows'] as $row)
    <tr>
        <td>{{ $row['id'] }}</td>
        <td>
            <a href="javascript:void(0);" onclick="findSample('{{ $row['id'] }}')"> {{ $row['unique_id'] }}</a>
        </td>
        <td>{{ $row['date_of_collection'] }}</td>
        <td>
            {{ $row['patient_name'] }}
            <br>
            {{ $row['patient_phone'] }}
        </td>
    </tr>
@endforeach
@if ($clinic_id)
    <script>
        // let user_clinic_id = "{{ $clinic_id }}";
        $('#clinic_id').val(String("{{ $clinic_id }}")).trigger('change.select2');
        $('#clinic_id').prop('disabled', true);
    </script>
@endif
