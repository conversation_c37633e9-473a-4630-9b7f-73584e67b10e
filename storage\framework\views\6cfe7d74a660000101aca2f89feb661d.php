<?php $__env->startSection('title'); ?>
    <?php echo e(config('report.title', 'Diagnostic Report(WorkorderID Wise)')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="conatiner-fluid content-inner p-3">
        <div class="row">
            <!-----Report filter starts here-------->
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body   justify-content-between align-items-center">


                        <div class="header-title mb-3">
                            <h4 class="card-title mb-3 mb-md-0">Diagnostic Report(Investigation Wise)</h4>
                        </div>

                        <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                            <div class="d-flex flex-lg-nowrap flex-wrap gap-2 ">


                                <div class="form-group mb-0 w-100">
                                    <label class="mb-1 d-flex gap-2 align-items-center">
                                        <span>Show</span>
                                        <select id="perPageCount" class="form-select form-select-sm px-1 search-change"
                                            style="min-width: 80px">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>entries</span>
                                    </label>
                                </div>



                                <div class="form-group mb-0 w-100">
                                    <select name="clinic_id" class="select2-multpl-custom1 form-select-sm search-change"
                                        data-style="py-0" style="width: 100%; min-width:200px;">
                                        <option value="">Filter By Clinic</option>
                                        <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>


                                <div class="form-group mb-0 w-100">
                                    <input type="text" name="date_of_collection" id="date_range"
                                        placeholder="Please select a date range"
                                        class="form-control form-control-sm flatpickr-input active search-date-range"
                                        readonly="readonly">
                                </div>

                                
                            </div>


                            
                            <a href=""><button type="button" class="btn btn-sm btn-primary">Reset</button></a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV(this)">Export</button>
                            <a href="<?php echo e(route('report.diagnosticreport.index')); ?>"><button type="button" class="btn btn-sm btn-primary">WorkorderID Wise</button></a>
                        </div>
                    </div>

                </div>
            </div>
            <!------Report filter ends here-------->
        </div>







        <!------Report box ends here-------->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="Table-custom-padding1 table-responsive ">

                        <table id="data-list" class="table table-sm table-sm0 table-striped table-hover w-100 dataTable "
                            data-page-length='25'>

                            <thead>
                                <tr>
                                    <th class="word-wrap wht-space-custom">SL No</th>
                                    
                                    <th class="word-wrap wht-space-custom">WorkOrder-ID</th>
                                    <th class="word-wrap wht-space-custom">Collection Date</th>
                                    <th class="word-wrap wht-space-custom">Clinic</th>
                                    <th class="word-wrap wht-space-custom">Date</th>
                                    <th class="word-wrap wht-space-custom">Visit Type</th>
                                    
                                    <th class="word-wrap wht-space-custom">Test Name</th>
                                    <th class="word-wrap wht-space-custom">Dept</th>
                                    <th class="word-wrap wht-space-custom">P-Name</th>
                                    <th class="word-wrap wht-space-custom">P-Age</th>

                                    <th class="word-wrap wht-space-custom">P-Gender</th>
                                    <th class="word-wrap wht-space-custom">P-Phone</th>
                                    <th class="word-wrap wht-space-custom">Doc Name</th>
                                    <th class="word-wrap wht-space-custom">Doc Type</th>
                                    <th class="word-wrap wht-space-custom">Doc Code</th>
                                    

                                    <th class="word-wrap wht-space-custom">Discount Type</th>
                                    <th class="word-wrap wht-space-custom">Collected BY</th>
                                    
                                    <th class="word-wrap wht-space-custom">Billed Amt</th>
                                    <th class="word-wrap wht-space-custom">Discount</th>
                                    
                                    <th class="word-wrap wht-space-custom">Net Amt</th>
                                    


                                </tr>
                            </thead>
                            <tbody>
                                <?php echo $__env->make('admin.custom.loading', ['td' => 28, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tbody>
                            <tfoot>
                                <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre loading data ends here -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });

        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id

            let url = "<?php echo e(config('report.url') . 'diagnosticinvestigationwise'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            /* let sortCollumns = [
                 "id",
                 "patients.name",
                 "phone",
                 "registration_no"
             ];
             setSortCollumns(sortCollumns);*/
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    /*  "date_of_collection": {
                        "type": "eq",
                        "value": "<?php echo e(date('Y-m-d')); ?>"
                    }*/
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 10,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });



        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('report.url') . 'diagnosticinvestigationreportexport'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/diagnostic/DiagnosticInvestigationWiseIndex.blade.php ENDPATH**/ ?>