<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Report\Models\Payment;
use Modules\Report\Models\PaymentBillMaster;

class SampleCollectionEstimation extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'patient_id',
        'full_address',
        'land_mark',
        'city',
        'pincode',
        'test_id',
        'collection_id',
        'clinic_id',
        'sub_total',
        'discount',
        'gross_total',
        'assign_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
    public function diagnosticTestSample($test_ids)
    {
        $test_list = Test::whereIn('id', explode(',',$test_ids))
            ->select('test_name','test_price')
            ->get();
        return $test_list;
    }
    public function assignPhlebotomists(): HasOne
    {
        return $this->hasOne(SampleCollectionAssignEstimation::class, 'estimation_id', 'id')->orderBy('id', 'desc');
    }
}
