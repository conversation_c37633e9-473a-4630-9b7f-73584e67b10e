<?php if(count($list['patients']) > 0): ?>
    <?php $__currentLoopData = $list['patients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td><?php echo e($row->name); ?></td>

            <td><?php echo e($row->sex); ?></td>
            <td class="abwrap">
                <?php
                    $service = '';
                ?>
                <?php if(count($row->membershipRegistrations) > 0): ?>
                    <?php $__currentLoopData = $row->membershipRegistrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($row2->is_renewal == 1 && in_array($row2->status, [3, 4, 6])): ?>
                            <div><?php echo e($row2->memberships->name); ?><br>
                                <?php if($row2->status == 3): ?>
                                    <?php
                                        $service = $row2->memberships->name;
                                    ?>
                                    Expiry Date: <?php echo e($row2->end_date); ?>

                                <?php elseif($row2->status == 4): ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Expired</span>
                                <?php elseif($row2->status == 6): ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Upcoming</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </td>
            <td>
                <div class="d-flex flex-nowrap gap-1 justify-content-center">
                    <?php if(array_intersect(['create_with_phlebo_diagnostic'], $permissionPage)): ?>
                        <button class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="selectPatient(<?php echo e($row->id); ?>,<?php echo e($phone); ?>,'<?php echo e($service); ?>','With Phlebo')">Select</button>
                    <?php else: ?>
                        <button class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="selectPatient(<?php echo e($row->id); ?>,<?php echo e($phone); ?>,'<?php echo e($service); ?>','All')">Select</button>
                    <?php endif; ?>
                    
                </div>
            </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <tr>
        <td colspan="5" class="border-0">
            No patients found
        </td>
    </tr>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/diagnostic/api/listFamily.blade.php ENDPATH**/ ?>