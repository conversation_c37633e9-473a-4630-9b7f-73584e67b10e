@extends('admin.layouts.app')
@section('title')
    {{ config('report.title', 'Membership Report') }}
@endsection
@section('meta')
@endsection
@section('style')
@endsection
@section('content')
    <div class="conatiner-fluid content-inner p-3">
        <div class="row">
            <!-----Report filter starts here-------->
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body   justify-content-between align-items-center">


                        <div class="header-title mb-3">
                            <h4 class="card-title mb-3 mb-md-0">Membership Report</h4>
                        </div>

                        <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                            <div class="d-flex flex-lg-nowrap flex-wrap gap-2 ">


                                <div class="form-group mb-0 w-100">
                                    <label class="mb-1 d-flex gap-2 align-items-center">
                                        <span>Show</span>
                                        <select id="perPageCount" class="form-select form-select-sm px-1">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>entries</span>
                                    </label>
                                </div>

                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select search-change" id="status"
                                        name="status"
                                        style="width: 100%;
                                    min-width:130px;" required>
                                        <option value="">Select a Status</option>
                                        @foreach ($data['status_list'] as $id => $label)
                                            <option value="{{ $id }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group mb-0 w-100">
                                    <select name="clinic_id" class="select2-multpl-custom1 form-select search-change"
                                        data-style="py-0" style="width: 100%; min-width:200px;">
                                        <option value="">Filter By Clinic</option>
                                        @foreach ($data['clinic_list'] as $row)
                                            <option value="{{ $row['id'] }}">{{ $row['clinic_name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select search-change" data-style="py-0"
                                        id="card_type" name="card_type" style="width: 100%; min-width:200px;">
                                        <option value="">Select Membership</option>
                                        @foreach ($data['memberlist_list'] as $row)
                                            <option value="{{ $row['id'] }}">{{ $row['name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>


                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select search-change"
                                        style="min-width:190px;" id="data_source" name="data_source" style="width: 100%;">
                                        <option value="">Select Data Source</option>
                                        <option value="CCE">CCE</option>
                                        <option value="Walkin">Walkin</option>
                                        <option value="ABcamp1123">ABcamp1123</option>
                                        <option value="Agent">Agent</option>
                                        <option value="WalkIn-Pharmacy">WalkIn-Pharmacy</option>
                                        <option value="ABcamp1123-influencer">ABcamp1123-influencer</option>

                                    </select>
                                </div>

                                <div class="form-group mb-0 w-100">
                                    <input type="text" name="date" id="date_range"
                                        placeholder="Please select a date range"
                                        class="form-control form-control-sm flatpickr-input active search-date-range"
                                        readonly="readonly">
                                </div>
                            </div>

                            <a href=""><button type="button" class="btn btn-sm btn-primary">Reset</button></a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV(this)">Export</button>

                        </div>
                    </div>

                </div>
            </div>
            <!------Report filter ends here-------->
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="row row-cols-1 row-cols-xxl-5 row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-xs-2 px-md-2 ">
                <div class="col   px-md-2 mb-3">
                    <div class="card  h-100 mb-0">
                        <div class="card-body p-1 p-lg-3 ">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="bg-soft-primary rounded p-2 p-lg-3">
                                    <svg class="icon-24" height="20" viewBox="0 0 100 100" width="20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <path
                                                d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                                fill="currentColor" />
                                            <path
                                                d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                                fill="currentColor" opacity=".4" />
                                            <circle cx="75.521" cy="70.648" r="3" fill="currentColor" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="text-end">
                                    Walkin
                                    <h2 class="counter" id="Walkin">

                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="col px-md-2 mb-3">
                    <div class="card  h-100 mb-0">
                        <div class="card-body p-1 p-lg-3 ">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="bg-soft-primary rounded p-2 p-lg-3">
                                    <svg class="icon-24" height="20" viewBox="0 0 100 100" width="20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <path
                                                d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                                                fill="currentColor" />
                                            <path
                                                d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                                fill="currentColor" opacity=".4" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="text-end">
                                    ABcamp1123
                                    <h2 class="counter" id="ABcamp1123">

                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col px-md-2 mb-3">
                    <div class="card  h-100 mb-0">
                        <div class="card-body p-1 p-lg-3 ">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="bg-soft-primary rounded p-2 p-lg-3">
                                    <svg class="icon-24" height="20" viewBox="0 0 100 100" width="20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <path
                                                d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                                                fill="currentColor" />
                                            <path
                                                d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                                fill="currentColor" opacity=".4" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="text-end">
                                    Agent
                                    <h2 class="counter" id="Agent">

                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col px-md-2 mb-3">
                    <div class="card  h-100 mb-0">
                        <div class="card-body p-1 p-lg-3 ">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="bg-soft-primary rounded p-2 p-lg-3">
                                    <svg class="icon-24" height="20" viewBox="0 0 100 100" width="20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <path
                                                d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                                fill="currentColor" />
                                            <path
                                                d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                                fill="currentColor" opacity=".4" />
                                            <circle cx="75.521" cy="70.648" r="3" fill="currentColor" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="text-end">
                                    WalkIn-Pharmacy
                                    <h2 class="counter" id="WalkIn_Pharmacy">

                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col  px-md-2 mb-3">
                    <div class="card h-100 mb-0">
                        <div class="card-body p-1 p-lg-3 ">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="bg-soft-primary rounded p-2 p-lg-3">
                                    <svg class="icon-24" height="20" viewBox="0 0 100 100" width="20"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <path
                                                d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                                                fill="currentColor" />
                                            <path
                                                d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                                fill="currentColor" opacity=".4" />
                                        </g>
                                    </svg>
                                </div>
                                <div class="text-end">
                                    ABcamp1123-influencer
                                    <h2 class="counter" id="ABcamp1123_influencer">

                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div class="collapse row" id="collapseExample">
            <div class="col-lg-7">
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between">
                        <div class="header-title">
                            <h5 class="h5">Activity Report</h5>
                        </div>
                    </div>
                    <div class="card-body py-0 text-center">
                        <div id="line_apexcharts"></div>
                        <h6 class="mb-3"><small>

                            </small></h6>
                    </div>
                </div>
            </div>
        </div>

        <!------Report box ends here-------->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="Table-custom-padding1 table-responsive ">

                        <table id="data-list" class="table table-sm table-sm0 table-striped table-hover w-100 dataTable "
                            data-page-length='25'>

                            <thead>
                                <tr>
                                    <th class="word-wrap wht-space-custom">SL No</th>
                                    <th class="word-wrap wht-space-custom">Registration Date</th>
                                    <th class="word-wrap wht-space-custom">Patient Name</th>
                                    <th class="word-wrap wht-space-custom">Patient Phone</th>
                                    <th class="word-wrap wht-space-custom">Gender</th>
                                    <th class="word-wrap wht-space-custom">Age</th>
                                    <th class="word-wrap wht-space-custom">Membership No</th>
                                    <th class="word-wrap wht-space-custom">Status</th>
                                    <th class="word-wrap wht-space-custom">Expiry Date</th>
                                    <th class="word-wrap wht-space-custom">Membership Type</th>
                                    <th class="word-wrap wht-space-custom">Total Amount</th>
                                    <th>Center</th>
                                    <th>Data Source</th>
                                    <th class="word-wrap wht-space-custom">Created By</th>
                                    <th>Designation</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                @include('admin.custom.loading', ['td' => 16, 'action' => 0])
                            </tbody>
                            <tfoot>
                                @include('admin.custom.loadingPagination')
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre loading data ends here -->
@endsection

@push('scripts')
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });

        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "{{ config('report.url') . 'membership' }}";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            /* let sortCollumns = [
                 "id",
                 "patients.name",
                 "phone",
                 "registration_no"
             ];
             setSortCollumns(sortCollumns);*/
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {

                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 10,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });

        function customFuncData(data) {
            // console.log(data.walkin,'dsacsacaca');
            $('#Walkin').text(data.walkin ?? 0);
            $('#Agent').text(data.Agent_count ?? 0);
            $('#WalkIn_Pharmacy').text(data.WalkInPharmacy_count ?? 0);
            $('#ABcamp1123').text(data.CCE_count ?? 0);
            $('#ABcamp1123_influencer').text(data.CCE_Membership_count ?? 0);
        }

        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "{{ config('report.url') . 'exportMembership' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    //console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
@endpush
