<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('reviews_survey_result_childs')) {
            Schema::create('reviews_survey_result_childs', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('result_id')->nullable();
                $table->string('question_no')->nullable();
                $table->longtext('question')->nullable();
                $table->longtext('answer')->nullable();
                $table->tinyInteger('status')->comment('0:inactive,1:active')->default('1');
                $table->unsignedMediumInteger('created_by')->nullable();
                $table->unsignedMediumInteger('modified_by')->nullable();
                $table->unsignedMediumInteger('deleted_by')->nullable();
                $table->timestamps();
                $table->timestamp('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews_survey_result_childs');
    }
};
