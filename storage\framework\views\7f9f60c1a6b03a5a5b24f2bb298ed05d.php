<?php $__env->startSection('title'); ?>
    <?php echo e(config('notification.templateSms_title', 'Template Sms')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>





<div class="col-sm-12">
    <div class="card">
        <div class="card-header d-flex justify-content-between">
            <div class="header-title">
                <h4 class="card-title"><?php echo e($id ? 'Update' : 'Add'); ?> Template SMS</h4>
            </div>
        </div>
        <div class="card-body placeholder-glow" id="data-add-edit">
            <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4,4,4,4,4,4,4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function() {
        let redirectUrl = "<?php echo e(route('notification.templateSms.index')); ?>";
        setRedirectUrl(redirectUrl);
        let ht_id = '#data-add-edit';
        setId(ht_id); // set show table id
        let url = "<?php echo e($id ? config('notification.templateSms_url') . 'edit/'.$id : config('notification.templateSms_url') . 'create'); ?>";
        setListUrl(url); // api url for show table
        let method = 'GET';
        setMethod(method);
        let filter = {};
        setFilter(filter); // set filter [where, pagination, sort]
        getForm(); // get list data with pagination and filter

        $(document).on("submit", "#submitForm", function() {
            event.preventDefault(); // Prevent the form from submitting normally
            var form = $(this).closest('form');
            setFormEntity(form);
            addUpdateForm();
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Notification\resources/views/templateSms/add.blade.php ENDPATH**/ ?>