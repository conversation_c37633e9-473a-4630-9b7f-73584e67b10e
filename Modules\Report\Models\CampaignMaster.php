<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CampaignMaster extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'campaign_name',
        'campaign_type',
        'url_slag',
        'image_path',
        'applicable_for',
        'start_date',
        'end_date',
        'is_for_coupon',
        'coupon_expeiry_date',
        'clinic',
        'medicine_type',
        'medicines',
        'medicine_discount_type',
        'medicine_discount',
        'medicine_discount_for',
        'diagnostic_individual',
        'individual_discount_type',
        'individual_discount',
        'individual_discount_for',
        'diagnostic_package',
        'package_discount_type',
        'package_discount',
        'package_discount_for',
        'self_otc_products',
        'self_otc_discount_type',
        'self_otc_discount',
        'self_otc_discount_for',
        'food_otc_products',
        'food_otc_discount_type',
        'food_otc_discount',
        'food_otc_discount_for',
        'is_hc_free',
        'min_limit',
        'status',
        'created_by',
        'created_at',
        'modified_by',
        'updated_at',
        'deleted_by',
        'deleted_at'
    ];

    protected $date = ['deleted_at'];


    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic', 'id');
    }
    public function campaignTestDetail(): HasMany
    {
        return $this->hasMany(CampaignTestDetail::class, 'campaign_id', 'id')->orderBy('id', 'asc');
    }
}
