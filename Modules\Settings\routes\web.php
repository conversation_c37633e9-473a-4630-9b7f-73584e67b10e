<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Settings\Http\Controllers\SettingsController;
use Modules\Settings\Http\Controllers\UploadCsquareController;
use Modules\Settings\Http\Controllers\ReviewSurveyController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('settings/upload-csquare')->group(function () {
    Route::get('/', [UploadCsquareController::class, 'index'])->name('settings.pharmacy_sale.index');
    Route::get('/import', [UploadCsquareController::class, 'addForm'])->name('settings.pharmacy_sale.addForm');
});

Route::prefix('review-survey')->group(function () {
    Route::get('/', [ReviewSurveyController::class, 'index'])->name('reviewSurvey.index');
    Route::get('/add', [ReviewSurveyController::class, 'addForm'])->name('reviewSurvey.addForm');
    Route::get('/view-response/{id}', [ReviewSurveyController::class, 'viewResponse'])->name('reviewSurvey.viewResponse');
});

Route::get('/reviewsandsurveyform/share/{url_slag}', [ReviewSurveyController::class, 'view'])->name('reviewSurvey.view');
Route::get('/review-survey/export-response-link', [ReviewSurveyController::class, 'exportResponseLink'])->name('reviewSurvey.exportResponseLink');

