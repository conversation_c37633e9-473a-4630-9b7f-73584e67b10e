<div class="modal-header flex-wrap gap-1  py-2">
    <h5 class=" fw-bold fs-6">
        <span id="patient_name"><?php echo e($list['patient_dtl']->name); ?></span>
        <span class="fw-normal text-gray h8" id="patient_dtl">
            (<?php echo e($list['patient_dtl']->sex); ?>/<?php echo e(Helper::ageCalculator($list['patient_dtl']->birthdate)); ?>/<?php echo e($list['patient_dtl']->bloodgroup); ?>)</span>
    </h5>
    <div class="d-flex gap-2 pe-5">
        <span class="badge rounded-pill bg-gray d-flex align-items-center"><?php echo e($list['data_source']); ?></span>
        <?php if($list['membership_name']): ?>
            <span class="badge rounded-pill bg-primary d-flex align-items-center"><?php echo e($list['membership_name']); ?></span>
        <?php endif; ?>
    </div>
</div>
<form class="clearfix" method="post" action="<?php echo e($id ? config('prescription.url') . 'updatePrescription/' . $id : ''); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" class="form-control" name="prescription_id" id="prescription_id"
        value="<?php echo e($list['prescription_id']); ?>">
    <input type="hidden" class="form-control" name="vital_id" id="vital_id" value="<?php echo e($list['vital_id']); ?>">
    <div class="row">
        <div class="col-md-8 col-lg-8 pe-3 pt-3 border-end">
            <div class="row">
                <div class="mob-sidebar col-12"></div>
                <div class="form-group col-md-12">
                    <label class="form-label h8 text-gray" for="symptom">History</label>
                    <div class="position-relative">
                        <textarea class="form-control form-control-sm bg-light h8 pe-5 ckeditor" name="symptom" id="symptom" rows="1"
                            value=""><?php echo e(!empty($list['prescriptions']) ? $list['prescriptions']->symptom : ''); ?></textarea>
                    </div>
                </div>

                <div class="form-group col-md-12">
                    <label class="form-label h8 text-gray" for="chief_complaints">Chief Complaints</label>
                    <textarea type="text" class="form-control form-control-sm bg-light h8 ckeditor" rows="1"
                        name="chief_complaints" id="chief_complaints"><?php echo e($list['chief_complaints']); ?></textarea>
                </div>

                <div class="form-group col-md-12">
                    <label class="form-label h8 text-gray" for="observation">Observations</label>
                    <textarea class="form-control form-control-sm bg-light text-dark h8" name="observation" id="observation" rows="1"
                        value=""><?php echo e(!empty($list['prescriptions']) ? $list['prescriptions']->observation : ''); ?></textarea>
                </div>

                <div class="form-group col-md-12">
                    <label class="form-label h8 text-gray" for="diagnosis_examination">Diagnosis/Examination</label>
                    <textarea class="form-control form-control-sm bg-light h8 ckeditor" name="diagnosis_examination"
                        id="diagnosis_examination" rows="1"><?php echo e(!empty($list['prescriptions']) ? $list['prescriptions']->diagnosis_examination : ''); ?></textarea>
                </div>
                <div class="form-group col-md-12">
                    <div class="d-flex flex-wrap  justify-content-between">
                        <label class="form-label fw-bold fs-7" for="altconno">Treatment Plan</label>
                        <button type="button" data-bs-toggle="modal" data-bs-target="#previousPrescriptionModal"
                            onclick="previousPrescriptionList()"
                            class="bg-white border-0 py-0 px-1 text-gray rounded-1 h7 fw-bold py-1 px-0  mt-0 ">
                            <svg width="20" viewBox="0 0 24 24" fill="#d01337" xmlns="http://www.w3.org/2000/svg">
                                <ellipse cx="10.5992" cy="10.6532" rx="8.59922" ry="8.65324" fill="#d01337">
                                </ellipse>
                                <path opacity="0.4"
                                    d="M20.6745 21.9553C20.3405 21.9445 20.0228 21.807 19.7853 21.5705L17.7488 19.1902C17.3122 18.7909 17.2765 18.1123 17.6688 17.6689C17.8524 17.4831 18.102 17.3787 18.3624 17.3787C18.6228 17.3787 18.8725 17.4831 19.0561 17.6689L21.6172 19.7181C21.9861 20.0957 22.0999 20.6563 21.9078 21.1492C21.7157 21.6422 21.2535 21.9754 20.7279 22L20.6745 21.9553Z"
                                    fill="#6c757d"></path>
                            </svg> Search Previous Prescription
                        </button>
                        <!-----------Dropzone starts here-------------->
                        <aside id="treatment_loading"
                            class="col-sm-12 col-md-12 dropzone-wrap p-2 rounded-2  border-dashed position-relative">
                            <?php echo $__env->make('prescription::prescription.api.treatmentPlan', [
                                'template_id' => $list['template_id'],
                                'list' => $list,
                                'test_childs' => $list['test_childs'],
                                'medicine_childs' => json_decode(json_encode($list['medicine_childs']), true),
                                'advice' => !empty($list['prescriptions']) ? $list['prescriptions']->advice : '',
                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </aside>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-12">
                <label class="form-label fw-bold h8 text-gray" for="next_followup_date">Next Follow-up
                    Date</label>
                <div class="col-12 col-md-12">
                    <input type="date" class="form-control form-control-sm bg-light text-dark h8"
                        id="next_followup_date" name="next_followup_date"
                        value="<?php echo e(!empty($list['prescriptions']) ? $list['prescriptions']->next_followup_date : ''); ?>">
                </div>

                <div class="form-group mt-2">
                    <label class="form-label fw-normal pe-2 h8" for="mobno">Recommendation</label>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8 "
                        onclick="nextFollowDate(this.value)" value="3">3 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="7">7 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="10">10 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="15">15 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="30">30 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="45">45 days</button>
                    <button type="button" class="chips border px-2 py-1 mb-1 bg-light rounded-2 h8"
                        onclick="nextFollowDate(this.value)" value="60">60 days</button>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-lg-4 ps-md-3 pt-md-3 pb-4 pb-md-0 prescrp-sidebar">
            <?php if($list['appointment_type'] == 3): ?>
                <div class="z-video-wrap">
                    <h6 class="mb-4 fw-bold h7"><?= $list['meeting_dtl']->topic ?></h6>
                    <!--  -->
                    <iframe id="dynamicIframe"
                        src="<?php echo e(asset('zoom/SDK/CDN/index.html?meeting_id=' . $list['cur_meeting']['id'] . '&password=' . $list['cur_meeting']['password'] . '&display_name=' . $list['cur_meeting']['name'] . '&meeting_role=' . $list['cur_meeting']['meeting_role'] . '&meeting_auto_join=' . $list['cur_meeting']['meeting_auto_join'] . '&sdk_client_id=' . $list['cur_meeting']['sdk_client_id'] . '&sdk_client_secret=' . $list['cur_meeting']['sdk_client_secret'])); ?>"
                        height="auto" width="100%"
                        style="    display: block;  height: auto !important; aspect-ratio: 3 / 3;">
                    </iframe>
                </div>
            <?php endif; ?>
            <div class="sticky-md-top">
                <h6 class="mb-1 fw-bold h7">Vitals</h6>
                <table class="table table-sm  table-responsive mb-3">
                    <tbody>
                        <tr>
                            <th scope="row" width="45%" class="border-0 py-0 ps-0 h8">Blood Pressure
                                (mmHg)
                            </th>
                            <td width="55%" class="border-0 py-0">
                                <div class="d-flex gap-1">
                                    <input type="text"
                                        class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                        style="min-height:7px;" name="blood_pressureUp" id="blood_pressureUp"
                                        value="<?php echo e(!empty($list['vitals']) ? explode('/', $list['vitals']->blood_pressure)[0] : ''); ?>">
                                    <input type="text"
                                        class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                        style="min-height:7px;" name="blood_pressureDwn" id="blood_pressureDwn"
                                        value="<?php echo e(!empty($list['vitals']) ? explode('/', $list['vitals']->blood_pressure)[1] : ''); ?>">
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row" width="12%" class="border-0 ps-0 h8">Heart Rate (bpm)</th>
                            <td class="border-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="heart_rate" id="heart_rate"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->heart_rate : ''); ?>">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row" width="12%" class="border-0 py-0 ps-0 h8">Temperature (C)
                            </th>
                            <td class="border-0 py-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0   bg-light text-dark"
                                    style="min-height:7px;" name="temperature" id="temperature"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->temperature : ''); ?>">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row" width="12%" class="border-0 ps-0 h8">SPO2 (%,Room Air)</th>
                            <td class="border-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="spo2" id="spo2"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->spo2 : ''); ?>"></td>
                        </tr>
                        <tr>
                            <th scope="row" class="border-0 py-0 ps-0 h8">Height (cm)</th>
                            <td class="border-0 py-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="height" id="height" onkeyup="bmiCalculate()"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->height : ''); ?>"></td>
                        </tr>
                        <tr>
                            <th scope="row" class="border-0 ps-0 h8">Weight (kg)</th>
                            <td class="border-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="weight" onkeyup="bmiCalculate()" id="weight"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->weight : ''); ?>"></td>
                        </tr>
                        <tr>
                            <th scope="row" class="border-0 py-0 ps-0 h8">BMI (kg/m2)</th>
                            <td class="border-0 py-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="bmi" id="bmi"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->bmi : ''); ?>" readonly>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row" width="12%" class="border-0 ps-0 h8">Blood Group</th>
                            <td class="border-0"><input type="text"
                                    class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                    style="min-height:7px;" name="blood_group" id="blood_group"
                                    value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->blood_group : ''); ?>">
                            </td>
                        </tr>
                        <?php if($list['speciality'] == 'PAEDIATRIC'): ?>
                            <tr id="head_circumference_div">
                                <th scope="row" width="12%" class="border-0 ps-0 h8">Head Circumference
                                    (cm)
                                </th>
                                <td class="border-0"><input type="text"
                                        class="form-control form-control-sm py-0 px-1 h8 rounded-0 border-0  bg-light text-dark"
                                        style="min-height:7px;" name="head_circumference" id="head_circumference"
                                        value="<?php echo e(!empty($list['vitals']) ? $list['vitals']->head_circumference : ''); ?>">
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <div class="bd-example">
                    <nav>
                        <div class="mb-3 nav nav-tabs" id="nav-module" role="tablist">
                            <div class="d-flex" id="module-box-most">
                                <button
                                    class="nav-link border-0 py-0 px-1 rounded-1 h8 fw-normal py-1 px-3  mt-0 align-items-center active"
                                    id="nav-most-tab" data-bs-toggle="tab" data-bs-target="#nav-most" type="button"
                                    role="tab" aria-controls="nav-most" aria-selected="true">Most Use</button>
                            </div>
                            <div class="d-flex" id="module-box-self">
                                <button class="nav-link border-0 py-0 px-1 rounded-1 h8 fw-normal py-1 px-3  mt-0"
                                    id="nav-self-tab" data-bs-toggle="tab" data-bs-target="#nav-self" type="button"
                                    role="tab" aria-controls="nav-self" aria-selected="true">Self</button>
                            </div>
                            <div class="d-flex" id="module-box-common">
                                <button class="nav-link border-0 py-0 px-1 rounded-1 h8 fw-normal py-1 px-3  mt-0"
                                    id="nav-common-tab" data-bs-toggle="tab" data-bs-target="#nav-common"
                                    type="button" role="tab" aria-controls="nav-common"
                                    aria-selected="true">Common</button>
                            </div>
                        </div>
                    </nav>
                    <div class="tab-content iq-tab-fade-up" id="content-pages">
                        <div class="tab-pane fade active show" id="nav-most" role="tabpanel"
                            aria-labelledby="nav-most-tab">
                            <?php $__currentLoopData = $list['template_most_used']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button type="button"
                                    class="template_used panel-title fw-bold h8 border d-block w-100 text-start border-gray rounded-1 p-1 bg-light text-gray"
                                    onclick="templateUsed('<?php echo e($row->id); ?>',this)">
                                    <?php echo e($row->template_name); ?> (<?php echo e($row->medicine_cnt); ?> Drugs)
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="tab-pane fade" id="nav-self" role="tabpanel" aria-labelledby="nav-self-tab">
                            <?php $__currentLoopData = $list['template_self']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button type="button"
                                    class="template_used panel-title fw-bold h8 border d-block w-100 text-start border-gray rounded-1 p-1 bg-light text-gray"
                                    onclick="templateUsed('<?php echo e($row->id); ?>',this)">
                                    <?php echo e($row->template_name); ?> (<?php echo e($row->medicine_cnt); ?> Drugs)
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="tab-pane fade" id="nav-common" role="tabpanel" aria-labelledby="nav-common-tab">
                            <?php $__currentLoopData = $list['template_common']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button type="button"
                                    class="template_used panel-title fw-bold h8 border d-block w-100 text-start border-gray rounded-1 p-1 bg-light text-gray"
                                    onclick="templateUsed('<?php echo e($row->id); ?>',this)">
                                    <?php echo e($row->template_name); ?> (<?php echo e($row->medicine_cnt); ?> Drugs)
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>






            </div>
        </div>
        
        <div class="form-group col-md-12">
            <button type="button" class="btn btn-gray" onclick="previewPrescription()">Preview</button>
            <a href="<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>">
                <button type="button" class="btn btn-gray">Close</button>
            </a>
            <button type="submit" name="submit"
                class="btn btn-primary text-white"><?php echo e($list['prescription_id'] ? 'Update' : 'Submit'); ?></button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>

</form>
<?php if($list['appointment_type'] == 3): ?>
    <script>
        async function checkAndRequestPermissions() {
            try {
                // Check existing permissions for camera and microphone
                const cameraPermission = await navigator.permissions.query({
                    name: 'camera'
                });
                const microphonePermission = await navigator.permissions.query({
                    name: 'microphone'
                });

                // Function to check permission state
                function isPermissionGranted(permission) {
                    return permission.state === 'granted';
                }

                function isPermissionDenied(permission) {
                    return permission.state === 'denied';
                }

                // If both permissions are granted, do nothing
                if (isPermissionGranted(cameraPermission) && isPermissionGranted(microphonePermission)) {
                    console.log("Camera and Microphone access already granted.");
                    return;
                }

                // If any permission is denied, show an alert and stop
                if (isPermissionDenied(cameraPermission) || isPermissionDenied(microphonePermission)) {
                    alert("Camera and/or Microphone access is denied. Please enable it in your browser settings.");
                    return;
                }

                // If permissions are not granted, ask the user
                alert("Camera and Microphone access is required. Click OK to grant permission.");

                // Request both permissions
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });

                if (stream) {
                    console.log("Camera and Microphone access granted.");
                }
            } catch (error) {
                console.error("Error accessing Camera/Microphone:", error);
                alert("Unable to access Camera or Microphone. Please check your settings.");
            }
        }

        // Run the function to check and request permissions
        checkAndRequestPermissions();
    </script>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Prescription\resources/views/prescription/api/prescriptionForm.blade.php ENDPATH**/ ?>