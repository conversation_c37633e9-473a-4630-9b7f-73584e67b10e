<!DOCTYPE html>
<html>

<head>
    <title>Export Data</title>
    <meta charset="utf-8">
</head>

<body>
    <table>
        <thead>
            <tr>

                <th>SL No</th>
                <th>WorkOrder-ID</th>
                <th>Visit Date & Time</th>

                <th>Collection Date</th>
                <th>Clinic</th>
                <th>Date</th>
                <th>Visit Type</th>
                <th>Created BY</th>
                <th>Test Name</th>
                <th>Dept</th>
                <th>P-Name</th>
                <th>P-Age</th>
                <th>P-Gender</th>
                <th>P-Phone</th>
                <th>Doc Name </th>
                <th>Doc Type </th>
                <th>Doc Code </th>
                <th>Discount Type</th>
                <th>Collected BY</th>
                <th>Billed Amt</th>
                <th>Discount</th>
                <th>Net Amt</th>





            </tr>
        </thead>
        <tbody>



            @if (!empty($data))
                @foreach ($data['rows'] as $row)
                    <tr>
                        <td>{{ $loop->iteration }}</td>

                        <td>{{ $row['sample_collectionbreakup']['unique_id'] ?? '' }}</td>

                        <td>{{ $row['sample_collectionbreakup']['date_of_collection'] ?? '' }}</td>


                        <td>{{ $row['sample_collectionbreakup']['clinic']['clinic_name'] ?? '' }}</td>

                        <td>{{ $row['sample_collectionbreakup']['created_at'] ?? '' }}</td>
                        <td>{{ $visit_type[$row['sample_collectionbreakup']['type_of_collection']] ?? '' }}</td>

                        <td>{{ $row['test']['test_name'] ?? '' }}</td>
                        <td>{{ $row['test']['department_id'] ?? '' }}</td>

                        <td>{{ $row['sample_collectionbreakup']['patient']['name'] ?? '' }}</td>
                        <td>{{ Helper::ageCalculator($row['sample_collectionbreakup']['patient']['birthdate'] ?? '') }}
                        </td>
                        <td>{{ $row['sample_collectionbreakup']['patient']['sex'] ?? '' }}</td>
                        <td>{{ $row['sample_collectionbreakup']['patient']['phone'] ?? '' }}</td>
                        <td>{{ $row['sample_collectionbreakup']['user_doctors']['username'] ?? '' }}</td>

                        <td>{{ $diagnostic_docotor_type[$row['sample_collectionbreakup']['doctortype']] ?? '' }}</td>

                        <td>{{ $row['user_doctors']['schedule_doctor']['itdose_doctorid'] ?? '*????' }}</td>


                        <td>{{ $row['sample_collectionbreakup']['offered_name'] ?? '' }}</td>



                        <td></td>
                        <td>{{ $row['amount'] ?? '0' }}</td>
                        <td>{{ $row['discount'] ?? '' }}</td>
                        <td> {{ $row['net_amount'] ?? '' }}</td>



                    </tr>
                @endforeach


            @endif
        </tbody>
    </table>
</body>

</html>
