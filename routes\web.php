<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Clinic\Http\Controllers\ClinicDistrictController;
use Modules\Clinic\Http\Controllers\ClinicController;
use Modules\Clinic\Http\Controllers\ClinicHolidayController;
use Modules\Speciality\Http\Controllers\SpecialityController;
use Modules\Diagnostic\Http\Controllers\DiagnosticTestController;
use Modules\Templates\Http\Controllers\TemplatesController;
use Modules\Schedule\Http\Controllers\ScheduleController;
use Modules\Pharmacy\Http\Controllers\MedicineController;
use App\Http\Controllers\MigrationController;
use App\Http\Controllers\MigrationReverseController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/config-cache', function () {
    Artisan::call('config:cache');
    return 'config-cache';
});
Route::get('/config-clear', function () {
    Artisan::call('config:clear');
    return 'config-clear';
});

Route::get('/', function () {
    return view('welcome');
});
// data mygrate another table

Route::prefix('data-migrate')->group(function () {
    Route::get('/', [MigrationController::class, 'dataMigrateIndex'])->name('dataMigrate.index');// data migrate dashboard UI 
    // indiviual master table
    Route::get('/district', [ClinicDistrictController::class, 'dataMigrate'])->name('dataMigrate.district');//district
    Route::get('/clinic', [ClinicController::class, 'dataMigrate'])->name('dataMigrate.clinic');//clinic
    Route::get('/holiday', [ClinicHolidayController::class, 'dataMigrate'])->name('dataMigrate.holiday');//holiday
    Route::get('/speciality', [SpecialityController::class, 'dataMigrate'])->name('dataMigrate.speciality');//speciality
    Route::get('/test', [DiagnosticTestController::class, 'dataMigrate'])->name('dataMigrate.test');//test
    Route::get('/prescription-template', [TemplatesController::class, 'dataMigrate'])->name('dataMigrate.prescription.template');//prescription template
    Route::get('/medicine', [MedicineController::class, 'dataMigrate'])->name('dataMigrate.medicine');//medicine
    
    // user module
    Route::get('/user', [MigrationController::class, 'dataMigrate'])->name('dataMigrate.user');// user
    Route::get('/mymd-doctor', [MigrationController::class, 'dataMigrateMymdDoctor'])->name('dataMigrate.userMymdDoctor');// user
    Route::get('/reffer-doctor', [MigrationController::class, 'dataMigrateRefferDoctor'])->name('dataMigrate.userRefferDoctor');// user
    Route::get('/other-doctor', [MigrationController::class, 'dataMigrateOtherDoctor'])->name('dataMigrate.userOtherDoctor');// user
    Route::get('/other-doctor-create-user', [MigrationController::class, 'dataMigrateOtherDoctorCreateUser'])->name('dataMigrate.dataMigrateOtherDoctorCreateUser');// user
    // membership module
    Route::get('/membership-patient', [MigrationController::class, 'dataMigratePatient'])->name('dataMigrate.membership.patient');// patient
    Route::get('/membership', [MigrationController::class, 'dataMigrateMembership'])->name('dataMigrate.membership');// membership
    Route::get('/membership-card', [MigrationController::class, 'dataMigrateMembershipCard'])->name('dataMigrate.membershipCard');// membership card
    // appoinment module
    Route::get('/appoinment-schedule', [ScheduleController::class, 'dataMigrate'])->name('dataMigrate.appoinment.schedule');//schedule
    Route::get('/appoinment', [MigrationController::class, 'dataMigrateAppoinment'])->name('dataMigrate.appoinment');//appoinment
    Route::get('/appoinment-bill', [MigrationController::class, 'dataMigrateAppoinmentBill'])->name('dataMigrate.appoinment.bill');//appoinment bill
    Route::get('/appoinment-vital', [MigrationController::class, 'dataMigrateAppoinmentVital'])->name('dataMigrate.appoinment.vital');//appoinment vital
    Route::get('/prescription', [MigrationController::class, 'dataMigrateAppoinmentPrescription'])->name('dataMigrate.appoinment.prescription');//prescription template
    // csqure
    Route::get('/csqure-brands', [MigrationController::class, 'dataMigrateCsqureBrands'])->name('dataMigrate.csqure.brands');//csqure-brands
    Route::get('/csqure-hsns', [MigrationController::class, 'dataMigrateCsqureHsns'])->name('dataMigrate.csqure.hsns');//csqure-hsns
    Route::get('/csqure-suppliers', [MigrationController::class, 'dataMigrateCsqureSuppliers'])->name('dataMigrate.csqure.suppliers');//csqure-suppliers
    Route::get('/csqure-master', [MigrationController::class, 'dataMigrateCsqureMaster'])->name('dataMigrate.csqureMaster');//csqure master
    Route::get('/csqure', [MigrationController::class, 'dataMigrateCsqure'])->name('dataMigrate.csqure');//csqure
    // order medicine
    Route::get('/order-medicine', [MigrationController::class, 'dataMigrateOrderMedicine'])->name('dataMigrate.orderMedicine');//orderMedicine
    // diagnastic
    Route::get('/diagnastic-sample-batch-generator', [MigrationController::class, 'dataMigrateDiagnasticBatchGenerator'])->name('dataMigrate.diagnastic.sampleBatchGenerator');//diagnastic.sampleBatchGenerator
    Route::get('/diagnastic-home-collection-charge', [MigrationController::class, 'dataMigrateDiagnasticHomecollectionCharge'])->name('dataMigrate.diagnastic.homecollectionCharge');//diagnastic.homecollectionCharge
    Route::get('/diagnastic-sample-type', [MigrationController::class, 'dataMigrateDiagnasticSampleType'])->name('dataMigrate.diagnastic.sampleType');//diagnastic.sampleType
    Route::get('/diagnastic', [MigrationController::class, 'dataMigrateDiagnastic'])->name('dataMigrate.diagnastic');//diagnastic
    Route::get('/diagnastic-breakup-test', [MigrationController::class, 'dataMigrateDiagnasticBreakupTest'])->name('dataMigrate.diagnastic.breakupTest');//diagnastic.breakupTest
    Route::get('/diagnastic-breakup-bill', [MigrationController::class, 'dataMigrateDiagnasticBreakupBill'])->name('dataMigrate.diagnastic.breakupBill');//diagnastic.breakupBill
    Route::get('/diagnastic-payment', [MigrationController::class, 'dataMigrateDiagnasticPayment'])->name('dataMigrate.diagnastic.payment');//diagnastic.payment
    Route::get('/diagnastic-phlebo-assignment', [MigrationController::class, 'dataMigrateDiagnasticPhleboAssignment'])->name('dataMigrate.diagnastic.phleboAssignment');//diagnastic.phleboAssignment
    Route::get('/diagnastic-api-log', [MigrationController::class, 'dataMigrateDiagnasticApiLog'])->name('dataMigrate.diagnastic.apiLog');//diagnastic.apiLog
    Route::get('/diagnastic-recollection', [MigrationController::class, 'dataMigrateDiagnasticRecollection'])->name('dataMigrate.diagnastic.recollection');//diagnastic.recollection
    Route::get('/diagnastic-breakup-test-itdose', [MigrationController::class, 'dataMigrateDiagnasticBreakupTestItdose'])->name('dataMigrate.diagnastic.breakupTestItdose');//diagnastic.breakupTestItdose
    Route::get('/diagnastic-estimation', [MigrationController::class, 'dataMigrateDiagnasticEstimation'])->name('dataMigrate.diagnastic.estimation');//diagnastic.estimation
    Route::get('/campaign', [MigrationController::class, 'dataMigrateCampaign'])->name('dataMigrate.campaign');//campaign
    Route::get('/reward-points', [MigrationController::class, 'dataMigrateRewardPoints'])->name('dataMigrate.rewardPoints');//campaign usage
});

Route::prefix('data-migrate-reverse')->group(function () {
    // Route::get('/', [MigrationController::class, 'dataMigrateIndex'])->name('dataMigrate.index');// data migrate dashboard UI 
    // indiviual master table
    Route::get('/district', [MigrationReverseController::class, 'dataMigrateDistrict'])->name('dataMigrateReverse.district');//district
    Route::get('/clinic', [MigrationReverseController::class, 'dataMigrateClinic'])->name('dataMigrateReverse.clinic');//clinic
    Route::get('/holiday', [MigrationReverseController::class, 'dataMigrateHoliday'])->name('dataMigrateReverse.holiday');//holiday
    Route::get('/speciality', [MigrationReverseController::class, 'dataMigrateSpeciality'])->name('dataMigrateReverse.speciality');//speciality
    Route::get('/test', [MigrationReverseController::class, 'dataMigrateTest'])->name('dataMigrateReverse.test');//test
    Route::get('/prescription-template', [MigrationReverseController::class, 'dataMigratePrescriptionTemplate'])->name('dataMigrateReverse.prescription.template');//prescription template
    Route::get('/medicine', [MigrationReverseController::class, 'dataMigrateMedicine'])->name('dataMigrateReverse.medicine');//medicine
    
    // // user module
    Route::get('/doctor', [MigrationReverseController::class, 'dataMigrateDoctor'])->name('dataMigrateReverse.doctor');// user
    Route::get('/cluster-manager', [MigrationReverseController::class, 'dataMigrateClusterManager'])->name('dataMigrateReverse.clusterManager');// user
    Route::get('/center-manager', [MigrationReverseController::class, 'dataMigrateCenterManager'])->name('dataMigrateReverse.centerManager');// user
    Route::get('/receptionist', [MigrationReverseController::class, 'dataMigrateReceptionist'])->name('dataMigrateReverse.receptionist');// user
    Route::get('/nurse', [MigrationReverseController::class, 'dataMigrateNurse'])->name('dataMigrateReverse.nurse');// user
    Route::get('/phlebotomist', [MigrationReverseController::class, 'dataMigratePhlebotomist'])->name('dataMigrateReverse.phlebotomist');// user
    Route::get('/pharmacist', [MigrationReverseController::class, 'dataMigratePharmacist'])->name('dataMigrateReverse.pharmacist');// user
    Route::get('/agent', [MigrationReverseController::class, 'dataMigrateAgent'])->name('dataMigrateReverse.agent');// user
    Route::get('/customer-care', [MigrationReverseController::class, 'dataMigrateCustomerCare'])->name('dataMigrateReverse.customerCare');// user
    
    Route::get('/mymd-doctor', [MigrationReverseController::class, 'dataMigrateMymdDoctor'])->name('dataMigrateReverse.userMymdDoctor');// user
    Route::get('/reffer-doctor', [MigrationReverseController::class, 'dataMigrateRefferDoctor'])->name('dataMigrateReverse.userRefferDoctor');// user
    Route::get('/other-doctor', [MigrationReverseController::class, 'dataMigrateOtherDoctor'])->name('dataMigrateReverse.userOtherDoctor');// user
    // // membership module
    Route::get('/membership-patient', [MigrationReverseController::class, 'dataMigratePatient'])->name('dataMigrateReverse.membership.patient');// patient
    Route::get('/membership', [MigrationReverseController::class, 'dataMigrateMembership'])->name('dataMigrateReverse.membership');// membership
    Route::get('/membership-card', [MigrationReverseController::class, 'dataMigrateMembershipCard'])->name('dataMigrateReverse.membershipCard');// membership card
    // // appoinment module
    Route::get('/appoinment-schedule', [MigrationReverseController::class, 'dataMigrateAppoinmentSchedule'])->name('dataMigrateReverse.appoinment.schedule');//schedule
    Route::get('/appoinment', [MigrationReverseController::class, 'dataMigrateAppoinment'])->name('dataMigrateReverse.appoinment');//appoinment
    // Route::get('/appoinment-bill', [MigrationReverseController::class, 'dataMigrateAppoinmentBill'])->name('dataMigrateReverse.appoinment.bill');//appoinment bill
    Route::get('/appoinment-vital', [MigrationReverseController::class, 'dataMigrateAppoinmentVital'])->name('dataMigrateReverse.appoinment.vital');//appoinment vital
    Route::get('/prescription', [MigrationReverseController::class, 'dataMigrateAppoinmentPrescription'])->name('dataMigrateReverse.appoinment.prescription');//prescription template
    // // csqure
    Route::get('/csqure-brands', [MigrationReverseController::class, 'dataMigrateCsqureBrands'])->name('dataMigrateReverse.csqure.brands');//csqure-brands
    Route::get('/csqure-hsns', [MigrationReverseController::class, 'dataMigrateCsqureHsns'])->name('dataMigrateReverse.csqure.hsns');//csqure-hsns
    Route::get('/csqure-suppliers', [MigrationReverseController::class, 'dataMigrateCsqureSuppliers'])->name('dataMigrateReverse.csqure.suppliers');//csqure-suppliers
    // Route::get('/csqure-master', [MigrationReverseController::class, 'dataMigrateCsqureMaster'])->name('dataMigrateReverse.csqureMaster');//csqure master
    Route::get('/csqure', [MigrationReverseController::class, 'dataMigrateCsqure'])->name('dataMigrateReverse.csqure');//csqure
    // // order medicine
    Route::get('/order-medicine', [MigrationReverseController::class, 'dataMigrateOrderMedicine'])->name('dataMigrateReverse.orderMedicine');//orderMedicine
    // // diagnastic
    Route::get('/diagnastic-sample-batch-generator', [MigrationReverseController::class, 'dataMigrateDiagnasticBatchGenerator'])->name('dataMigrateReverse.diagnastic.sampleBatchGenerator');//diagnastic.sampleBatchGenerator
    Route::get('/diagnastic-home-collection-charge', [MigrationReverseController::class, 'dataMigrateDiagnasticHomecollectionCharge'])->name('dataMigrateReverse.diagnastic.homecollectionCharge');//diagnastic.homecollectionCharge
    Route::get('/diagnastic-sample-type', [MigrationReverseController::class, 'dataMigrateDiagnasticSampleType'])->name('dataMigrateReverse.diagnastic.sampleType');//diagnastic.sampleType
    Route::get('/diagnastic', [MigrationReverseController::class, 'dataMigrateDiagnastic'])->name('dataMigrateReverse.diagnastic');//diagnastic
    Route::get('/diagnastic-breakup-test', [MigrationReverseController::class, 'dataMigrateDiagnasticBreakupTest'])->name('dataMigrateReverse.diagnastic.breakupTest');//diagnastic.breakupTest
    Route::get('/diagnastic-breakup-bill', [MigrationReverseController::class, 'dataMigrateDiagnasticBreakupBill'])->name('dataMigrateReverse.diagnastic.breakupBill');//diagnastic.breakupBill
    // Route::get('/diagnastic-payment', [MigrationReverseController::class, 'dataMigrateDiagnasticPayment'])->name('dataMigrateReverse.diagnastic.payment');//diagnastic.payment
    Route::get('/diagnastic-phlebo-assignment', [MigrationReverseController::class, 'dataMigrateDiagnasticPhleboAssignment'])->name('dataMigrateReverse.diagnastic.phleboAssignment');//diagnastic.phleboAssignment
    Route::get('/diagnastic-api-log', [MigrationReverseController::class, 'dataMigrateDiagnasticApiLog'])->name('dataMigrateReverse.diagnastic.apiLog');//diagnastic.apiLog
    Route::get('/diagnastic-recollection', [MigrationReverseController::class, 'dataMigrateDiagnasticRecollection'])->name('dataMigrateReverse.diagnastic.recollection');//diagnastic.recollection
    Route::get('/diagnastic-breakup-test-itdose', [MigrationReverseController::class, 'dataMigrateDiagnasticBreakupTestItdose'])->name('dataMigrateReverse.diagnastic.breakupTestItdose');//diagnastic.breakupTestItdose
    Route::get('/diagnastic-estimation', [MigrationReverseController::class, 'dataMigrateDiagnasticEstimation'])->name('dataMigrateReverse.diagnastic.estimation');//diagnastic.estimation
    Route::get('/campaign', [MigrationReverseController::class, 'dataMigrateCampaign'])->name('dataMigrateReverse.campaign');//campaign
    Route::get('/reward-points', [MigrationReverseController::class, 'dataMigrateRewardPoints'])->name('dataMigrateReverse.rewardPoints');//campaign usage
});
