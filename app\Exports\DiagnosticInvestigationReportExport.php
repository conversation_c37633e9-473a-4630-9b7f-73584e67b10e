<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;

class DiagnosticInvestigationReportExport implements FromView, ShouldAutoSize
{
    protected $data;
    protected $status_list;
    protected $visit_type;
    protected $diagnostic_docotor_type;
    protected $diagnostic_status_list;
    
    // Accept data via constructor
    public function __construct($data,$status_list,$visit_type,$diagnostic_docotor_type,$diagnostic_status_list)
    {
        // dd($data,'hii');
        $this->data = $data;
        $this->status_list = $status_list;
        $this->visit_type = $visit_type;
        $this->diagnostic_docotor_type = $diagnostic_docotor_type;
        $this->diagnostic_status_list = $diagnostic_status_list;
        
    }
    public function view(): View
    {
        $data = $this->data;
        $status_list = $this->status_list;
        $visit_type = $this->visit_type;
        $diagnostic_docotor_type = $this->diagnostic_docotor_type;
        $diagnostic_status_list = $this->diagnostic_status_list;


        // dd($data);
        return view('report::diagnostic.export.diagnosticInvestigationExport', compact('data','status_list', 'visit_type', 'diagnostic_docotor_type', 'diagnostic_status_list'));
    }
}
