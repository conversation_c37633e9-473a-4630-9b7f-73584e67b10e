<?php

namespace Modules\Billing\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Billing\Http\Requests\BillingRequest;
use Modules\Billing\Http\Requests\BillingCardRequest;
use Modules\Billing\Services\BillingService;
use Modules\Billing\Services\LeadGenerationService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Reward\Services\RewardService;
use Modules\Patient\Services\PatientService;
use App\Services\SMSMsgService;
use Modules\Appointment\Services\AppointmentService;
use Illuminate\Support\Str;
use Modules\Billing\Models\LeadGeneration;
use Modules\Reward\Models\VerifyOtp;
use DB;
use PDF;
use Modules\Billing\Models\MembershipRegistration;
use Carbon\Carbon;

class BillingController extends Controller
{
    private $billingService;
    private $paymentBillService;
    private $paymentService;
    private $paymentDetailService;
    private $rewardService;
    private $smsMsgService;
    private $appointmentService;
    private $patientService;
    private $leadGenerationService;
    private $otpExpire;//minutes

    public function __construct(BillingService $billingService, PaymentBillService $paymentBillService,PaymentService $paymentService, PaymentDetailService $paymentDetailService,RewardService $rewardService,SMSMsgService $smsMsgService,AppointmentService $appointmentService, PatientService $patientService, LeadGenerationService $leadGenerationService)
    {
        $this->billingService = $billingService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->rewardService = $rewardService;
        $this->smsMsgService = $smsMsgService;
        $this->appointmentService = $appointmentService;
        $this->patientService = $patientService;
        $this->leadGenerationService = $leadGenerationService;
        $this->otpExpire = env('OTP_EXPIRE_MINUTES');//minutes
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->billingService->allClinics()->toArray()
        ];
        return view('billing::billing.index',compact('data'));
    }
    public function indexLeadGeneration(Request $request)
    {
        $data = [
            'clinic_list' => $this->billingService->allClinics()->toArray()
        ];
        return view('billing::billing.indexLeadGeneration',compact('data'));
    }
    public function selectClinicForm(Request $request)
    {
        $clinic_id = $request->clinic_id ? $request->clinic_id : null;
        $task_id = $request->task_id ? $request->task_id : null;
        $source_id = $request->source_id ? $request->source_id : null;
        $data = [
            'clinic' => $this->appointmentService->allClinics()->toArray(),
        ];
        return view('billing::billing.selectClinicForm',compact('clinic_id','task_id','source_id','data'));
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        return view('billing::billing.add',compact('id'));
    }
    public function indexLedger(Request $request)
    {
        $data = [
            'clinic_list' => $this->billingService->allClinics()->toArray(),
            'ledger_type_list' => config('billing.ledger_types')
        ];
        return view('billing::billing.indexLedger',compact('data'));
    }
    public function list(Request $request)
    {
        try {
            $filter = $request['filter'];
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 8:
                    $clinic_id = auth()->user()->pharmacist->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            if ($role->id == 9) {
                $filter['created_by'] =  [
                    'type' => 'eq',
                    'value' => $this->getUserId()
                ];
                // $request['with'] = 'paymentBill';
            }
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $request->merge([
                'filter' => $filter
            ]);
            array_push($this->billingService->columns,'patients.parent_id','patients.name');
            $this->billingService->setRequest($request);
            $this->billingService->findAll();
            $this->response['success']  = true;
            $data = $this->billingService->collectionRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $this->response['tbody'] = view('billing::billing.api.list',compact('data','permissionPage','clinic_id'))->render();
            $this->response['tfoot'] = $this->billingService->paginationCustom();
            $this->response['headerAction'] = view('billing::billing.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function leadGenerationList(Request $request)
    {
        try {
            $filter = $request['filter'];
            $role = $this->getUserRole();
            if ($role->id == 9) {
                $filter['created_by'] =  [
                    'type' => 'eq',
                    'value' => $this->getUserId()
                ];
                // $request['with'] = 'paymentBill';
            }
            
            $request->merge([
                'filter' => $filter
            ]);
            array_push($this->leadGenerationService->columns,'patients.name');
            $this->leadGenerationService->setRequest($request);
            $this->leadGenerationService->findAll();
            $this->response['success']  = true;
            $data = $this->leadGenerationService->getRows();
            $data['rows'] = collect($data['rows'])->map(function ($collectData) {
                $phone = $this->patientService->getParentPhone($collectData['patient_id']);
                return collect($collectData)
                    ->put('phone', $phone)
                    ->toArray();
            });
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $this->response['tbody'] = view('billing::billing.api.leadGenerationList',compact('data','permissionPage'))->render();
            $this->response['tfoot'] = $this->leadGenerationService->paginationCustom();
            // $this->response['headerAction'] = view('billing::billing.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listFamily(Request $request)
    {
        $this->response['success']  = true;
        $phone = $request->phone;
        $role = $this->getUserRole();
        $this->response['data'] = [];
        $list = [
            'role_id' => $role->id,
            'patients' => $this->billingService->getFamilys($phone)
        ];
        if (count($list['patients']) > 0) {
            $list['otc_check'] = $this->billingService->otcCheck($list['patients'][0]->family_id);
        }
        // dd($list);
        $parent_id = $this->billingService->getPatientID($phone);
        if (!isset($parent_id)) {
            $parent_id = 0;
        }
        // dd($phone,$parent_id,$list);
        $html = '<tr>';
            $html .= '<td colspan="5" class="border-0">';
            if (count($list['patients']) > 0) {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(0,'.$parent_id.','.$request->phone.')">Add Member</button>';
            }
            else {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(1,'.$parent_id.','.$request->phone.')">Add Patient</button>';
            }
            $html .= '</td>';
        $html .= '</tr>';
        // dd($list['patients'],$list['patients'][0]->membershipRegistrations[0]->memberships);
        $this->response['tbody'] = view('billing::billing.api.listFamily',compact('list','parent_id','phone'))->render();
        $this->response['tfoot'] = $html;
        $permissionPage = $this->getPermissionList();
        $this->response['lead_stat'] = array_intersect(['create_agent_billing'], $permissionPage) ? 1 : 0;
        return response()->json($this->response);
    }
    public function create(Request $request)
    {
        try {
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $service_id = $request->service_id;
            $agent_clinic_id = $request->agent_clinic_id;
            $agent_source_id = $request->agent_source_id;
            $category_id = [1,2,4];//Membership
            $this->response['success']  = true;
            $patient = $this->billingService->findPatient($patient_id);
            $category_ids = $patient->membershipRegistrations->where('status',3)->pluck('category_id')->toArray();
            $otc_check = $this->billingService->otcCheck($patient->family_id);
            if ($otc_check > 0 && !in_array(2,$category_ids)) {
                $category_ids[] = 2;
            }
            $renew_details = [];
            if ($service_id != 0) {
                $renew_details = $this->billingService->findById($service_id);
                $this->response['attr'] = [
                    'card_type' => $renew_details->card_type
                ];
            }
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $user_clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $user_clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 8:
                    $user_clinic_id = auth()->user()->pharmacist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            // dd($otc_check);
            $data = [
                'patient_detail' => $patient,
                'category_ids' => $category_ids,
                'renew_details' => $renew_details,
                'otc_check' => $otc_check,
                'agent_clinic_id' => $agent_clinic_id ?? null,
                'agent_source_id' => $agent_source_id ?? null
            ];
            // dd($data['agent_clinic_id']);
            $list = [
                'membership_list' => $this->billingService->allMemberships($category_id)->toArray(),
                'user_clinic_id' => $user_clinic_id,
                'clinic_list' => $this->billingService->allClinics()->toArray(),
                'source_list' => config('billing.source_list'),
                'payment_mode' => config('billing.payment_mode'),
                'patient_phone' => $phone,
                'reward_points' => $this->rewardService->rewardPoints($phone)
            ];
            $permissionPage = $this->getPermissionList();
            $this->response['form'] = view('billing::billing.api.addEdit',compact('patient_id','phone','data','list','service_id','permissionPage'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function leadGeneration(Request $request)
    {
        try {
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $service_id = $request->service_id;
            $agent_clinic_id = $request->agent_clinic_id;
            $agent_source_id = $request->agent_source_id;
            // $category_id = [1,2];//Membership
            $this->response['success']  = true;
            $patient = $this->billingService->findPatient($patient_id);
            // $category_ids = $patient->membershipRegistrations->where('status',3)->pluck('category_id')->toArray();
            // $otc_check = $this->billingService->otcCheck($patient->family_id);
            // if ($otc_check > 0 && !in_array(2,$category_ids)) {
            //     $category_ids[] = 2;
            // }
            // $renew_details = [];
            // if ($service_id != 0) {
            //     $renew_details = $this->billingService->findById($service_id);
            //     $this->response['attr'] = [
            //         'card_type' => $renew_details->card_type
            //     ];
            // }
            // dd($otc_check);
            $patient_current = $this->patientService->findById($patient_id);
            $member_ids = $patient_current->family->pluck('id')->toArray();
            $check_lead = DB::table('lead_generations')->whereIn('patient_id',$member_ids)->count();
            $data = [
                'patient_detail' => $patient,
                // 'category_ids' => $category_ids,
                // 'renew_details' => $renew_details,
                // 'otc_check' => $otc_check,
                'agent_clinic_id' => $agent_clinic_id ?? null,
                'agent_source_id' => $agent_source_id ?? null,
                'check_lead' => $check_lead
            ];
            // dd($data['agent_clinic_id']);
            $list = [
                // 'membership_list' => $this->billingService->allMemberships($category_id)->toArray(),
                'clinic_list' => $this->billingService->allClinics()->toArray(),
                // 'payment_mode' => config('billing.payment_mode'),
                'patient_phone' => $phone,
                // 'reward_points' => $this->rewardService->rewardPoints($phone)
                'gender_list' => config('patient.genders'),
                'bloodgroup_list' => config('patient.bloodgroups'),
                'language_list' => $this->patientService->allLanguages()
            ];
            // dd($list);
            $permissionPage = $this->getPermissionList();
            $this->response['form'] = view('billing::billing.api.addLeadGeneration',compact('patient_id','phone','data','list','service_id','permissionPage'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function sendOtpForLeadGeneration(Request $request)
    {
        try {
            $check = $this->rewardService->findByOtp($request->phone, 'Lead-Generation');
            $created_at = date('Y-m-d H:i:s');
            $validated_at = date('Y-m-d H:i:s', strtotime('+'.$this->otpExpire.' minutes'));
            $otp = rand(1000, 9999);
            $data = [
                'phone' => $request->phone,
                'type' => 'Lead-Generation',
                'otp' => $otp,
                'created_by' => $this->createdBy(),
                'created_at' => $created_at,
                'validated_at' => $validated_at,
                'status' => 0
            ];
            // dd($check);
            $this->rewardService->updateOtp($data);

            $this->smsMsgService->setFlowId('656edb27d6fc050f1e70af33');
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $variable = [
                'OTP' => $otp
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'Lead Generation OTP',
                'campaign_name' => 'Sent OTP for Lead Generation',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['otp_expire']     = $this->otpExpire;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function verifyOtpForLeadGeneration(Request $request)
    {
        try {
            // dd($request->all());
            $checkOtp = $this->rewardService->checkOtp($request->phone, 'Lead-Generation');
            if ($checkOtp['otp'] == $request->otp) {
                VerifyOtp::where('id',$checkOtp['id'])->update([
                    'status' => 1
                ]);
                $this->response['verify']  = true;
            }
            else {
                $this->response['verify']  = false;
            }
            $this->response['success']  = true;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function addLeadGeneration(Request $request)
    {
        try {
            // dd($request->language);
            $patient = $this->patientService->findById($request->patient_id);
            if (!$patient) {
                $this->response['success']  = false;
                $this->response['message']  = 'Patient not found!';
                return response()->json($this->response);
            }
            $member_ids = $patient->family->pluck('id')->toArray();
            $check_lead = DB::table('lead_generations')->whereIn('patient_id',$member_ids)->count();
            if($check_lead > 0){
                $this->response['success']  = false;
                $this->response['message']  = 'Lead Already Generated!';
                return response()->json($this->response);
            }
            // dd($check_lead);
            $request->merge([
                'created_by' => $this->createdBy(),
                'datasource' => 1,
            ]);
            $this->leadGenerationService->setRequest($request);
            $this->leadGenerationService->add();
            $data = [];
            if($request->language){
                $data['language_id'] = $request->language;
            }
            if($request->sex){
                $data['sex'] = $request->sex;
            }
            if($request->bloodgroup){
                $data['bloodgroup'] = $request->bloodgroup;
            }
            
            $this->patientService->setRequest($data);
            $this->patientService->update();
            // reward points credit
            $bill_id = $this->paymentBillService->billingIncrementId($request->lead_clinic_id,'backendpharmacy',8);
            $pay_type = config('billing.types.5');
            $reward_point_credit = DB::table('service_categorys')->where('id',1)->value('reward_point_for_lead') ?? 30;
            $reward = [
                'phone_no' => $request->phone,
                'date' => date('Y-m-d H:i:s'),
                'type' => 'backendpharmacy',
                'point' => $reward_point_credit,
                'credit_debit' => 1,
                'is_redeem' => 1,
                'bill_id' => $bill_id,
                'created_by' => $this->createdBy()
            ];
            $this->rewardService->setRequest($reward);
            $this->rewardService->add();

            // sms send for reward points credit
            $this->smsMsgService->setFlowId('666d6ce4d6fc05414553f7c3');
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $available_points = $this->rewardService->rewardPoints($request->phone);
            $variable = [
                'var1' => $reward_point_credit,
                'var2' => $available_points
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'Backend Pharmacy',
                'campaign_name' => 'Reward points credit for lead generation',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            // sms send for lead registration
            if ($request->language == "1") {
                $flow_id = "65d45731d6fc05658b46b412";
                $smslang = "Bengali";
            } else if ($request->language == "3") {
                $flow_id = "65d4578ed6fc05552208b902";
                $smslang = "Hindi";
            } else {
                $flow_id = "65d454d4d6fc0547056186a2";
                $smslang = "English";
            }

            $this->smsMsgService->setFlowId($flow_id);
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $clinic_name = DB::table('clinics')->where('id',$request->lead_clinic_id)->value('clinic_name') ?? 'HO';
            $variable = [
                'var' => $clinic_name,
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'Backend Pharmacy',
                'campaign_name' => "Lead generated ( " . $smslang . " )",
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);
            // dd($request->all());
            $this->response['success']  = true;
            $this->response['message']  = 'Lead has been added successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function sendOtpForMembership(Request $request)
    {
        try {
            $check = $this->rewardService->findByOtp($request->phone, 'MB-Reg');
            $created_at = date('Y-m-d H:i:s');
            $validated_at = date('Y-m-d H:i:s', strtotime('+'.$this->otpExpire.' minutes'));
            $otp = rand(1000, 9999);
            $data = [
                'phone' => $request->phone,
                'type' => 'MB-Reg',
                'otp' => $otp,
                'created_by' => $this->createdBy(),
                'created_at' => $created_at,
                'validated_at' => $validated_at,
                'status' => 0
            ];
            // dd($check);
            $this->rewardService->updateOtp($data);

            $this->smsMsgService->setFlowId('656edb27d6fc050f1e70af33');
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $variable = [
                'OTP' => $otp
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'Membership Registration OTP',
                'campaign_name' => 'Sent OTP for Membership Registration',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['otp_expire']     = $this->otpExpire;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function verifyOtpForMembership(Request $request)
    {
        try {
            // dd($request->all());
            $checkOtp = $this->rewardService->checkOtp($request->phone, 'MB-Reg');
            if ($checkOtp['otp'] == $request->otp) {
                VerifyOtp::where('id',$checkOtp['id'])->update([
                    'status' => 1
                ]);
                $this->response['verify']  = true;
            }
            else {
                $this->response['verify']  = false;
            }
            $this->response['success']  = true;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function add(BillingRequest $request)
    {
        try {
            // dd($shortlink);
            $request->validated();
            $category_id = $this->billingService->findCatagory($request->card_type);
            // dd($category_id);
            $request->merge([
                'created_by' => $this->createdBy(),
                'registration_date' => $this->getCurrentDateTime(),
                'data_source' => ($request->agent_payment == 'agent' ? 'Agent' : 'Walkin'),
                'source' => $request->agent_source_id,
                'pincode' => 0,
                'category_id' => $category_id
            ]);
            // dd(strtotime(date('Y-m-d')),strtotime($request->start_date),$request->start_date);
            if (strtotime(date('Y-m-d')) < strtotime($request->start_date)) {
                $request->merge([
                    'status' => 6
                ]);
            }
            if($request->agent_payment == 'agent'){
                $request->merge([
                    'status' => 1
                ]);
            }
            // dd($request->except('payment_modes','amounts','payment_list','upi_mode'));
            $this->billingService->setRequest($request->except('payment_modes','amounts','payment_list','upi_mode'));
            $billingService = $this->billingService->add();
            // renew membership
            if ($request->service_id != 0 && strtotime(date('Y-m-d')) >= strtotime($request->start_date)) {
                $this->billingService->findById($request->service_id);
                $this->billingService->setRequest(['is_renewal' => 2]);
                $this->billingService->update();
            }

            if ($request->agent_payment == 'agent') {
                // payment bill create
                $bill_id = $this->paymentBillService->billingIncrementId($request->clinic_id,'MB',8);
                $pay_type = config('billing.types.0');
                // payment here
                $paymentData = [
                    'bill_show_id' => $bill_id,
                    'type' => $pay_type,//ServiceCategory->membership
                    'service_id' => $billingService->id,
                    'patient_id' => $billingService->patient_id,
                    'bill_amount' => $request->bill_amount,
                    'discount' => 0,
                    'total_amount' => $request->gross_total,
                    'paid_amount' => 0,
                    'due_amount' => $request->gross_total,
                    'status' => 0,
                    'created_by' => $this->createdBy(),
                ];
                // dd($service->paymentBill);
                $this->paymentBillService->setRequest($paymentData);
                $paymentBill = $this->paymentBillService->add();

                // sms fire
                // sms send for Membership payment
                $this->smsMsgService->setFlowId('65900bd2d6fc0567c11218e2');
                $this->smsMsgService->sendSmsToRecipients($request->phone);
                $redirect_slug = "?t=" . urlencode(base64_encode($billingService->id));
                if ($request->card_type == 3) {//for OTC
                    $shortlink = route('billing.membershipBillFormOtc').'/'. $redirect_slug;
                }
                else {
                    $shortlink = route('billing.membershipBillForm').'/'. $redirect_slug;
                }
                $variable = [
                    'shortlink' => $shortlink
                ];
                $this->smsMsgService->setVariable($variable);
                $data = $this->smsMsgService->send();
                $req_data = [
                    'created_by' => $this->createdBy(),
                    'event' => 'MB',
                    'campaign_name' => 'Membership registration payment link',
                    'status' => 2,
                    'reason' => ''
                ];
                $this->smsMsgService->add($req_data);
                // dd($variable);
            }
            else{
               // payment bill create
                $bill_id = $this->paymentBillService->billingIncrementId($request->clinic_id,'MB',8);
                $pay_type = config('billing.types.0');
                // payment here
                $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->bill_amount,0,$billingService->id,$billingService->patient_id,$request->phone);
                // sms send for point calculation
                $this->smsMsgService->setFlowId('667a6881d6fc056d1126b902');
                $this->smsMsgService->sendSmsToRecipients($request->phone);
                $variable = [
                    'var' => $request->reward_points_final == '' ? 0 : $request->reward_points_final,
                    'var1' => $reward_point_credit,
                    'var2' => $request['closing_points']
                ];
                $this->smsMsgService->setVariable($variable);
                $data = $this->smsMsgService->send();
                $req_data = [
                    'created_by' => $this->createdBy(),
                    'event' => 'MB',
                    'campaign_name' => 'Cash back point redemption success',
                    'status' => 2,
                    'reason' => ''
                ];
                $this->smsMsgService->add($req_data);
                // sms send for Membership registration
                $this->smsMsgService->setFlowId('6592b5e6d6fc056991325d82');
                $this->smsMsgService->sendSmsToRecipients($request->phone);
                $variable = [
                    'shortlink' => route('billing.viewInvoice', [base64_encode($billingService->id)])
                ];
                $this->smsMsgService->setVariable($variable);
                $data = $this->smsMsgService->send();
                $req_data = [
                    'created_by' => $this->createdBy(),
                    'event' => 'MB',
                    'campaign_name' => 'Membership registration',
                    'status' => 2,
                    'reason' => ''
                ];
                $this->smsMsgService->add($req_data);
            }
            // dd($request->all());
            $this->response['success']  = true;
            $this->response['message']  = 'Membership billing has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function viewInvoice($id,Request $request)
    {
        $id = base64_decode($id);
        $billing = $this->billingService->findById($id);
        $earned_points = 0;
        if ($billing) {
            $billPayment = $billing->paymentBill->whereIn('type',['MB','MB-CARD']);
            $billPayment->each(function ($billRedeem) {
                if ($billRedeem->type == 'MB') {
                    $billRedeem->redeem_all = $billRedeem->payments->sum('redeem_points');
                }
            });
            $bill_id = $billPayment[0]->bill_show_id;
            // dd($billPayment,$bill_id);
            
            $earned_points = DB::table('reward_points')
                ->where([
                    'bill_id' => $bill_id,
                    'credit_debit' => 1
                ])
                ->whereIn('type',['MB','MB-CARD'])
                ->orderBy('created_at', 'desc')
                ->value('point');
        }
        // dd($earned_points);
        $data = [
            'billing' => $billing,
            'bill_id' => $bill_id,
            'clinic_dtl' => $billing->clinics,
            'patient_dtl' => $billing->patients,
            'billPayment' => $billPayment,
            'billTotal' => $billPayment->where('type','MB')->first(),
            'redeem_for_bill' => $billPayment->sum('redeem_all'),
            'mb_card' => $billPayment->where('type','MB-CARD')->first(),
            'reward_payment_dtl' => $billPayment->first()->payments->first(),
            'earned_points' => $earned_points ? $earned_points : 0,
            'createdBy' => $billing->createdBy->username,
            'all_clinic_dtl' => $this->billingService->allClinics()->pluck('clinic_name')->toArray()
        ];
        // dd($data['billTotal']);
        $pdf = PDF::loadView('billing::billing.pdf.viewInvoice', $data);
        $pdf = $pdf->setPaper('a4', 'portrait');
        return $pdf->stream('bill.pdf');
    }
    public function cardBill($id,Request $request)
    {
        $id = base64_decode($id);
        // dd(round(2.67, 2));
        return view('billing::billing.cardBill',compact('id'));
    }
    public function createCardBill($id)
    {
        try {
            $billing = $this->billingService->findById($id);
            if (!$billing) {
                $this->response['success']    = false;
                $this->response['message']  = 'billing not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // $this->response['data']     = $billing;
            $data = $billing;

            $list = [
                'patient_name' => $data->patients->name,
                'patient_sex' => $data->patients->sex,
                'patient_age' => $data->patients->birthdate,
                'patient_phone' => $data->phone,
                'payment_mode' => config('billing.payment_mode'),
                'visit_price' => 30,
                'reward_points' => $this->rewardService->rewardPoints($data->phone)
            ];
            $discount = 0;
            $list['discount'] = $discount;
            $list['gross_total'] = $list['visit_price'] - $discount;
            // dd($data,$list);
            $this->response['form'] = view('billing::billing.api.cardBillForm',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addCardBill($id,BillingCardRequest $request)
    {
        try {
            $request->validated();
            // dd($request->all());
            $billing = $this->billingService->findById($id);
            if (!$billing) {
                $this->response['success']  = false;
                $this->response['message']  = 'billing not found!';
                return response()->json($this->response);
            }
            // $unique_bill_id = $this->paymentService->billingIncrementId($billing->clinic_id,'MB',8);
            // dd($unique_bill_id);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'created_by' => $this->createdBy(),
                'smart_card' => 2,
            ]);
            $this->billingService->setRequest($request->except('sub_total','discount','gross_total','remarks','payment_modes','amounts','payment_list','upi_mode'));
            $this->billingService->update();
            $billPayment = $billing->paymentBill;
            $pay_type = config('billing.types.3');
            // payment bill create
            $bill_id = $billPayment[0]->bill_show_id;
            // payment here
            $reward_point_credit =$this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,0,$billing->id,$billing->patient_id,$request->phone);

            // sms send for point calculation
            $this->smsMsgService->setFlowId('667a6881d6fc056d1126b902');
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $variable = [
                'var' => $request->reward_points_final == '' ? 0 : $request->reward_points_final,
                'var1' => $reward_point_credit,
                'var2' => $request['closing_points']
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => $pay_type,
                'campaign_name' => 'Cash back point redemption success',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['message']  = 'Smart Card billing has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    private function paymentWithReward($request,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    {
        $request->merge([
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount
        ]);
        // dd($service->paymentBill);
        $this->paymentBillService->setRequest($request->except('remarks','payment_modes','amounts','payment_list','upi_mode'));
        $paymentBill = $this->paymentBillService->add();
        if($request->reward_points_final > 0 || isset($request->payment_modes)){
            // payment create
            $request->merge([
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d'),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','MB-receipt',8),
                'status' => 'Paid'
            ]);
            $this->paymentService->setRequest($request->except('phone','clinic_id','start_date','end_date','registration_no','category_id','card_type','remarks','payment_modes','amounts','payment_list','upi_mode'));
            $payment = $this->paymentService->add();
            // payment details
            $total_amount = 0;
            $total_discount = 0;
            if (isset($request->payment_modes)) {
                foreach ($request->payment_modes as $key => $row) {
                    if ($row == 'upi') {
                        $payment_details = $request->payment_list[$key].' '.$request->upi_mode;
                    }
                    else {
                        $payment_details = $request->payment_list[$key];
                    }
                    $request['payment_id'] = $payment->id;
                    $request['payment_mode'] = $row;
                    $request['amount'] = $request->amounts[$key];
                    $request['payment_details'] = $payment_details;
                    $total_amount += $request->amounts[$key];
                    $this->paymentDetailService->setRequest($request->except('patient_id','status','remarks','payment_modes','amounts','payment_list','upi_mode'));
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }
            // reward points
            $percentage = $this->rewardService->getPercentage(2);
            $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
            $request['openning_points'] = $this->rewardService->rewardPoints($phone);
            // reward points debit
            if($request->reward_points_final > 0){
                $reward = [
                    'phone_no' => $request->reward_phone_no,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $request->reward_points_final*-1,
                    'credit_debit' => 2,
                    'is_redeem' => 2,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // reward points credit
            if($total_amount > 0){
                $reward = [
                    'phone_no' => $phone,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $reward_point_credit,
                    'credit_debit' => 1,
                    'is_redeem' => 1,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // payment table reward point update
            $request['amount'] = $total_amount;
            $request['discount'] = $total_discount;
            $request['gross_total'] = ($total_amount+$total_discount);
            $request['redeem_points'] = $request->reward_points_final == '' ? 0 : $request->reward_points_final;
            $request['closing_points'] = $this->rewardService->rewardPoints($phone);
            $payment = $this->paymentService->findById($payment->id);
            $this->paymentService->setRequest($request->except(
                'bill_id','date','remarks','recpit_no','status',
                'patient_id','remarks','payment_modes',
                'amounts','payment_list','upi_mode'
            ));
            $this->paymentService->update();
        }
        // payment bill table amount update
        $request['total_amount'] = $total_amount;
        $request['paid_amount'] = $total_amount;
        $request['due_amount'] = 0;
        $paymentBill = $this->paymentBillService->findById($paymentBill->id);
        $this->paymentBillService->setRequest($request->except(
            'bill_show_id','type','service_id','patient_id','status',
            'patient_id','phone','clinic_id','start_date','end_date',
            'registration_no','card_type','remarks','payment_modes',
            'amounts','payment_list','upi_mode'
        ));
        $this->paymentBillService->update();

        return $reward_point_credit;
    }
    public function updateStatus($id,Request $request)
    {
        try {
            $holiday = $this->billingService->findById($id);
            if (!$holiday) {
                $this->response['success']  = false;
                $this->response['message']  = 'Membership not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->billingService->setRequest($request);
            $this->billingService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Membership has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    // public function delete($id)
    // {
    //     $deleted_by = $this->createdBy();
    //     try {
    //         $holiday = $this->billingService->findById($id);
    //         if (!$holiday) {
    //             $this->response['success'] = false;
    //             $this->response['message'] = 'Membership not found!';
    //             return response()->json($this->response);
    //         }
    //         // if(count($holiday->clinics) > 0){
    //         //     $this->response['success'] = false;
    //         //     $this->response['message'] = 'This Holiday cannot be deleted because there are clinics created under it.';
    //         //     return response()->json($this->response);
    //         // }
    //         $this->billingService->delete($deleted_by);

    //         $this->response['success'] = true;
    //         $this->response['message'] = 'Membership has been delete successfully!';
    //         $this->response['data'] = [];
    //     } catch (\Exception $e) {
    //         $this->response['success'] = false;
    //         $this->response['message'] = $e->getMessage();
    //         $this->response['line_number'] = $e->getLine();
    //         $this->response['file_name'] = $e->getFile();
    //     }

    //     return response()->json($this->response);
    // }
    public function listLedger(Request $request)
    {
        try {
            // dd($request->all());
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            $this->response['clinic_id'] = $clinic_id;
            if ($request->type == 'MB') {
                $filter = $request['filter'];
                if ($clinic_id) {
                    $filter['clinic_id'] =  [
                        'type' => 'eq',
                        'value' => $clinic_id
                    ];
                }
                $request->merge([
                    'filter' => $filter
                ]);
                $this->billingService->setRequest($request);
                $data = $this->billingService->findBillingMB();
                // dd($data);
                $this->response['success']  = true;
                // dd($data->where('card_type',2)->sum('payments_sum_amount'));
                $permissionPage = $this->getPermissionList();
                $this->response['tbody'] = view('billing::billing.api.listLedgerMB',compact('data','permissionPage'))->render();
                $this->response['totalCal'] = view('billing::billing.api.listLedgerTotalMB',compact('data','permissionPage'))->render();
            }
            elseif ($request->type == 'OPD') {
                // dd($request->all());
                $filter = $request['filter'];
                if ($clinic_id) {
                    $filter['clinic_id'] =  [
                        'type' => 'eq',
                        'value' => $clinic_id
                    ];
                }
                $request->merge([
                    'filter' => $filter
                ]);
                $this->billingService->setRequest($request);
                $data = $this->billingService->findBillingOPD();
                $this->response['success']  = true;
                // dd($data->sum('cash_payments_sum'),$data->sum('credit_card_payments_sum'));
                $permissionPage = $this->getPermissionList();
                $this->response['tbody'] = view('billing::billing.api.listLedgerOPD',compact('data','permissionPage'))->render();
                $this->response['totalCal'] = view('billing::billing.api.listLedgerTotalOPD',compact('data','permissionPage'))->render();
            }
            elseif ($request->type == 'RP') {
                // dd($request->all());
                // $filter = $request['filter'];
                // if ($clinic_id) {
                //     $filter['clinic_id'] =  [
                //         'type' => 'eq',
                //         'value' => $clinic_id
                //     ];
                // }
                // $request->merge([
                //     'filter' => $filter
                // ]);
                $this->billingService->setRequest($request);
                $data = $this->billingService->findBillingRP();
                $this->response['success']  = true;
                // dd($data[0]->paymentBill);
                $permissionPage = $this->getPermissionList();
                $this->response['tbody'] = view('billing::billing.api.listLedgerRP',compact('data','permissionPage'))->render();
                $this->response['totalCal'] = view('billing::billing.api.listLedgerTotalRP',compact('data','permissionPage'))->render();
            }
            // $this->response['tfoot'] = $this->billingService->paginationCustom();
            // $this->response['headerAction'] = view('schedule::schedule.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function membershipBillForm(Request $request)
    {
        $mem_id = base64_decode($request->t);
        $billing = $this->billingService->findById($mem_id);
        if (!$billing) {
            $this->response['success']  = false;
            $this->response['message']  = 'billing not found!';
            return response()->json($this->response);
        }
        if (date('Y-m-d H:i:s', strtotime('+1 day', strtotime($billing->created_at))) < date('Y-m-d H:i:s')) {
            $this->response['success']  = false;
            $this->response['message']  = 'Link Expired';
            return response()->json($this->response);
        }

        $data = [
            'memebership' => $billing,
            'patient' => $billing->patients,
            'clinic' => $billing->clinics,
            'card_type' => $billing->memberships->name,
            'payment_dtl' => $billing->paymentBill
        ];
        // dd($data);
        return view('billing::billing.membershipBillForm',compact('data'));
    }
    public function membershipBillFormOtc(Request $request)
    {
        $mem_id = base64_decode($request->t);
        $billing = $this->billingService->findById($mem_id);
        if (!$billing) {
            $this->response['success']  = false;
            $this->response['message']  = 'billing not found!';
            return response()->json($this->response);
        }
        if (date('Y-m-d H:i:s', strtotime('+1 day', strtotime($billing->created_at))) < date('Y-m-d H:i:s')) {
            $this->response['success']  = false;
            $this->response['message']  = 'Link Expired';
            return response()->json($this->response);
        }

        $data = [
            'memebership' => $billing,
            'patient' => $billing->patients,
            'clinic' => $billing->clinics,
            'card_type' => $billing->memberships->name,
            'payment_dtl' => $billing->paymentBill
        ];
        // dd($data);
        return view('billing::billing.membershipBillFormOtc',compact('data'));
    }
}
