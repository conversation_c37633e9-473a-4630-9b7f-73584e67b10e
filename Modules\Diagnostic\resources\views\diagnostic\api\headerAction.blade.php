
<div class="d-flex gap-2">
    @if (array_intersect(['create_diagnostic','create_with_phlebo_diagnostic'], $permissionPage))
        <a href="{{ route('diagnostic.addForm') }}"
            class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center">
            <i class="icon">
                <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="icon-22">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4">
                    </path>
                </svg>
            </i>
            Add New
        </a>
    @endif
    @if (array_intersect(['view_diagnostic_sample_status'], $permissionPage))
        <a href="{{ route('diagnostic.sampleStatus.index') }}">
            <button type="button" class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center ">
                Sample Status
            </button>
        </a>
    @endif
</div>

