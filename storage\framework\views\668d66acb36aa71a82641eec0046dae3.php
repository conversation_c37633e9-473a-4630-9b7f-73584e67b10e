<!DOCTYPE html>
<html>

<head>
    <title>Review Form</title>
    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo e(asset('/front-assets/fav/apple-icon-57x57.png')); ?>">
    <link rel="apple-touch-icon" sizes="60x60" href="<?php echo e(asset('/front-assets/fav/apple-icon-60x60.png')); ?>">
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo e(asset('/front-assets/fav/apple-icon-72x72.png')); ?>">
    <link rel="apple-touch-icon" sizes="76x76" href="<?php echo e(asset('/front-assets/fav/apple-icon-76x76.png')); ?>">
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo e(asset('/front-assets/fav/apple-icon-114x114.png')); ?>">
    <link rel="apple-touch-icon" sizes="120x120" href="<?php echo e(asset('/front-assets/fav/apple-icon-120x120.png')); ?>">
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo e(asset('/front-assets/fav/apple-icon-144x144.png')); ?>">
    <link rel="apple-touch-icon" sizes="152x152" href="<?php echo e(asset('/front-assets/fav/apple-icon-152x152.png')); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('/front-assets/fav/apple-icon-180x180.png')); ?>">
    <link rel="icon" type="image/png" sizes="192x192"
        href="<?php echo e(asset('/front-assets/fav/android-icon-192x192.png')); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('/front-assets/fav/favicon-32x32.png')); ?>">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo e(asset('/front-assets/fav/favicon-96x96.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('/front-assets/fav/favicon-16x16.png')); ?>">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href="https://unpkg.com/survey-core/defaultV2.min.css" type="text/css" rel="stylesheet">

    <!-- <script type="text/javascript" src="https://unpkg.com/survey-core/survey.core.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/survey-js-ui/survey-js-ui.min.js"></script> -->

    <link href="https://unpkg.com/survey-core@2.0.0/survey-core.min.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="https://unpkg.com/survey-core@2.0.0/survey.core.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/survey-js-ui@2.0.0/survey-js-ui.min.js"></script>
    <link href="<?php echo e(asset('/front-assets/scss/sweetalert.min.css?v=937985')); ?>" rel="stylesheet">
    <style>
        /* Customizing the survey theme colors */
        :root {
            --sjs-primary-backcolor: #e04848;
            --sjs-primary-hover-backcolor: #c43d3d;
            --sjs-primary-text-color: #ffffff;
            --sjs-secondary-backcolor: #f7d1d1;
            --sjs-secondary-hover-backcolor: #f1b1b1;
            --sjs-secondary-text-color: #e04848;
        }

        /* Styling the image at the top */
        #surveyHeader {
            text-align: center;
            margin-bottom: 20px;
        }

        #surveyHeader img {
            max-width: 150px;
            /* border-radius: 50%;  */
        }

        .sd-navigation__complete-btn:hover {
            background-color: #f7d1d1;
            /* Change this to your desired hover color */
            color: #e04848;
            /* Maintain text color on hover */
        }

        .sd-progress--pages {
            color: #e04848;
        }

        .sd-root-modern .sd-container-modern__title {
            text-align: center;
        }

        body {
            margin: 0;
        }

        .sd-root-modern--mobile .sd-body.sd-body--static {
            padding: 1rem;
        }

        /* rating section design  */
        :root {
            --sjs-primary-backcolor: #d01337;
            --sjs-primary-hover-backcolor: #d01337;
            --sjs-primary-text-color: #ffffff;
            --sjs-secondary-backcolor: #f7d1d1;
            --sjs-secondary-hover-backcolor: #f1b1b1;
            --sjs-secondary-text-color: #d01337;
        }

        .sd-title.sd-container-modern__title {
            box-shadow: none !important;
        }

        .sd-body.sd-body--static {
            padding-top: 2em;
        }

        #surveyHeader {
            margin-bottom: 0;
            margin-top: 1em;
        }

        .sd-page.sd-body__page {
            padding: 1em !important;
            background-color: #fff !important;
        }

        .sd-body.sd-body--static {
            max-width: calc(100*(var(--sjs-base-unit, var(--base-unit, 8px)))) !important;
        }

        .sd-question__header--location-top {
            padding-bottom: 0;
        }

        .sd-element--with-frame.sd-question--title-top {
            display: flex;
            padding: 1em 2em;
            box-shadow: none;
            align-items: center;
        }

        .sd-element__num {
            color: #d01337 !important;
            font-size: 16px !important;
            font-weight: bold;
            padding-top: 4px;
        }

        .sd-body.sd-body--static .sd-body__navigation.sd-action-bar {
            justify-content: center;
        }

        .sd-comment {
            resize: both;
            background: #fff;
            border: 1px solid #e1e1e1;
            box-shadow: none;
        }

        .sd-rating__item--selected:focus-within {
            box-shadow: var(--sjs-shadow-small-reset, 0px 0px 0px 0px rgba(0, 0, 0, 0.15)), inset 0 0 0 4px var(--sjs-general-backcolor, var(--background, #fff)), 0 0 0 2px var(--sjs-primary-backcolor, var(--primary, #19b394));
        }

        .sd-btn--action {
            background-color: #d01337 !important;
            color: #fff !important;
        }

        .sd-completedpage {
            padding: 6em 1em !important;
        }

        .sd-completedpage:before {
            display: none !important;
        }

        .sd-completedpage h3 {
            font-size: 1.2em !important;
        }

        .sd-error.sd-element__erbox {
            position: absolute;
            background: transparent;
            top: auto !important;
            bottom: -23px;
        }

        @media screen and (max-width: 767px) {
            .sd-root-modern .sd-container-modern__title .sd-header__text h3 {
                font-size: 1.4em !important;
            }

            .sd-element--with-frame.sd-question--title-top {
                padding: 1em 0 !important;
                flex-wrap: wrap;
                gap: 1em !important;
            }

            .sd-error.sd-element__erbox {
                top: auto !important;
                bottom: -31px;
                left: 1em;
            }
        }
    </style>
</head>

<body>
    <!-- Image at the top -->
    <div id="surveyHeader">
        <img src="https://ik.imagekit.io/mymd/image/profile_image/1707204126_MYMD.png" alt="Survey Header Image">
    </div>

    <!-- Survey container -->
    <div id="surveyContainer"></div>
</body>

</html>
<script src="<?php echo e(asset('/front-assets/js/sweetalert.min.js?v=373234')); ?>"></script>
<script>
    // Survey JSON from the database
    const surveyJson = <?php echo $data['reviewSurvey']['survey_settings_json']; ?>;

    // Create the survey model
    const survey = new Survey.Model(surveyJson);

    // Function to send survey results to the server
    async function sendResultsToServer(sender) {
        const surveyResults = sender.data; // Survey results as JSON
        const postData = {
            survey_results: surveyResults,
            url_slag: "<?php echo e($data['reviewSurvey']['url_slag'] ?? ''); ?>",
            patient_id: "<?php echo e($data['patient_id'] ? $data['patient_id'] : ''); ?>",
            source_type: "<?php echo e($data['source_type'] ? $data['source_type'] : 0); ?>",
            source_id: "<?php echo e($data['source_id'] ? $data['source_id'] : ''); ?>",
        };

        try {
            const response = await fetch("<?php echo e(config('settings.url_review_survey') . 'submitPatientSurvey'); ?>", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(postData),
            });
            const data = await response.json();
            if (data.success) {
                // You can trigger Thank You SMS here if needed
                // triggerThankYouSMS();
                return true;
            } else {
                swal("", "Failed to submit the survey. Please try again.", "error");
                return false;
            }
        } catch (error) {
            swal("", "An error occurred while submitting the survey. Please try again later.", "error");
            return false;
        }
    }

    // Usage: On survey complete
    survey.onComplete.add(async function(sender) {
        const success = await sendResultsToServer(sender);
        if (success) {
            
        } else {
            // window.location.reload();
        }
    });

    // Render the survey when the DOM is fully loaded
    document.addEventListener("DOMContentLoaded", function() {
        survey.render(document.getElementById("surveyContainer"));
    });
</script>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Settings\resources/views/reviewSurvey/view.blade.php ENDPATH**/ ?>