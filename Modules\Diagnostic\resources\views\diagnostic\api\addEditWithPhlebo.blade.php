@if ($id && !empty($list['offer_list']))
    @php
        $offer_list = $list['offer_list'][0];
        $data_offer = json_encode(
            [
                'offered_type' => $offer_list['offered_type'],
                'rate_type' => $offer_list['rate_type'],
                'rate' => $offer_list['rate'],
                'test_list' => $offer_list['test_list'],
                'hc_list' => $offer_list['hc_list'],
                'clinic_list' => $offer_list['clinic_list'],
            ],
            true,
        );
    @endphp
    <script>
        offerDiscount('<?php echo $data_offer; ?>');
    </script>
@endif
<form class="clearfix" method="post" action="{{ config('diagnostic.url') . 'addWithPhlebo' }}" data-mode="add"
    enctype="multipart/form-data" id="submitForm">
    <input type="hidden" id="source_id" name="source_id" value="">
    <input type="hidden" id="source_type" name="source_type" value="">
    <input type="hidden" id="source_test_id" name="source_test_id" value="">
    <input type="hidden" name="patient_id" value="{{ $data['patient_detail']->id }}">
    @if (!$id)
        <input type="hidden" name="patient_phone" value="{{ $list['patient_phone'] }}">
    @endif
    <div class="row" data-select2-id="select2-data-16-ukk2">
        <div class="col-sm-12" data-select2-id="select2-data-15-62ts">
            <!------------Top card starts here----------->
            <div class="card  mb-3" data-select2-id="select2-data-14-bl8l">
                <div class="card-header border-bottom p-4" style="background-color: #fff8f8;">
                    <div class=" row">
                        <!--Form header starts here-->
                        <div class="col-md-12 mb-3 mb-md-0">
                            <div class="d-flex flex-wrap justify-content-between">
                                <div class="d-flex gap-4 flex-wrap">
                                    <div class="d-flex align-items-center me-5">
                                        <h6 class="h5 fw-bold text-primary">
                                            <span>{{ $data['patient_detail']->name }}</span>
                                        </h6> &nbsp;<span class="h7" style="margin-top: 4px;">
                                            (<span>{{ $data['patient_detail']->sex }}</span>/<span>{{ Helper::ageCalculator($data['patient_detail']->birthdate) }}</span>)</span>
                                    </div>
                                    <div class="d-flex align-items-center  justify-content-between flex-wrap">
                                        <p class="text-dark h7 me-3 mb-0">
                                            <!--<span class="text-dark fw-bold">UHID:</span>--><svg height="20"
                                                fill="#d01337" style="    margin-top: -2px;"
                                                enable-background="new 0 0 24 24" id="fi_3596091" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <path
                                                        d="m21.5 21h-19c-1.378 0-2.5-1.122-2.5-2.5v-13c0-1.378 1.122-2.5 2.5-2.5h19c1.378 0 2.5 1.122 2.5 2.5v13c0 1.378-1.122 2.5-2.5 2.5zm-19-17c-.827 0-1.5.673-1.5 1.5v13c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-13c0-.827-.673-1.5-1.5-1.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m7.5 12c-1.378 0-2.5-1.122-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.122 2.5 2.5-1.122 2.5-2.5 2.5zm0-4c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m11.5 17c-.276 0-.5-.224-.5-.5v-1c0-.827-.673-1.5-1.5-1.5h-4c-.827 0-1.5.673-1.5 1.5v1c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-1c0-1.378 1.122-2.5 2.5-2.5h4c1.378 0 2.5 1.122 2.5 2.5v1c0 .276-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 9h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 13h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 17h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                            </svg>
                                            <span id="patientUHIDNo">{{ $data['patient_detail']->uhid_no }}</span>
                                        </p>
                                        <p class=" text-dark h7 me-3 mb-0">
                                            <!--<span class="text-dark fw-bold">Phone:</span>-->
                                            <svg id="fi_159832" height="15" fill="#d01337"
                                                style="    margin-top: -3px;" version="1.1" viewBox="0 0 482.6 482.6"
                                                x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
                                                <g>
                                                    <path d="M98.339,320.8c47.6,56.9,104.9,101.7,170.3,133.4c24.9,11.8,58.2,25.8,95.3,28.2c2.3,0.1,4.5,0.2,6.8,0.2
            c24.9,0,44.9-8.6,61.2-26.3c0.1-0.1,0.3-0.3,0.4-0.5c5.8-7,12.4-13.3,19.3-20c4.7-4.5,9.5-9.2,14.1-14
            c21.3-22.2,21.3-50.4-0.2-71.9l-60.1-60.1c-10.2-10.6-22.4-16.2-35.2-16.2c-12.8,0-25.1,5.6-35.6,16.1l-35.8,35.8
            c-3.3-1.9-6.7-3.6-9.9-5.2c-4-2-7.7-3.9-11-6c-32.6-20.7-62.2-47.7-90.5-82.4c-14.3-18.1-23.9-33.3-30.6-48.8
            c9.4-8.5,18.2-17.4,26.7-26.1c3-3.1,6.1-6.2,9.2-9.3c10.8-10.8,16.6-23.3,16.6-36s-5.7-25.2-16.6-36l-29.8-29.8
            c-3.5-3.5-6.8-6.9-10.2-10.4c-6.6-6.8-13.5-13.8-20.3-20.1c-10.3-10.1-22.4-15.4-35.2-15.4c-12.7,0-24.9,5.3-35.6,15.5l-37.4,37.4
            c-13.6,13.6-21.3,30.1-22.9,49.2c-1.9,23.9,2.5,49.3,13.9,80C32.739,229.6,59.139,273.7,98.339,320.8z M25.739,104.2
            c1.2-13.3,6.3-24.4,15.9-34l37.2-37.2c5.8-5.6,12.2-8.5,18.4-8.5c6.1,0,12.3,2.9,18,8.7c6.7,6.2,13,12.7,19.8,19.6
            c3.4,3.5,6.9,7,10.4,10.6l29.8,29.8c6.2,6.2,9.4,12.5,9.4,18.7s-3.2,12.5-9.4,18.7c-3.1,3.1-6.2,6.3-9.3,9.4
            c-9.3,9.4-18,18.3-27.6,26.8c-0.2,0.2-0.3,0.3-0.5,0.5c-8.3,8.3-7,16.2-5,22.2c0.1,0.3,0.2,0.5,0.3,0.8
            c7.7,18.5,18.4,36.1,35.1,57.1c30,37,61.6,65.7,96.4,87.8c4.3,2.8,8.9,5,13.2,7.2c4,2,7.7,3.9,11,6c0.4,0.2,0.7,0.4,1.1,0.6
            c3.3,1.7,6.5,2.5,9.7,2.5c8,0,13.2-5.1,14.9-6.8l37.4-37.4c5.8-5.8,12.1-8.9,18.3-8.9c7.6,0,13.8,4.7,17.7,8.9l60.3,60.2
            c12,12,11.9,25-0.3,37.7c-4.2,4.5-8.6,8.8-13.3,13.3c-7,6.8-14.3,13.8-20.9,21.7c-11.5,12.4-25.2,18.2-42.9,18.2
            c-1.7,0-3.5-0.1-5.2-0.2c-32.8-2.1-63.3-14.9-86.2-25.8c-62.2-30.1-116.8-72.8-162.1-127c-37.3-44.9-62.4-86.7-79-131.5
            C28.039,146.4,24.139,124.3,25.739,104.2z">
                                                    </path>
                                                </g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                            </svg>
                                            <span>{{ Helper::maskPhoneNumber($list['patient_phone']) }}</span>
                                        </p>
                                        <!-- <p class="mb-1 text-gray">Age: </p> -->
                                        <span>
                                        </span>
                                        @if ($service)
                                            <p class="text-dark h7 mb-0">
                                                <svg height="18" width="20" style="margin-top: -2px;"
                                                    fill="#d01337" clip-rule="evenodd" fill-rule="evenodd"
                                                    id="fi_9720867" stroke-linejoin="round" stroke-miterlimit="2"
                                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="Icon">
                                                        <path
                                                            d="m7.035 8.641 3.996-4.58c.244-.28.598-.441.969-.441s.725.161.969.441l3.996 4.58 3.813-2.408c.427-.269.972-.264 1.394.014s.642.778.562 1.277l-1.812 11.322c-.141.884-.904 1.534-1.798 1.534h-14.248c-.894 0-1.657-.65-1.798-1.534l-1.812-11.322c-.08-.499.14-.999.562-1.277s.967-.283 1.394-.014zm-4.213-.886 1.737 10.854c.025.156.159.271.317.271h14.248c.158 0 .292-.115.317-.271l1.737-10.854-3.567 2.252c-.536.339-1.239.236-1.656-.241l-3.955-4.534-3.955 4.534c-.417.477-1.12.58-1.656.241l-3.567-2.252z">
                                                        </path>
                                                        <path
                                                            d="m20.037 15.129c.414 0 .75.336.75.75s-.336.75-.75.75h-16.074c-.414 0-.75-.336-.75-.75s.336-.75.75-.75z">
                                                        </path>
                                                    </g>
                                                </svg>

                                                {{ $service }}: Active <span>
                                                </span>
                                            </p>
                                        @endif
                                    </div>
                                </div>
                                @if (!$id)
                                    <div>
                                        <a href="{{ !isset($data['patient_detail']['parent_id']) ? route('patient.addForm', ['id' => $data['patient_detail']['id'], 'stat' => 'DG']) : route('patient.addFamilyForm', ['id' => $data['patient_detail']['parent_id'], 'mem_id' => $data['patient_detail']['id'], 'stat' => 'DG']) }}"
                                            onclick="redirectStoreRecord({{ $data['patient_detail']['id'] }},{{ $phone }},'{{ $service }}')"
                                            class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center mt-2 mt-md-0">
                                            <svg width="20" viewBox="0 0 24 24" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.4"
                                                    d="M19.9927 18.9534H14.2984C13.7429 18.9534 13.291 19.4124 13.291 19.9767C13.291 20.5422 13.7429 21.0001 14.2984 21.0001H19.9927C20.5483 21.0001 21.0001 20.5422 21.0001 19.9767C21.0001 19.4124 20.5483 18.9534 19.9927 18.9534Z"
                                                    fill="currentColor"></path>
                                                <path
                                                    d="M10.309 6.90385L15.7049 11.2639C15.835 11.3682 15.8573 11.5596 15.7557 11.6929L9.35874 20.0282C8.95662 20.5431 8.36402 20.8344 7.72908 20.8452L4.23696 20.8882C4.05071 20.8903 3.88775 20.7613 3.84542 20.5764L3.05175 17.1258C2.91419 16.4915 3.05175 15.8358 3.45388 15.3306L9.88256 6.95545C9.98627 6.82108 10.1778 6.79743 10.309 6.90385Z"
                                                    fill="currentColor"></path>
                                                <path opacity="0.4"
                                                    d="M18.1208 8.66544L17.0806 9.96401C16.9758 10.0962 16.7874 10.1177 16.6573 10.0124C15.3927 8.98901 12.1545 6.36285 11.2561 5.63509C11.1249 5.52759 11.1069 5.33625 11.2127 5.20295L12.2159 3.95706C13.126 2.78534 14.7133 2.67784 15.9938 3.69906L17.4647 4.87078C18.0679 5.34377 18.47 5.96726 18.6076 6.62299C18.7663 7.3443 18.597 8.0527 18.1208 8.66544Z"
                                                    fill="currentColor"></path>
                                            </svg>
                                            Edit Patient
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!--Form header ends here-->
                    </div>
                </div>
                <div class="card-body pb-3" data-select2-id="select2-data-13-l6y1">
                    <div class="row">
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label">Visit Type *</label>
                            <div
                                class="row row-cols-auto row-cols-md-auto row-cols-lg-auto   mt-1 ps-2 ps-md-3 gap-md-1 gap-1">
                                @foreach ($list['visit_type'] as $key => $row)
                                    <div class="form-check col-md p-0 border-0 border-light border-2 mb-0">
                                        <input class="form-check-input btn-check" value="{{ $key }}"
                                            type="radio" onclick="typeCollectionChange(this.value)"
                                            name="type_of_collection" id="type_of_collection{{ $key }}"
                                            {{ $key == 'HC' ? 'checked' : '' }}>
                                        <label
                                            class="btn py-1 px-3 px-md-2 px-xxl-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                                            for="type_of_collection{{ $key }}">{{ $row }}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label">Visit Date *</label>
                            <input type="date" class="form-control form-control-sm" name="date_of_collection"
                                id="date_of_collection" required=""
                                value="{{ date('Y-m-d', strtotime('+1 days')) }}"
                                min="{{ date('Y-m-d', strtotime('+1 days')) }}">
                        </div>
                        <div class="col-md-3 form-group payment right-six">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Status</label>
                            </div>
                            <div class="">
                                <select class="form-select form-select-sm m-bot15 js-example-basic-single"
                                    id="status" name="status" value="">
                                    @foreach ($list['status_list'] as $key => $row)
                                        <option value="{{ $key }}"
                                            {{ $id ? ($data['diagnostic']['status'] == $key ? 'selected' : '') : '' }}>
                                            {{ $row }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 form-group right-six mb-3 mb-md-0">
                            <label class="form-label  d-block mb-2">Select Doctor:</label>
                            <div
                                class="row row-cols-auto row-cols-md-auto row-cols-lg-auto   mt-1 ps-2 ps-md-3 gap-md-1 gap-1">
                                <div class="form-check col-md p-0 border-0 border-light border-2 mb-0">
                                    <input class="form-check-input btn-check" value="1" type="radio"
                                        onclick="doctorTypeChange(this.value)" name="doctortype" id="doctortype1">
                                    <label
                                        class="btn py-1 px-3 px-md-2 px-xxl-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                                        for="doctortype1">myMD Doctor</label>
                                </div>
                                <div class="form-check col-md p-0 border-0 border-light border-2 mb-0">
                                    <input class="form-check-input btn-check" value="2" type="radio"
                                        onclick="doctorTypeChange(this.value)" name="doctortype" id="doctortype2"
                                        checked>
                                    <label
                                        class="btn py-1 px-3 px-md-2 px-xxl-3 w-100 h7 rounded shadow-sm btn-outline-primary"
                                        for="doctortype2">Other Doctor</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 form-group panel selct-modal mymdDoctorDiv"
                            style="{{ $id ? ($data['diagnostic']['doctortype'] == 1 ? '' : 'display:none;') : 'display:none;' }}">
                            <label for="exampleInputEmail1" class="form-label fw-bold"> Doctor</label>
                            <select id="doctor_id" name="doctor_id" class="select2-multpl-custom1 form-select"
                                data-style="py-0">
                                <option value="">Select</option>
                                @foreach ($list['doctor_list'] as $row)
                                    <option value="{{ $row->id }}"
                                        {{ $id ? ($data['diagnostic']['doctor_id'] == $row->id ? 'selected' : '') : '' }}>
                                        {{ stripos($row->username, 'Dr.') !== 0 ? 'Dr. ' . $row->username : $row->username }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 form-group payment right-six otherDoctorDiv" style="">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Doctor Name</label>
                            </div>
                            <div class="d-flex">
                                <input type="text" class="form-control form-control-sm " name="doctor_first_name"
                                    id="doctor_first_name" placeholder="First Name"
                                    value="{{ $id ? $data['diagnostic']->userDoctors->diagnosticDoctor->first_name : '' }}">
                                <input type="text" class="form-control form-control-sm " name="doctor_last_name"
                                    id="doctor_last_name" placeholder="Last Name"
                                    value="{{ $id ? $data['diagnostic']->userDoctors->diagnosticDoctor->last_name : '' }}">
                            </div>
                        </div>
                        <div class="col-md-3 form-group payment right-six mb-3 mb-md-0">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Remarks</label>
                            </div>
                            <div class="">
                                <input type="text" class="form-control form-control-sm " rows="1"
                                    name="sample_remarks" id="sample_remarks" placeholder=" "
                                    value="{{ $id ? $data['diagnostic']['remarks'] : '' }}">
                            </div>
                        </div>
                        <div class="col-md-3 form-group panel selct-modal4 CV_div {{ isset($list['user_clinic_id']) ? 'pe-none' : '' }}"
                            style="">
                            <label for="exampleInputEmail1" class="form-label "> Clinics</label>
                            <select id="clinic_id" name="clinic_id" class="select2-multpl-custom1 form-select"
                                data-style="py-0">
                                <option value="">Select</option>
                                @foreach ($list['clinic_list'] as $row)
                                    @php
                                        $selected = $id
                                            ? ($data['diagnostic']['clinic_id'] == $row['id']
                                                ? 'selected'
                                                : '')
                                            : '';
                                        if (isset($list['user_clinic_id'])) {
                                            $selected = $list['user_clinic_id'] == $row['id'] ? 'selected' : '';
                                        }
                                    @endphp
                                    <option value="{{ $row['id'] }}" {{ $selected }}>
                                        {{ $row['clinic_name'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label class="form-label fw-bold" for="city">Upload Prescription</label>
                            <input type="file" id="prescription_upload"
                                class="default form-control form-control-sm" name="prescription_upload_file">
                        </div>
                        <div class="col-md-12 HC_div"
                            style="{{ $id ? ($data['diagnostic']['type_of_collection'] == 'HC' ? '' : 'display:none;') : 'display:none;' }}">
                            <div class="row">
                                {{-- <div class="col-md-3 form-group panel">
                                    <label for="exampleInputBulding" class="form-label">Building No.</label>
                                    <input type="text" class="form-control form-control-sm" name="building_no"
                                        id="building_no" value="{{ $id ? $data['diagnostic']['building_no'] : '' }}"
                                        placeholder="">
                                </div> --}}
                                @php
                                    $full_address = $id
                                        ? $data['diagnostic']['full_address']
                                        : $list['source_list']['hc_address']['full_address'] ?? '';
                                    $landmark = $id
                                        ? $data['diagnostic']['landmark']
                                        : $list['source_list']['hc_address']['landmark'] ?? '';
                                    $city = $id
                                        ? $data['diagnostic']['city']
                                        : $list['source_list']['hc_address']['city'] ?? '';
                                    $pincode = $id
                                        ? $data['diagnostic']['pincode']
                                        : $list['source_list']['hc_address']['pincode'] ?? '';
                                @endphp
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputAddress" class="form-label">Full Address</label>
                                    <input type="text" class="form-control form-control-sm" name="full_address"
                                        id="full_address" value="{{ $full_address }}" placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputLandmark" class="form-label">Land Mark</label>
                                    <input type="text" class="form-control form-control-sm" name="landmark"
                                        id="landmark" value="{{ $landmark }}" placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputCity" class="form-label">City</label>
                                    <input type="text" class="form-control form-control-sm" name="city"
                                        id="city" value="{{ $city }}" placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputPincode" class="form-label">Pincode</label>
                                    <input type="text" class="form-control form-control-sm" name="pincode"
                                        id="pincode" value="{{ $pincode }}" placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card-body border-top" data-select2-id="select2-data-13-l6y1">
                    <div class="row">
                        <div class="col-md-12 col-12">
                            {{-- test_list --}}
                            <div class="row border-bottom pb-3 mb-3  d-md-flex">
                                <div class="col-md-12">
                                    <div class="row mb-4">
                                        <div class="col-md-12 ">
                                            <label for="exampleInputEmail1" class="form-label "> Tests</label>
                                        </div>
                                        <div class="col-md-3 ">
                                            <select class="select2-multpl-custom1 form-select" data-style="py-0"
                                                id="test_ids">
                                                <option value="">Select</option>
                                                @foreach ($list['test_list'] as $row)
                                                    <option value="{{ $row['id'] }}"
                                                        {{ $id ? (in_array($row['id'], $data['diagnosticBreakupItemMergePackage']) ? 'disabled' : '') : '' }}>
                                                        {{ $row['test_name'] }} - {{ $row['test_price'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2 ">
                                            <button type="button" id="addItemButton"
                                                class="btn btn-sm btn-primary text-white"
                                                onclick="getTestList(test_ids.value)">Add
                                                Item</button>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12 col-4">


                                            <div class="row mb-3">
                                                <div class="col-md-3 ">
                                                    <h6 class="h7 labl-hgt">Items</h6>
                                                </div>
                                                <div class="col-md-2 ">
                                                    <h6 class="h7 labl-hgt">Test Code</h6>
                                                </div>
                                                <div class="col-md-2 ">
                                                    <h6 class="h7 labl-hgt">Report Delivery Date</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Charges</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Discount</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Total</h6>
                                                </div>
                                                <div class="col-md-1  text-md-center">
                                                    <h6 class="h7 labl-hgt">IsUrgent</h6>
                                                </div>
                                                <div class="col-md-1  text-md-center">
                                                    <h6 class="h7 labl-hgt">Action</h6>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-8">


                                            <div class="testItemDiv">
                                                @if ($id && count($data['diagnosticBillItem']) > 0)
                                                    @foreach ($data['diagnosticBillItem'] as $key => $row)
                                                        @php
                                                            $test = $row->test;
                                                            $testData = [
                                                                'test' => $row->test,
                                                                'discount' => $row->discount,
                                                                'delivery_date' =>
                                                                    $data['diagnosticBreakupItemGroup'][$key]
                                                                        ->report_delivery_date ?? '',
                                                                'is_urgent' =>
                                                                    $data['diagnosticBreakupItemGroup'][$key]
                                                                        ->is_urgent ?? 0,
                                                                'package_test_list' =>
                                                                    $test->is_package == 2
                                                                        ? $test->packageTest($test->package_test)
                                                                        : [],
                                                            ];
                                                            // dd($testData);
                                                        @endphp
                                                        @include(
                                                            'diagnostic::diagnostic.api.testItemList',
                                                            [
                                                                'data' => $testData,
                                                            ]
                                                        )
                                                    @endforeach
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row HC_div mt-3"
                                        style="{{ $id ? ($data['diagnostic']['type_of_collection'] == 'HC' ? '' : 'display:none;') : 'display:none;' }}">
                                        <h6 class="h6 fw-medium mb-2">Phlebo Visit Charge</h6>
                                        <div class="col-md-3">
                                            <select class="form-select form-select-sm" data-style="py-0"
                                                name="home_collection_range" id="home_collection_range"
                                                onchange="homeCollectionRangeChange(this)">
                                                <option value="">Select Charge</option>
                                                @foreach ($list['distance_list'] as $key => $row)
                                                    <option value="{{ $row['id'] }}"
                                                        data-charge="{{ $row['charges'] }}">
                                                        {{ $row['area_range'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="hidden" class="form-control form-control-sm"
                                                name="home_collection_charge" id="home_collection_charge"
                                                value="{{ $id && count($data['diagnosticBillHC']) > 0 ? $data['diagnosticBillHC'][0]->net_amount : '' }}"
                                                placeholder="Home Collection Charge" readonly>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="hidden" class="form-control form-control-sm"
                                                name="home_collection_quantity" id="home_collection_quantity"
                                                value='{{ $id && isset($data['diagnostic']->hc_quantity_arearange) ? explode(',', $data['diagnostic']->hc_quantity_arearange)[1] ?? 1 : 1 }}'
                                                placeholder="" onkeyup="calculateCharges()"
                                                placeholder="Enter Quantity">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if ($id)
                            <div class="col-md-6  mt-4 mt-md-0  mb-3 mb-md-0 payment">
                                <div class="row">
                                    <div class="col-md-12 mb-2  row  align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Sub Total
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                name="sub_total" id="sub_total"
                                                value="{{ $data['diagnosticBill']->sum('amount') }}" placeholder=" "
                                                readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-2 row align-items-center" id="discount_offer_div"
                                        style="{{ $data['diagnosticBill']->sum('discount') == 0 ? 'display:none;' : '' }}">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">Offered
                                            Discount(₹)</label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                name="discount_offer" id="discount_offer"
                                                value="{{ $data['diagnosticBill']->sum('discount') }}"
                                                placeholder=" " readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-2 row align-items-center" id="actaulRdPointsDiv"
                                        style="{{ $data['reward_redeem'] == 0 ? 'display: none !important;' : '' }}">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">Reward
                                            Points
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm rounded-1"
                                                name="reward_points_final" id="reward_points_final"
                                                value='{{ $data['reward_redeem'] }}' readonly placeholder="">
                                        </div>
                                    </div>
                                    <div class="col-md-12 row mb-2 align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Gross Total
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm pay_in"
                                                name="gross_total" id="payable_amount"
                                                value="{{ $data['diagnosticBill']->sum('net_amount') }}"
                                                placeholder=" " readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-12 row mb-2 align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Paid Amount
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                value="{{ $data['diagnosticPayment']->sum('amount') }}"
                                                placeholder=" " readonly>
                                        </div>
                                    </div>
                                    {{-- <div class="col-md-12  mb-0 row  payment right-six">
                                        <div class="payment_label col-5 col-md-4">
                                            <label for="exampleInputEmail1">
                                                Note
                                            </label>
                                        </div>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm pay_in"
                                                name="remarks" id=""
                                                value="{{ $data['diagnosticPayment']->value('remarks') }}"
                                                placeholder=" ">
                                        </div>
                                    </div> --}}
                                </div>
                            </div>
                            <div class="col-md-6  mt-4 mt-md-0  mb-3 mb-md-0">
                                <div class="row">
                                    <div class="col-md-12 mb-3 d-flex align-items-start">
                                        <div class="copon-wrap row row-cols-md-3 row-cols-2 w-100 justify-content-end">
                                            @if (count($list['offer_list']) > 0)
                                                <div class="campaign-coupon-card  col" style="padding:3px;">
                                                    <div class="form-check p-0">
                                                        <input class="form-check-input btn-check" type="radio"
                                                            name="offered_id" id="offered_id"
                                                            value="{{ $list['offer_list'][0]['offered_id'] }}"
                                                            checked>
                                                        <label for="offered_id"
                                                            class="form-check-label btn p-0 campaign-coupon-card-inner border border-1 border-gray rounded-1 w-100 pe-none">
                                                            <div class="">
                                                                <h3 class="text-gray fw-bold text-center text-uppercase h8 mt-1"
                                                                    style="min-height: 26px;">
                                                                    {{ $list['offer_list'][0]['title'] }}
                                                                </h3>
                                                                <p class="text-center px-1 mb-1 h8"
                                                                    style="min-height: 26px; line-height: 13px;">
                                                                    {{ $list['offer_list'][0]['description'] }}
                                                                </p>
                                                                <div class="offer text-center bg-gray text-center p-0">
                                                                    <h2 class="text-white h5 fw-bold ">
                                                                        {{ $list['offer_list'][0]['rate'] }}&nbsp;{{ $list['offer_list'][0]['rate_type'] == 0 ? '%' : 'Rs' }}
                                                                    </h2>
                                                                </div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @if (!$id)
                            <div class="col-md-12">
                                <div class="row">
                                    <!-----------------Rewards points cards starts here---------------------->
                                    <input type="hidden" name="reward_phone_no" id="reward_phone_no"
                                        value="{{ $list['patient_phone'] }}">
                                    <input type="hidden" id="opening_rdPoints"
                                        value="{{ $list['reward_points'] }}">
                                    <div class="col-lg-4 col-md-5" id="ownPhnoDiv">
                                        <div
                                            class="card card-block card-stretch card-height border border-5 shadow p-2 mb-0  rounded-3">
                                            <div class="card-body px-0 px-md-3">



                                                <h2 class="counter h1 text-primary text-center"
                                                    style="visibility: visible;">
                                                    <svg width="45" viewBox="0 0 24 24" fill="#999"
                                                        xmlns="http://www.w3.org/2000/svg" style="margin-top: -5px;">
                                                        <path fill-rule="evenodd" fill="#999999" clip-rule="evenodd"
                                                            d="M21.9964 8.37513H17.7618C15.7911 8.37859 14.1947 9.93514 14.1911 11.8566C14.1884 13.7823 15.7867 15.3458 17.7618 15.3484H22V15.6543C22 19.0136 19.9636 21 16.5173 21H7.48356C4.03644 21 2 19.0136 2 15.6543V8.33786C2 4.97862 4.03644 3 7.48356 3H16.5138C19.96 3 21.9964 4.97862 21.9964 8.33786V8.37513ZM6.73956 8.36733H12.3796H12.3831H12.3902C12.8124 8.36559 13.1538 8.03019 13.152 7.61765C13.1502 7.20598 12.8053 6.87318 12.3831 6.87491H6.73956C6.32 6.87664 5.97956 7.20858 5.97778 7.61852C5.976 8.03019 6.31733 8.36559 6.73956 8.36733Z">
                                                        </path>
                                                        <path opacity="0.4"
                                                            d="M16.0374 12.2966C16.2465 13.2478 17.0805 13.917 18.0326 13.8996H21.2825C21.6787 13.8996 22 13.5715 22 13.166V10.6344C21.9991 10.2297 21.6787 9.90077 21.2825 9.8999H17.9561C16.8731 9.90338 15.9983 10.8024 16 11.9102C16 12.0398 16.0128 12.1695 16.0374 12.2966Z"
                                                            fill="currentColor"></path>
                                                        <circle cx="18" cy="11.8999" r="1"
                                                            fill="currentColor"></circle>
                                                    </svg>
                                                    <span
                                                        id="opening_rdPoints_text">{{ number_format($list['reward_points'], 2) }}</span>
                                                </h2>
                                                <p class="mb-0 text-dark text-center">Your Available Reward Points
                                                </p>

                                                {{-- <div class="gap-2 justify-content-center align-items-center rewardRedemption"
                                                    style="display: none !important;">
                                                    <p class="mb-0 text-dark text-center" style="">
                                                        Reward redemption requested :
                                                        <span id="rewardRedemptionPoint"></span>
                                                    </p>
                                                    <div class="text-center mt-0" style=" font-size: 0;">
                                                        <button onclick="cancelownphno()"
                                                            title="Cancel redemption request" type="button"
                                                            class="link-primary border-0 bg-transparent fw-bold text-decoration-underline"><svg
                                                                height="18" viewBox="0 0 512 512" width="18"
                                                                xmlns="http://www.w3.org/2000/svg" id="fi_9068699">
                                                                <g id="Layer_2" data-name="Layer 2">
                                                                    <g id="close">
                                                                        <circle id="background" cx="256"
                                                                            cy="256" fill="#f44336" r="256">
                                                                        </circle>
                                                                        <path
                                                                            d="m348.6 391a42.13 42.13 0 0 1 -30-12.42l-62.6-62.58-62.6 62.61a42.41 42.41 0 1 1 -60-60l62.6-62.61-62.61-62.6a42.41 42.41 0 0 1 60-60l62.61 62.6 62.6-62.61a42.41 42.41 0 1 1 60 60l-62.6 62.61 62.61 62.6a42.41 42.41 0 0 1 -30 72.4z"
                                                                            fill="#fff"></path>
                                                                    </g>
                                                                </g>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>

                                                <div class="" id="redeempoints_div">
                                                    <div class="input-group d-flex mt-4 align-items-center">
                                                        <input type="number" class="form-control form-control-sm"
                                                            value="" id="redeem_points"
                                                            placeholder="Enter Reward Points"
                                                            onchange="checkRedeemPoints(this, 0)"
                                                            onkeyup="checkRedeemPoints(this,0)" min="1">
                                                        <div class="" id="redeempointsbuttondiv">
                                                            <button id="reddem_patientPhone"
                                                                onclick="sendOtp(this, 0)" type="button"
                                                                class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end"
                                                                disabled>Redeem
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="" id="otpdiv" style="display: none;">
                                                    <div class="form-group mt-3">
                                                        <div class="input-group mb-3 mt-0">
                                                            <input type="text" class="form-control form-control-sm"
                                                                id="otp" placeholder="Enter OTP"
                                                                value="">
                                                            <div class="" id="checkotpbuttondiv">
                                                                <button onclick="checkOTP(this, 0)" type="button"
                                                                    class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Verify
                                                                    OTP</button>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12 mb-4 mt-2">
                                                            <div class="row justify-content-between">
                                                                <div class="col-auto">
                                                                    <h6 class="d-flex flex-wrap gap-1">
                                                                        Time Remaining
                                                                        <span>:</span>
                                                                        <span class="timerOtpExpire">00:00</span>
                                                                    </h6>
                                                                </div>
                                                                <div class="col-auto">
                                                                    <button type="button"
                                                                        class="text-primary h6 border-0 p-0 bg-transparent resendOTP"
                                                                        onclick="resendOTP()" disabled>
                                                                        Resend OTP
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <h6 class="h6 mt-3 text-primary small text-center"
                                                            id="error_msg" style="display: none;">
                                                            Please Enter Correct OTP
                                                        </h6>
                                                    </div>
                                                </div>
                                                <div class="d-block text-center mt-3">
                                                    <button onclick="checkOtherPhno('{{ $list['patient_phone'] }}')"
                                                        type="button"
                                                        class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Other
                                                        Phone No.</button>
                                                </div> --}}

                                            </div>
                                        </div>
                                    </div>
                                    <!-----------------Rewards points cards ends here---------------------->

                                    <!-----------------Rewards points cards for other No starts here---------------------->
                                    <div class="col-lg-4 col-md-5" id="othPhnoDiv" style="display: none;">
                                        <div
                                            class="card card-block card-stretch card-height border border-5 shadow p-2 mb-0  rounded-3">
                                            <div class="card-body px-0 px-md-3">



                                                <h2 class="counter h1 text-primary text-center"
                                                    style="visibility: visible;">
                                                    <svg width="45" viewBox="0 0 24 24" fill="#999"
                                                        xmlns="http://www.w3.org/2000/svg" style="margin-top: -5px;">
                                                        <path fill-rule="evenodd" fill="#999999" clip-rule="evenodd"
                                                            d="M21.9964 8.37513H17.7618C15.7911 8.37859 14.1947 9.93514 14.1911 11.8566C14.1884 13.7823 15.7867 15.3458 17.7618 15.3484H22V15.6543C22 19.0136 19.9636 21 16.5173 21H7.48356C4.03644 21 2 19.0136 2 15.6543V8.33786C2 4.97862 4.03644 3 7.48356 3H16.5138C19.96 3 21.9964 4.97862 21.9964 8.33786V8.37513ZM6.73956 8.36733H12.3796H12.3831H12.3902C12.8124 8.36559 13.1538 8.03019 13.152 7.61765C13.1502 7.20598 12.8053 6.87318 12.3831 6.87491H6.73956C6.32 6.87664 5.97956 7.20858 5.97778 7.61852C5.976 8.03019 6.31733 8.36559 6.73956 8.36733Z">
                                                        </path>
                                                        <path opacity="0.4"
                                                            d="M16.0374 12.2966C16.2465 13.2478 17.0805 13.917 18.0326 13.8996H21.2825C21.6787 13.8996 22 13.5715 22 13.166V10.6344C21.9991 10.2297 21.6787 9.90077 21.2825 9.8999H17.9561C16.8731 9.90338 15.9983 10.8024 16 11.9102C16 12.0398 16.0128 12.1695 16.0374 12.2966Z"
                                                            fill="currentColor"></path>
                                                        <circle cx="18" cy="11.8999" r="1"
                                                            fill="currentColor"></circle>
                                                    </svg>
                                                    <span id="rdPoints_otherNo">0.00</span>
                                                </h2>
                                                <p class="mb-0 text-dark text-center">Your Available Reward Points
                                                </p>
                                                <div class="justify-content-center flex-wrap">
                                                    <p id="otherphno"
                                                        class="mb-0 text-dark text-center rewardOtherShow"
                                                        style="display: none;">Phone
                                                        Number :
                                                        <span id="otherno_for_rdpoints"></span>
                                                    </p>

                                                    <div class="gap-2 justify-content-center align-items-center rewardOtherShow rewardRedemption_other"
                                                        style="display: none;">
                                                        <p class="mb-0 text-dark text-center"
                                                            id="frezeRdpointsptagothNo" style=" ">
                                                            Reward redemption requested : <span
                                                                id="rewardRedemptionPoint_other"></span>
                                                        </p>
                                                        <div class="text-center mt-0" id="cancelothno"
                                                            style="font-size: 0;">
                                                            <button onclick="cancelothphno()" type="button"
                                                                title="Cancel redemption request"
                                                                class="link-primary border-0 bg-transparent fw-bold text-decoration-underline"><svg
                                                                    height="18" viewBox="0 0 512 512"
                                                                    width="18" xmlns="http://www.w3.org/2000/svg"
                                                                    id="fi_9068699">
                                                                    <g id="Layer_2" data-name="Layer 2">
                                                                        <g id="close">
                                                                            <circle id="background" cx="256"
                                                                                cy="256" fill="#f44336" r="256">
                                                                            </circle>
                                                                            <path
                                                                                d="m348.6 391a42.13 42.13 0 0 1 -30-12.42l-62.6-62.58-62.6 62.61a42.41 42.41 0 1 1 -60-60l62.6-62.61-62.61-62.6a42.41 42.41 0 0 1 60-60l62.61 62.6 62.6-62.61a42.41 42.41 0 1 1 60 60l-62.6 62.61 62.61 62.6a42.41 42.41 0 0 1 -30 72.4z"
                                                                                fill="#fff"></path>
                                                                        </g>
                                                                    </g>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>



                                                <div class="" id="redeempoints_otherNo_div">
                                                    <div class="input-group d-flex mt-4 align-items-center">
                                                        <!-- <label for="email" class="form-label h6 fw-normal">Reward Points</label> -->
                                                        <input type="number" class="form-control form-control-sm"
                                                            value="" id="other_phone_no"
                                                            placeholder="Enter Another Number">
                                                        <div class="" id="redeempointsbuttondiv">
                                                            <button onclick="checkRewardPointsOther(this)"
                                                                type="button"
                                                                class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Check</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="rewardOtherShow" id="redeempoints_div_other"
                                                    style="">
                                                    <div class="input-group d-flex mt-4 align-items-center">
                                                        <!-- <label for="email" class="form-label h6 fw-normal">Reward Points</label> -->
                                                        <input type="number" class="form-control form-control-sm"
                                                            value="" id="redeem_points_other_no"
                                                            placeholder="Enter Reward Points"
                                                            onchange="checkRedeemPoints(this, 1)"
                                                            onkeyup="checkRedeemPoints(this,1)" min="1">
                                                        <div class="" id="redeempointsbuttondiv">
                                                            <button id="reddem_otherPhone" onclick="sendOtp(this, 1)"
                                                                type="button"
                                                                class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Redeem</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="rewardOtherShow" id="otpdiv_other" style="">
                                                    <div class="form-group mt-3">
                                                        <!-- <label for="email" class="form-label h6">OTP</label> -->
                                                        <div class="input-group mb-3 mt-0">
                                                            <input type="text" class="form-control form-control-sm"
                                                                id="otp_otherNo" placeholder="Enter OTP"
                                                                value="">
                                                            <div class="" id="checkotpbuttondivOtherNo">
                                                                <button onclick="checkOTP(this, 1)" type="button"
                                                                    class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Verify
                                                                    OTP</button>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12 mb-4 mt-2">
                                                            <div class="row justify-content-between">
                                                                <div class="col-auto">
                                                                    <h6 class="d-flex flex-wrap gap-1">
                                                                        Time Remaining
                                                                        <span>:</span>
                                                                        <span class="timerOtpExpire_other">00:00</span>
                                                                    </h6>
                                                                </div>
                                                                <div class="col-auto">
                                                                    <button type="button"
                                                                        class="text-primary h6 border-0 p-0 bg-transparent resendOTP_other"
                                                                        onclick="resendOTPOther()" disabled>
                                                                        Resend OTP
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <h6 class="h6 mt-3 text-primary small text-center"
                                                            id="error_msg_other" style="display: none;">
                                                            Please Enter Correct OTP
                                                        </h6>
                                                    </div>
                                                </div>
                                                <div class="d-block text-center mt-3">
                                                    <button
                                                        onclick="checkOwnPhno('{{ $list['patient_phone'] }}','{{ $list['reward_points'] }}')"
                                                        type="button"
                                                        class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Patient
                                                        Phone No.</button>
                                                </div>
                                                <div class="d-block text-center mt-1">
                                                    <button onclick="checkOtherPhno('{{ $list['patient_phone'] }}')"
                                                        type="button"
                                                        class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Try
                                                        Another Phone No.</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-----------------Rewards points cards  cards for other No ends here---------------------->

                                    <div class="col-md-7 offset-md-1  mt-4 mt-md-0  mb-3 mb-md-0 payment">
                                        <div class="row">
                                            <div class="col-md-12 mb-3 d-flex align-items-start">
                                                <div class="copon-wrap row row-cols-md-4 row-cols-2 w-100">
                                                    <input type="hidden" name="offered_type" id="offered_type"
                                                        value="0">
                                                    @include('diagnostic::diagnostic.api.offerList', [
                                                        'offer_list' => $list['offer_list'],
                                                    ])
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12 mb-2  row  align-items-center">
                                                <label for="exampleInputEmail1" class="col-5 col-md-4">
                                                    Sub Total
                                                </label>
                                                <div class="col-7 col-md-2 pe-md-0">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="sub_total" id="sub_total" value=''
                                                        placeholder=" " readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-12 mb-2 row align-items-center" id="discount_offer_div"
                                                style="display:none;">
                                                <label for="exampleInputEmail1" class="col-5 col-md-4">Offered
                                                    Discount(₹)</label>
                                                <div class="col-7 col-md-2 pe-md-0">
                                                    <input type="text" class="form-control form-control-sm"
                                                        name="discount_offer" id="discount_offer" value=''
                                                        placeholder=" " readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-12 mb-2 row align-items-center" id="actaulRdPointsDiv"
                                                style="display: none !important;">
                                                <label for="exampleInputEmail1" class="col-5 col-md-4">Reward
                                                    Points
                                                </label>
                                                <div class="col-7 col-md-2 pe-md-0">
                                                    <input type="text"
                                                        class="form-control form-control-sm rounded-1"
                                                        name="reward_points_final" id="reward_points_final"
                                                        value='0' readonly placeholder="">
                                                </div>
                                            </div>
                                            <div class="col-md-12 row mb-2 align-items-center">
                                                <label for="exampleInputEmail1" class="col-5 col-md-4">
                                                    Gross Total
                                                </label>
                                                <div class="col-7 col-md-2 pe-md-0">
                                                    <input type="text" class="form-control form-control-sm pay_in"
                                                        name="gross_total" id="payable_amount" value=''
                                                        placeholder=" " readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-12  mb-0 row  payment right-six">
                                                <div class="payment_label col-5 col-md-4">
                                                    <label for="exampleInputEmail1">
                                                        Note
                                                    </label>
                                                </div>
                                                <div class="col-7 col-md-7 ">
                                                    <input type="text" class="form-control form-control-sm pay_in"
                                                        name="remarks" id="" value=''
                                                        placeholder=" ">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    @if (!$id)
                        {{-- Payment Mode --}}
                        @include('billing::billing.api.paymentForm', [
                            // 'payment_modes' => config('billing.payment_mode'),
                            'due' => 0,
                            'min_payment_percentage' => 0,
                            // 'sub_text' => 'At least 40% amount need to clear for clearing the bill.',
                        ])
                    @endif
                    <!-- Phelebo assignment table starts here -->
                    <div class="row justify-content-end align-items-center HC_div" style="margin-top: 60px;">
                        <div class="col-12 col-md-6 mb-4">
                            <h4 class="fw-bold">Schedule Collection</h4>
                        </div>
                        <div class="col-12 col-md-6 mb-4">
                            <div class="w-100" id="phlebohcassigndiv">
                                <div class="d-flex justify-content-end gap-2">
                                    <button type="button" onclick="refresh()" id=""
                                        class="btn btn-sm btn-primary text-dark fw-normal bg-transparent border-0">Refresh
                                        <svg id="fi_8391685" class="ms-1" height="20" viewBox="0 0 32 32"
                                            width="20" xmlns="http://www.w3.org/2000/svg" data-name="Layer 1">
                                            <path
                                                d="m16 32a16 16 0 1 1 11.733-26.878 1 1 0 1 1 -1.466 1.359 14 14 0 1 0 3.733 9.519 1 1 0 0 1 2 0 16.019 16.019 0 0 1 -16 16z">
                                            </path>
                                            <path d="m27 7a1 1 0 0 1 -1-1v-5a1 1 0 0 1 2 0v5a1 1 0 0 1 -1 1z"></path>
                                            <path d="m27 7h-5a1 1 0 0 1 0-2h5a1 1 0 0 1 0 2z"></path>
                                        </svg>
                                    </button>
                                    <button type="button" onclick="assignphlebo()" id="assignphlebobtn"
                                        class="btn btn-sm btn-gray text-white">
                                        Load Phelebo
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center text-md-start" id="assignphleboheaderclass"
                            style="display: none;">
                            <div class="d-inline-block bg-light rounded-2 w-auto">
                                <button type="button" onclick="previousDate(this)" id="prevDate"
                                    class="btn btn-primary text-primary fw-bold bg-transparent border-0 px-2"><svg
                                        style="margin-top: -2px;" id="fi_2985161" enable-background="new 0 0 128 128"
                                        height="25" viewBox="0 0 128 128" width="25"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path id="Left_Arrow_4_"
                                            d="m84 108c-1.023 0-2.047-.391-2.828-1.172l-40-40c-1.563-1.563-1.563-4.094 0-5.656l40-40c1.563-1.563 4.094-1.563 5.656 0s1.563 4.094 0 5.656l-37.172 37.172 37.172 37.172c1.563 1.563 1.563 4.094 0 5.656-.781.781-1.805 1.172-2.828 1.172z"
                                            fill="#d01337"></path>
                                    </svg> Prev</button>
                                <button type="button" id="colectiondateforcalender"
                                    class="btn btn-primary text-dark bg-transparent border-0 px-0"></button>
                                <button type="button" onclick="nextDate(this)"
                                    class="btn btn-primary text-primary fw-bold bg-transparent border-0 px-2">Next <svg
                                        id="fi_2985179" style="margin-top: -2px;" enable-background="new 0 0 128 128"
                                        height="25" viewBox="0 0 128 128" width="25"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path id="Right_Arrow_4_"
                                            d="m44 108c-1.023 0-2.047-.391-2.828-1.172-1.563-1.563-1.563-4.094 0-5.656l37.172-37.172-37.172-37.172c-1.563-1.563-1.563-4.094 0-5.656s4.094-1.563 5.656 0l40 40c1.563 1.563 1.563 4.094 0 5.656l-40 40c-.781.781-1.805 1.172-2.828 1.172z"
                                            fill="#d01337"></path>
                                    </svg></button>
                            </div>
                        </div>
                        <div class="col-md-4 mt-3 mt-md-0" id="assignphlebotextclass" style="display: none;">
                            <h4 class="fs-4 text-center">Collection Schedule</h4>
                        </div>
                        <div class="col-md-4"></div>
                    </div>


                    <div id="assignphlebotableclass" style="display: none;">
                        <div class="schedule-table table-responsive">
                            <table class="table ">
                                <thead>
                                    <tr>

                                        <th>Available Phlebotomist</th>
                                        <th>7 A.M. - 9 A.M.</th>
                                        <th>9 A.M. - 11 A.M.</th>
                                        <th>11 A.M. - 1 P.M.</th>
                                        <th>1 P.M. - 3 P.M.</th>
                                        <th>3 P.M. - 5 P.M.</th>
                                        <th>5 P.M. - 7 P.M.</th>
                                    </tr>
                                </thead>
                                <tbody id="phlebo_table_body">


                                </tbody>
                            </table>



                        </div>

                        <div class="d-flex justify-content-between mt-4" id="tablefooter">
                            <div>
                                <div class="hideAllLabel" id="selectslotlevel" style="display: none;">Selected
                                    collection Slot : <span id="selectedslotspan" class="d-block d-md-inline"
                                        style="font-weight: bold;"></span></div>
                                <div class="hideAllLabel" id="notselectedlotlevel"><span id="notselectedslotspan"
                                        class="text-primary" style="font-weight: bold;  "></span></div>
                            </div>
                            <!-- <button type="button" onclick="checkphleboslot()" id="assigned" class="btn btn-sm btn-primary text-white" style="width: 170px;">Assign</button> -->
                            <button type="button" onclick="leaveunassignedbutton()" id="leaveunassigned"
                                class="btn btn-sm btn-primary text-white" style="width: 170px;">Leave
                                Unassigned</button>

                        </div>
                    </div>

                    <!-- Phelebo assignment table ends here -->
                    <div class="row">
                        <div class="modal-footer">
                            <button type="submit" id="formSubmit" style="display: none;"></button>
                            <button type="button" id="submitAssignBtn" onclick="return checkPhleboSlot()"
                                class="btn btn-primary text-white" style="display: none;">Submit</button>
                        </div>
                        <div class="form-group col-md-12">
                            <div id="errorMessage" class="" style="color: red;"></div>
                        </div>
                    </div>
                </div>

                <!------------Top card ends here----------->
            </div>
        </div>
</form>
<script>
    $(".HC_div").show();
</script>
@if (!empty($list['source_list']))
    <script>
        var source_list = {!! json_encode($list['source_list']) !!};
        // console.log(source_list);
        // $('#search_patient').val('{{ $list['patient_phone'] }}');
        $("#source_id").val(source_list.source_id);
        $("#source_type").val(source_list.source_type);
        $("#source_test_id").val(source_list.source_test_id);
        $("#clinic_id").val(source_list.source_clinic_id);
        // console.log(source_list.source_test_id);
        $.each(source_list.source_test_id, function(key, val) {
            getTestList(val);
        });
    </script>
@endif
