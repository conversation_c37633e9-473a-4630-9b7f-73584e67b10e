<form class="clearfix" method="post"
    action="<?php echo e($id ? config('appointment.url') . 'update/' . $id : config('appointment.url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <?php if(!$id): ?>
        <input type="hidden" name="patient_id" value="<?php echo e($data['patient_detail']->id); ?>">
        <input type="hidden" name="patient_phone" value="<?php echo e($phone); ?>">
    <?php endif; ?>
    <div class="row" data-select2-id="select2-data-16-ukk2">
        <div class="col-sm-12" data-select2-id="select2-data-15-62ts">
            <!------------Top card starts here----------->
            <div class="card  mb-3" data-select2-id="select2-data-14-bl8l">
                <?php if(!$id): ?>
                    <div class="card-header border-bottom p-4" style="background-color: #fff8f8;">
                        <div class=" row">
                            <!--Form header starts here-->
                            <div class="col-md-12 mb-3 mb-md-0">
                                <div class="d-flex flex-wrap justify-content-between">
                                    <div class="d-flex gap-4 flex-wrap">
                                        <div class="d-flex align-items-center me-5">
                                            <h6 class="h5 fw-bold text-primary">
                                                <span><?php echo e($data['patient_detail']->name); ?></span>
                                            </h6> &nbsp;<span class="h7" style="margin-top: 4px;">
                                                (<span><?php echo e($data['patient_detail']->sex); ?></span>/<span><?php echo e(Helper::ageCalculator($data['patient_detail']->birthdate)); ?></span>)</span>
                                        </div>
                                        <div class="d-flex align-items-center  justify-content-between flex-wrap">
                                            
                                            <p class=" text-dark h7 me-3 mb-0">
                                                <!--<span class="text-dark fw-bold">Phone:</span>-->
                                                <svg id="fi_159832" height="15" fill="#d01337" style="    margin-top: -3px;"
                                                    version="1.1" viewBox="0 0 482.6 482.6" x="0px" xml:space="preserve"
                                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                                    y="0px">
                                                    <g>
                                                        <path d="M98.339,320.8c47.6,56.9,104.9,101.7,170.3,133.4c24.9,11.8,58.2,25.8,95.3,28.2c2.3,0.1,4.5,0.2,6.8,0.2
                                            c24.9,0,44.9-8.6,61.2-26.3c0.1-0.1,0.3-0.3,0.4-0.5c5.8-7,12.4-13.3,19.3-20c4.7-4.5,9.5-9.2,14.1-14
                                            c21.3-22.2,21.3-50.4-0.2-71.9l-60.1-60.1c-10.2-10.6-22.4-16.2-35.2-16.2c-12.8,0-25.1,5.6-35.6,16.1l-35.8,35.8
                                            c-3.3-1.9-6.7-3.6-9.9-5.2c-4-2-7.7-3.9-11-6c-32.6-20.7-62.2-47.7-90.5-82.4c-14.3-18.1-23.9-33.3-30.6-48.8
                                            c9.4-8.5,18.2-17.4,26.7-26.1c3-3.1,6.1-6.2,9.2-9.3c10.8-10.8,16.6-23.3,16.6-36s-5.7-25.2-16.6-36l-29.8-29.8
                                            c-3.5-3.5-6.8-6.9-10.2-10.4c-6.6-6.8-13.5-13.8-20.3-20.1c-10.3-10.1-22.4-15.4-35.2-15.4c-12.7,0-24.9,5.3-35.6,15.5l-37.4,37.4
                                            c-13.6,13.6-21.3,30.1-22.9,49.2c-1.9,23.9,2.5,49.3,13.9,80C32.739,229.6,59.139,273.7,98.339,320.8z M25.739,104.2
                                            c1.2-13.3,6.3-24.4,15.9-34l37.2-37.2c5.8-5.6,12.2-8.5,18.4-8.5c6.1,0,12.3,2.9,18,8.7c6.7,6.2,13,12.7,19.8,19.6
                                            c3.4,3.5,6.9,7,10.4,10.6l29.8,29.8c6.2,6.2,9.4,12.5,9.4,18.7s-3.2,12.5-9.4,18.7c-3.1,3.1-6.2,6.3-9.3,9.4
                                            c-9.3,9.4-18,18.3-27.6,26.8c-0.2,0.2-0.3,0.3-0.5,0.5c-8.3,8.3-7,16.2-5,22.2c0.1,0.3,0.2,0.5,0.3,0.8
                                            c7.7,18.5,18.4,36.1,35.1,57.1c30,37,61.6,65.7,96.4,87.8c4.3,2.8,8.9,5,13.2,7.2c4,2,7.7,3.9,11,6c0.4,0.2,0.7,0.4,1.1,0.6
                                            c3.3,1.7,6.5,2.5,9.7,2.5c8,0,13.2-5.1,14.9-6.8l37.4-37.4c5.8-5.8,12.1-8.9,18.3-8.9c7.6,0,13.8,4.7,17.7,8.9l60.3,60.2
                                            c12,12,11.9,25-0.3,37.7c-4.2,4.5-8.6,8.8-13.3,13.3c-7,6.8-14.3,13.8-20.9,21.7c-11.5,12.4-25.2,18.2-42.9,18.2
                                            c-1.7,0-3.5-0.1-5.2-0.2c-32.8-2.1-63.3-14.9-86.2-25.8c-62.2-30.1-116.8-72.8-162.1-127c-37.3-44.9-62.4-86.7-79-131.5
                                            C28.039,146.4,24.139,124.3,25.739,104.2z">
                                                        </path>
                                                    </g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                    <g></g>
                                                </svg>
                                                <span><?php echo e(Helper::maskPhoneNumber($list['patient_phone'])); ?></span>
                                            </p>
                                            <!-- <p class="mb-1 text-gray">Age: </p> -->
                                            <span>
                                            </span>
                                            <?php if($service): ?>
                                                <p class="text-dark h7 mb-0">
                                                    <svg height="18" width="20" style="margin-top: -2px;" fill="#d01337"
                                                        clip-rule="evenodd" fill-rule="evenodd" id="fi_9720867" stroke-linejoin="round"
                                                        stroke-miterlimit="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <g id="Icon">
                                                            <path
                                                                d="m7.035 8.641 3.996-4.58c.244-.28.598-.441.969-.441s.725.161.969.441l3.996 4.58 3.813-2.408c.427-.269.972-.264 1.394.014s.642.778.562 1.277l-1.812 11.322c-.141.884-.904 1.534-1.798 1.534h-14.248c-.894 0-1.657-.65-1.798-1.534l-1.812-11.322c-.08-.499.14-.999.562-1.277s.967-.283 1.394-.014zm-4.213-.886 1.737 10.854c.025.156.159.271.317.271h14.248c.158 0 .292-.115.317-.271l1.737-10.854-3.567 2.252c-.536.339-1.239.236-1.656-.241l-3.955-4.534-3.955 4.534c-.417.477-1.12.58-1.656.241l-3.567-2.252z">
                                                            </path>
                                                            <path
                                                                d="m20.037 15.129c.414 0 .75.336.75.75s-.336.75-.75.75h-16.074c-.414 0-.75-.336-.75-.75s.336-.75.75-.75z">
                                                            </path>
                                                        </g>
                                                    </svg>

                                                    <?php echo e($service); ?>: Active <span>
                                                    </span>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--Form header ends here-->
                        </div>
                    </div>
                <?php endif; ?>
                <div class="card-body pb-3" data-select2-id="select2-data-13-l6y1">
                    <div class="row">
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label fw-bold">Doctor <b class="text-primary">*</b></label>
                            <select id="doctor_id" name="doctor_id" data-show="clinic_id" class="select2-multpl-custom1 form-select"
                                data-style="py-0" onchange="changeDoctor(this)">
                                <option value="">Select</option>
                                <?php $__currentLoopData = $list['doctor_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($row->id); ?>"
                                        <?php echo e($id ? ($data['doctor_id'] == $row->id ? 'selected' : '') : ''); ?>>Dr. <?php echo e($row->username); ?>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label fw-bold">Clinic <b class="text-primary">*</b></label>
                            <select id="clinic_id" name="clinic_id" data-show="date" class="select2-multpl-custom1 form-select"
                                data-style="py-0" onchange="changeClinic(this)">
                                <option value="">Select</option>
                                <?php if($id): ?>
                                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row['clinic_id']); ?>"
                                            <?php echo e($id ? ($data['clinic_id'] == $row['clinic_id'] ? 'selected' : '') : ''); ?>>
                                            <?php echo e($row['name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label fw-bold">Date <b class="text-primary">*</b></label>
                            <select id="date" name="date" data-show="time_slot" class="select2-multpl-custom1 form-select"
                                data-style="py-0" onchange="changeDate(this)">
                                <option value="">Select</option>
                                <?php if($id): ?>
                                    <?php
                                        $dates = array_column($list['date_list'], 'name');
                                    ?>
                                    <?php if(!in_array($data['date'], $dates)): ?>
                                        <option value="<?php echo e($data['date']); ?>" selected><?php echo e($data['date']); ?></option>
                                    <?php endif; ?>
                                    <?php $__currentLoopData = $dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row); ?>"
                                            <?php echo e($id ? ($data['date'] == $row ? 'selected' : '') : ''); ?>>
                                            <?php echo e($row); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label fw-bold">Available Slots <b
                                    class="text-primary">*</b></label>
                            <select id="time_slot" name="time_slot" class="select2-multpl-custom1 form-select" data-style="py-0">
                                <option value="">Select</option>
                                <?php if($id): ?>
                                    <?php $__currentLoopData = $list['time_slot_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option
                                            value="<?php echo e($row['name']); ?><?php echo e(isset($row['schedule_id']) ? ',' . $row['schedule_id'] : ''); ?>"
                                            <?php echo e($id ? ($data['time_slot'] == $row['name'] ? 'selected' : '') : ''); ?>>
                                            <?php echo e($row['name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <?php if(!$id): ?>
                            <div class="form-group col-lg-3 col-md-6 col-12">
                                <label for="exampleInputEmail1" class="fw-bold">
                                    Appointment Status <b class="text-primary">*</b>
                                </label>
                                <select class="form-select form-select-sm m-bot15" name="status" id="status">
                                    <?php $__currentLoopData = $list['status_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($row); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="form-group col-lg-3 col-md-6 col-12">
                                <label for="exampleInputEmail1" class="fw-bold">
                                    Remarks <b class="text-primary">*</b>
                                </label>
                                <input type="text" class="form-control form-control-sm" name="remarks" id="exampleInputEmail1"
                                    value='' placeholder="">
                            </div>
                            <div class="form-group col-lg-3 col-md-6 col-12">
                                <label for="exampleInputEmail1" class="fw-bold">Appointment Type <b
                                        class="text-primary">*</b></label>
                                <select class="form-select form-select-sm m-bot15" name="appointment_type" id="appointment_type">
                                    <?php $__currentLoopData = $list['appointment_type_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($row); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        <?php endif; ?>
                        <div class="form-group col-md-12">
                            <button type="submit" name="submit" class="btn btn-primary text-white">Submit</button>
                        </div>
                        <div class="form-group col-md-12">
                            <div id="errorMessage" class="" style="color: red;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/addEdit.blade.php ENDPATH**/ ?>