<?php

namespace Modules\Authorization\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Modules\Users\Services\UserService;
use Modules\Doctor\Services\DoctorService;
use Modules\Patient\Services\PatientService;
use Modules\Appointment\Services\AppointmentService;
use Modules\Prescription\Services\PrescriptionService;
use Modules\Billing\Services\BillingService;
use Modules\Diagnostic\Services\DiagnosticService;
use App\Services\SMSMsgService;
use App\Services\ImageUploadService;
use Modules\Users\Models\User;
use Modules\Reward\Models\VerifyOtp;
use App\Models\Role;
use Validator;
use Illuminate\Support\Facades\Auth;
use DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class AuthorizationController extends Controller
{
    private $userService;
    private $doctorService;
    private $patientService;
    private $appointmentService;
    private $prescriptionService;
    private $billingService;
    private $diagnosticService;
    private $smsMsgService;
    private $imageUploadService;
    private $user;
    private $userLogs;
    private $otpExpire;
    private $redirectDashboard;

    public function __construct(UserService $userService,DoctorService $doctorService,PatientService $patientService,AppointmentService $appointmentService,PrescriptionService $prescriptionService,BillingService $billingService, SMSMsgService $smsMsgService, ImageUploadService $imageUploadService, DiagnosticService $diagnosticService)
    {
        $this->middleware('auth:api', ['except' => ['login', 'forgotPassword', 'resetPassword', 'register','refresh','otpLogin','otpVerifyLogin']]);
        $this->userService = $userService;
        $this->doctorService = $doctorService;
        $this->patientService = $patientService;
        $this->appointmentService = $appointmentService;
        $this->prescriptionService = $prescriptionService;
        $this->billingService = $billingService;
        $this->diagnosticService = $diagnosticService;
        $this->smsMsgService = $smsMsgService;
        $this->imageUploadService = $imageUploadService;
        $this->otpExpire = env('OTP_EXPIRE_MINUTES');//minutes
        $this->redirectDashboard = route('user.dashboard');
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = $validator->validated();
        $data['status'] = 1;
        // dd($data);
        if (!$token = auth()->attempt($data)) {
            return response()->json(['error' => 'Incorrect Login'], 401);
        }
        // $token_p = auth()->guard('patient')->attempt($validator->validated());
        $role = $this->getUserRole();
        switch ($role->id) {
            case 10://Customer Care
                $this->redirectDashboard = route('appointment.index',['all']);
                break;
            default:
                $this->redirectDashboard = route('user.dashboard');
                break;
        }
        $cur_time = $this->userService->getCurrentDateTime();
        $request->merge([
            'user_id' => $this->createdBy(),
            'role' => 'admin',
            'ip_address' => $this->getIp(),
            'created_at' => $cur_time,
            'updated_at' => $cur_time
        ]);
        $this->userService->setRequest($request->except('email','password'));
        $this->user = $this->userService->setEntity(auth()->user());
        $this->userService->addUserLogs();
        return $this->createNewToken($token);
    }
    public function forgotPassword(Request $request)
    {
        try {
            // dd($request->all());
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
            $data = $validator->validated();
            $user = $this->userService->findByOtherId('email',$data['email']);
            if (!isset($user)) {
                return response()->json(['error' => 'This email is not registered in our system.'], 401);
            }
            $token = Hash::make(rand(1000, 9999));
            $forgotPasswordUrl = route('resetPassword', [$token]);
            $resetData = DB::table('password_reset_tokens')->where('email', $data['email'])->first();
            if (!isset($resetData)) {
                DB::table('password_reset_tokens')->insert([
                    'email' => $data['email'],
                    'token' => $token,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            else {
                DB::table('password_reset_tokens')->where('email', $data['email'])->update([
                    'token' => $token,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            $this->userService->sendMail($data['email'], $forgotPasswordUrl,$user);
            $this->response['success']  = true;
            $this->response['message']  = 'Password reset request sent successfully.';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getMessage()], 401);
        }
    }
    public function resetPassword(Request $request, $token)
    {
        try {
            $request->validate([
                'password' => 'required|min:6|confirmed',
            ]);
            $resetData = DB::table('password_reset_tokens')->where('token', $token)->first();
            if (!isset($resetData)) {
                return response()->json(['error' => 'Invalid request.'], 401);
            }
            $expired_time = Carbon::parse($resetData->created_at)->addMinutes(config('auth.passwords.users.expire'));
            if (Carbon::parse($expired_time)->isPast()) {
                return response()->json(['error' => 'Page expired.'], 401);
            }
            // dd($resetData);
            $user = $this->userService->findByOtherId('email',$resetData->email);
            $user->password = Hash::make($request->password);
            $user->save();
            $this->response['success']  = true;
            $this->response['message']  = 'Password reset successfully.';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Throwable $th) {
            return response()->json(['error' => $th->getMessage()], 401);
        }
    }
    public function otpLogin(Request $request)
    {
        $validator = Validator::make($request->except('stat'), [
            'phone' => 'required|digits:10'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $validator->validated();
        $phone = $request->phone;
        $otp = rand(1000, 9999);
        $user = $this->userService->findByOtherId('phone',$phone);
        if (!isset($user)) {
            return response()->json(['error' => 'This phone number is not registered in our system.'], 401);
        }
        // $this->userService->findById($user->id);
        // $this->userService->setRequest(['otp' => $otp]);
        // $this->userService->update();
        $created_at = date('Y-m-d H:i:s');
        $validated_at = date('Y-m-d H:i:s', strtotime('+'.$this->otpExpire.' minutes'));
        $data = [
            'phone' => $phone,
            'type' => 'Login',
            'otp' => $otp,
            'created_by' => $user->id,
            'created_at' => $created_at,
            'validated_at' => $validated_at,
            'status' => 0
        ];
        // dd($user);
        VerifyOtp::create($data);

        // send sms for reward points otp
        $this->smsMsgService->setFlowId('656edb27d6fc050f1e70af33');
        $this->smsMsgService->sendSmsToRecipients($phone);
        $variable = [
            'OTP' => $otp
        ];
        $this->smsMsgService->setVariable($variable);
        $data = $this->smsMsgService->send();
        $req_data = [
            'created_by' => $user->id,
            'event' => 'Login OTP',
            'campaign_name' => 'Sent OTP for clinic based login',
            'status' => 2,
            'reason' => ''
        ];
        $this->smsMsgService->add($req_data);
        // dd($data);
        $this->response['success']  = true;
        $this->response['message']  = 'OTP Sent!';
        $this->response['data']     = [
            'user' => $user,
            'role' => implode(',',$user->getRoleNames()->toArray()),
            'otp_expire' => $this->otpExpire
        ];
        return response()->json($this->response);
    }
    public function otpVerifyLogin(Request $request)
    {
        // dd(auth()->user());
        $validator = Validator::make($request->except('stat'), [
            'phone' => 'required|digits:10',
            'otp' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $data = [];
        // dd($data);
        $data['phone'] = $request->phone;
        $data['otp'] = $request->otp;

        $user = $this->userService->findByOtherId('phone',$data['phone']);
        if (!isset($user)) {
            return response()->json(['error' => 'This phone number is not registered in our system.'], 401);
        }
        $verifyOtp = VerifyOtp::where([
            'phone' => $data['phone'],
            'otp' => $data['otp'],
            'status' => 0,
            'type' => 'Login',
        ])->orderBy('id', 'desc')->first();//->limit(1)->update(['status' => 1]);
        
        if ($request->has('otp')) {
            if (!$verifyOtp) {
                return response()->json(['error' => 'Invalid OTP'], 401);
            }
            if (Carbon::parse($verifyOtp->validated_at)->isPast()) {
                return response()->json(['error' => 'Your OTP has expired.'], 401);
            }
            // dd($verifyOtp);
            auth()->login($user);
            if (!$token = auth()->tokenById($user->id)) {
                return response()->json(['error' => 'Incorrect Login'], 401);
            }
            // Clear OTP after successful login
            VerifyOtp::where('id', $verifyOtp->id)->update([
                'status' => 1,
                'modified_by' => $user->id,
            ]);
            // dd($token);
            return $this->createNewToken($token);
        }
        return response()->json(['message' => 'Incorrect Login'], 400);
    }
    public function refresh()
    {
        try {
            // sleep(1); // simulate processing time
            return $this->createNewToken(auth()->refresh(),true);
        } catch (\Exception $e) {
            $this->response['title'] = 'Session Expired';
            $this->response['text'] = 'You have been logged out due to inactivity.';
            $this->response['icon'] = 'warning';
            return response()->json($this->response);
        }
    }
    public function register(Request $request)
    {
        // dd($request->name);
        try {
            $validator = Validator::make($request->all(), [
                'username' => 'required|string|between:2,100',
                'email' => 'required|string|email|max:100|unique:users',
                'password' => 'required|confirmed|min:6',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            // dd($validator->validated());
            $this->user = User::create(
                array_merge(
                    $validator->validated(),
                    ['phone' => $request->phone],
                    ['active' => $request->active],
                    ['password' => Hash::make($request->password)]
                )
            );

            $this->response['success'] = true;
            $this->response['message'] = 'User successfully registered !';
            $this->response['data'] = $this->user;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success'] = false;
            $this->response['message'] = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function logout()
    {
        try {
            auth()->logout();
            $this->response['success'] = true;
            $this->response['message'] = 'User successfully signed out!';
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success'] = false;
            $this->response['message'] = $e->getMessage();
            return response()->json($this->response);
        }
    }
    protected function createNewToken($token,$status=null)
    {
        // $login_ex_min = env('LOGIN_EXPIRE_MINUTES');
        $user = auth()->user();
        $permission='';
        if (isset($status)) {
            $role = $this->getUserRole();
            // if sidebar module is not there
            switch ($role->id) {
                case 9://Agent
                    $permissionPage = [];
                    break;
                default:
                    $permissionPage = $this->getPermissionList();
                    break;
            }
            $permission = view('admin.custom.permissionSideber',compact('permissionPage'))->render();
        }

        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 60,
            'user' => $user,
            'permission' => $permission,
            'redirect' => $this->redirectDashboard
        ]);
    }
    public function dashboardCount(Request $request)
    {
        try {
            $this->response['success']  = true;
            $created_by = $this->createdBy();
            $role = $this->getUserRole();
            $permissionPage = $this->getPermissionList();
            if ($role->id == 9) {// Agent
                $data = [
                    'clinic' => $this->appointmentService->allClinics()->toArray(),
                ];
                $this->response['headerAction'] = view('authorization::admin.api.agentDashboard',compact('permissionPage','data'))->render();
            }
            else if ($role->id == 7) {// Phlebotomist
                $data = [
                    
                ];
                $this->response['headerAction'] = view('authorization::admin.api.phleboDashboard',compact('permissionPage','data'))->render();
            }
            else {
                // dd($created_by);
                // $doctor = $this->doctorService->entity->where('status',1);
                $doctor = $this->userService->allUserWithRole('Doctor');
                $patient = $this->patientService->entity->where('status',1);
                $appointment = $this->appointmentService->entity;
                $prescription = $this->prescriptionService->entity->where('status',1);
                $test_collection = $this->diagnosticService->entity;
                $membership = $this->billingService->entity->whereNotIn('status',[1,2]);
                switch($role->id) {
                    case 2://Doctor
                        $appointment = $appointment->where('doctor_id',$created_by);
                        $prescription = $prescription->where('doctor_id',$created_by);
                        $test_collection = $test_collection->where('doctor_id',$created_by);
                        break;
                    case 5://Receptionist
                        $clinic_id = auth()->user()->receptionist->clinic_id;
                        $appointment = $appointment->where('clinic_id',$clinic_id);
                        $test_collection = $test_collection->where('clinic_id',$clinic_id);
                        $membership = $membership->where('clinic_id',$clinic_id);
                        break;
                    case 6://Nurse
                        $clinic_id = auth()->user()->nurse->clinic_id;
                        $appointment = $appointment->where('clinic_id',$clinic_id);
                        $test_collection = $test_collection->where('clinic_id',$clinic_id);
                        $membership = $membership->where('clinic_id',$clinic_id);
                        break;
                    case 8://Pharmacist
                        $clinic_id = auth()->user()->pharmacist->clinic_id;
                        $membership = $membership->where('clinic_id',$clinic_id);
                        break;
                }
                
                $doctor = $doctor->count();
                $patient = $patient->count();
                $appointment = $appointment->count();
                $prescription = $prescription->count();
                $test_collection = $test_collection->count();
                $membership = $membership->count();
                // dd($doctor,$patient,$appointment,$prescription,$test_collection,$membership);
                $data = [
                    'doctor' => $doctor,//->where('id',1)
                    'patient' => $patient,
                    'appointment' => $appointment,
                    'prescription' => $prescription,
                    'test_collection' => $test_collection,
                    'membership' => $membership,
                ];
                // dd($data);
                
                $this->response['headerAction'] = view('authorization::admin.api.dashboardCount',compact('permissionPage','data'))->render();
            }
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function userProfile(Request $request)
    {
        try {
            $this->response['success']  = true;
            $user = $this->userService->findById(auth()->user()->id);
            // dd($user->clusterUser);
            $role = $this->getUserRole();
            $user_profile = [
                'title' => '',
                'sub_title' => '',
                'email' => $user->email != 'null' ? $user->email : '',
                'phone' => $user->phone,
                'profile_image' => $user->profile_image ? Storage::disk('gcs')->url($user->profile_image) : '',
                'location' => '',
                'professional_info' => [],
            ];
            switch ($role->id) {
                case 2://Doctor
                    $doctor = $user->doctor;
                    $user_profile['title'] = 'Dr. '.$doctor->first_name.' '.$doctor->last_name;
                    $user_profile['sub_title'] = $doctor->specialitys->speciality;
                    $user_profile['location'] = $doctor->address;
                    $user_profile['professional_info'] = [
                        'Registration No' => $doctor->registration_no,
                        'Visit Price' => $doctor->visit_price,
                        'Degree' => $doctor->degree,
                        'Type' => $doctor->doctorType->title,
                        'Speciality' => $doctor->specialitys->speciality,
                    ];
                    break;
                case 3://Cluster Manager
                    $clusterUser = $user->clusterUser;
                    $user_profile['title'] = $user->username;
                    $user_profile['location'] = $clusterUser->address;
                    $user_profile['professional_info'] = [
                        'Associated Clinic' => implode(', ',DB::table('clinics')->whereIn('id',json_decode($clusterUser->clinic_id))->pluck('clinic_name')->toArray()),
                    ];
                    break;
                case 4://Center Manager
                    $centerManager = $user->centerManager;
                    $user_profile['title'] = $user->username;
                    $user_profile['professional_info'] = [
                        'Associated Clinic' => $centerManager->clinics->clinic_name,
                    ];
                    break;
                case 5://Receptionist
                    $receptionist = $user->receptionist;
                    $user_profile['title'] = $user->username;
                    $user_profile['location'] = $receptionist->address;
                    $user_profile['professional_info'] = [
                        'Associated Clinic' => $receptionist->clinics->clinic_name,
                    ];
                    break;
                case 6://Nurse
                    $nurse = $user->nurse;
                    $user_profile['title'] = $user->username;
                    $user_profile['location'] = $nurse->address;
                    $user_profile['professional_info'] = [
                        'Associated Clinic' => $nurse->clinics->clinic_name,
                    ];
                    break;
                case 7://Phlebotomist
                    $phlebotomist = $user->phlebotomist;
                    $user_profile['title'] = $user->username;
                    $user_profile['location'] = $phlebotomist->address;
                    $user_profile['professional_info'] = [
                        'Gender' => $phlebotomist->gender == 'M' ? 'Male' : 'Female',
                        'Date of Birth' => $phlebotomist->dob,
                        'City' => $phlebotomist->city,
                        'Pincode' => $phlebotomist->pincode,
                        'DL No' => $phlebotomist->dl_no,
                        'Vehicle No' => $phlebotomist->vehicle_no,
                        'Working City' => $phlebotomist->working_city,
                        'Working City Pincode' => $phlebotomist->working_city_pincode,
                        'Associated Clinic' => $phlebotomist->clinics->clinic_name,
                    ];
                    break;
                case 8://Pharmacist
                    $pharmacist = $user->pharmacist;
                    $user_profile['title'] = $user->username;
                    $user_profile['location'] = $pharmacist->address;
                    $user_profile['professional_info'] = [
                        'Associated Clinic' => $pharmacist->clinics->clinic_name,
                    ];
                    break;
                case 9://Agent
                    $agent = $user->agent;
                    $user_profile['title'] = $user->username;
                    $user_profile['sub_title'] = config('users.designations.'.$agent->designation);
                    $user_profile['location'] = $agent->address;
                    $user_profile['professional_info'] = [
                        'Team' => config('users.teams.'.$agent->team),
                    ];
                    break;
                case 10://Customer Care
                    $customerCare = $user->customerCare;
                    $user_profile['title'] = $user->username;
                    break;
                default:
                    $user_profile['title'] = $user->username;
                    break;
            }
            // dd($user_profile);
            $data = [
                'id' => auth()->user()->id,
                'user_profile' => $user_profile,
            ];
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $this->response['headerAction'] = view('authorization::admin.api.userProfile',compact('permissionPage','data'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'old_password' => 'required',
                'new_password' => 'required|confirmed|min:6',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            $user = $this->userService->findById(auth()->user()->id);
            if (!Hash::check($request->old_password, auth()->user()->password)) {
                return response()->json([
                    'message' => 'Old password is incorrect.',
                    'errors' => [
                        'old_password' => ['Old password is incorrect.']
                    ]
                ], 422);
            }
            // dd(auth()->user()->password,Hash::make($request->old_password));
            $user->password = Hash::make($request->new_password);
            $user->save();
            $this->response['success']  = true;
            $this->response['res_data'] = 'password changed';
            $this->response['message']  = 'Password has been changed successfully!';
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function uploadProfileImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_profile_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            $user = $this->userService->findById(auth()->user()->id);
            $profileImageUrl = null;
            if ($request->hasFile('user_profile_image')) {
                $this->imageUploadService->setFieldName('user_profile_image');
                $this->imageUploadService->setFilePath('user/profile/');
                $this->imageUploadService->uploadOneFile();
                $profileResponse = $this->imageUploadService->getUrls();
                $profileImageUrl = $profileResponse[0]['name'] ?? null;
            }
            if ($profileImageUrl) {
                $request->merge([
                    'profile_image' => $profileImageUrl,
                    'modified_by' => $this->modifiedBy()
                ]);
            }
            $this->userService->setRequest($request);
            $this->userService = $this->userService->update();
            $this->response['success']  = true;
            $this->response['res_data'] = 'uploaded image';
            $this->response['message']  = 'Profile image has been uploaded successfully!';
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
}
