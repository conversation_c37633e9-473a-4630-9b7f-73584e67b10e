<form class="clearfix" method="post"
    action="{{ $id ? config('settings.url_review_survey') . 'update/' . $id : config('settings.url_review_survey') . 'add' }}"
    data-mode="{{ $id ? 'update' : 'add' }}" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" name="language" id="language" value="{{ $id ? $data['language'] : '' }}">
    <input type="hidden" name="survey_title" id="survey_title" value="{{ $id ? $data['survey_title'] : '' }}">
    <input type="hidden" name="survey_settings_json" id="survey_settings_json" value="{{ $id ? $data['survey_settings_json'] : '' }}">
    <div class="row">
        <div class="form-group col-md-12" id="surveyCreator">

        </div>
        <div class="form-group col-md-12 d-flex justify-content-end">
            <button type="button" name="submit" onclick="submitSurvey()"
                class="btn btn-primary text-white">{{ $id ? 'Update' : 'Save' }} Survey</button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<script>
    $('#languageSelector').val('{{ $id ? $data['language'] : '' }}');
    survey('<?php echo $id ? $data['survey_settings_json'] : '{}'; ?>');
</script>
