<?php

namespace Modules\Report\Models;

use App\Models\Patient as AuthPatient;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Patient extends AuthPatient
{
    public function appointments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Appointment::class, 'patient_id', 'id');
    }
    public function membershipRegistrations(): Has<PERSON><PERSON>
    {
        return $this->hasMany(MembershipRegistration::class, 'patient_id', 'id');
    }
    public function membershipRegistrationOpd(): Has<PERSON><PERSON>
    {
        return $this->membershipRegistrations()->with('memberships:id,name')
        // ->where('category_id', 1)
        ->where('status', 3);
    }

    public function doctor(): Belong<PERSON>T<PERSON>
    {
        return $this->belongsTo(Doctor::class, 'doctor_id', 'id');
    }

     public function orderMedicines(): Has<PERSON>any
    {
        return $this->hasMany(OrderMedicine::class, 'patient_id', 'id');
    }

    
}
