<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\MedicineCategory;

class CategoryService extends ApplicationDefaultService
{
    public $entity;
    public $columns = ['id', 'category', 'description', 'status', 'created_by', 'modified_by', 'deleted_by', 'created_at', 'updated_at', 'deleted_at'];
    public $cacheColumns = ['id', 'category', 'description', 'status', 'created_by', 'modified_by', 'deleted_by', 'created_at', 'updated_at', 'deleted_at'];

    public function __construct(MedicineCategory $entity)
    {
        $this->entity = $entity;
    }

    public function allMedicineCategorys()
    {
        $this->entity = $this->entity->select($this->columns)
            ->where('status', 1)
            ->get();

        return $this->entity;
    }
}
