<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;

class MembershipReportExport implements FromView, ShouldAutoSize
{

    protected $data;
    protected $statusLabels;
    public function __construct($data, $statusLabels)
    {
        $this->data = $data;
        $this->statusLabels = $statusLabels;
    }

    public function view(): View
    {
        $data = $this->data;
        $statusLabels = $this->statusLabels;
        return view('report::membership.export.membership', compact('data', 'statusLabels'));
    }
}
