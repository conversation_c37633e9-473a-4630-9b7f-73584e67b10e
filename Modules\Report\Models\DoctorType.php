<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DoctorType extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'title',
        'price',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function doctors(): HasMany
    {
        return $this->hasMany(Doctor::class, 'doctor_type', 'id');
    }
}
