<?php $__env->startSection('title'); ?>
    <?php echo e(config('billing.title', 'billing')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12 placeholder-glow" id="data-add-edit">
        <?php echo $__env->make('appointment::appointment.loadingForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(route('billing.index')); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('billing.url') . 'createCardBill/' . $id : ''); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
        let reward_phone_no = '';
        let opening_point = 0;
        let redeem_points = 0;

        function checkRedeemPoints(params,stat) {
            opening_point = Number($('#opening_rdPoints').val());
            redeem_points = Number($(params).val());
            let payable_amount = Number($('#payable_amount').val());
            if (redeem_points > opening_point || redeem_points > payable_amount) {
                alert('Please enter valid points');
                $(params).val('');
                redeem_points = 0;
                if (stat == 1) {
                    $('#rdPoints_otherNo').text(opening_point.toFixed(2));
                }
                else {
                    $('#opening_rdPoints_text').text(opening_point.toFixed(2));
                }
                return false;
            }
            if (stat == 1) {
                $('#rdPoints_otherNo').text((opening_point - redeem_points).toFixed(2));
            }
            else{
                $('#opening_rdPoints_text').text((opening_point - redeem_points).toFixed(2));
            }
            return true;
            // console.log(params);
        }

        function sendOtp(params,stat) {
            reward_phone_no = $('#reward_phone_no').val();
            const validationErrors = validateRewardPoint(redeem_points);
            if (validationErrors) {
                alert(validationErrors.message);
                return false;
            }
            // redeem_points = Number($('#redeem_points').val());
            // console.log(reward_phone_no, redeem_points);
            if (redeem_points == '') {
                alert('Please enter points');
                return false;
            }
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'sendOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "reward_phone_no": reward_phone_no,
                    "type": "MB-CARD",
                    "redeem_points": redeem_points,
                    "opening_point": opening_point
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // console.log(data.otp_expire);
                    // Store data
                    localStorage.setItem('device_check', data.otp_expire);
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    if (stat == 1) {
                        $('#rewardRedemptionPoint_other').text(redeem_points.toFixed(2));
                        $('.rewardRedemption_other').show();
                        $('#redeempoints_div_other').hide();
                        $('#otpdiv_other').show();
                    }
                    else{
                        $('#rewardRedemptionPoint').text(redeem_points.toFixed(2));
                        $('.rewardRedemption').show();
                        $('#redeempoints_div').hide();
                        $('#otpdiv').show();
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
        function checkOTP(params,stat) {
            // Retrieve data
            let device_check = localStorage.getItem('device_check');

            console.log(device_check);
            if (device_check === null) {
                alert('OTP has been sent to this device. Please verify the OTP on the same device.');
                return false;
            }
            // console.log(localStorage.getItem('device_check'));
            let otp_user = 0;
            if (stat == 1) {
                otp_user = $('#otp_otherNo').val();
            }
            else{
                otp_user = $('#otp').val();
            }
            // console.log(otp_user,$('#otp').val());
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'verifyOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "reward_phone_no": reward_phone_no,
                    "type": "MB-CARD",
                    "redeem_points": redeem_points,
                    "opening_point": opening_point,
                    "otp": otp_user
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    if (data.verify == true) {
                        $('#reward_points_final').val(redeem_points);
                        let payable_amount = Number($('#payable_amount').val()) - redeem_points;
                        redeemPoints(payable_amount);
                        $('#actaulRdPointsDiv').show();
                        if (stat == 1) {
                            $('#otpdiv_other').hide();
                            $('#error_msg_other').hide();
                        }
                        else{
                            $('#otpdiv').hide();
                            $('#error_msg').hide();
                        }
                        // Remove data
                        localStorage.removeItem('device_check');
                    }
                    else{
                        if (stat == 1) {
                            $('#error_msg_other').show();
                        }
                        else{
                            $('#error_msg').show();
                        }
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
        function redeemPoints(payable_amount) {
            $('#payable_amount').val(payable_amount);
            $('#total_due').val(payable_amount);
            paidCalculation();
        }
        function cancelownphno() {
            console.log(redeem_points);
            let payable_amount = Number($('#payable_amount').val()) + Number($('#reward_points_final').val());
            redeemPoints(payable_amount);
            redeem_points = 0;
            console.log(opening_point);
            $('#opening_rdPoints_text').text(opening_point.toFixed(2));
            $('#redeem_points').val('');
            $('.rewardRedemption').hide();
            $('#redeempoints_div').show();
            $('#otp').val('');
            $('#otpdiv').hide();
            $('#reward_points_final').val(0);
            $('#actaulRdPointsDiv').hide();
        }
        // checkOtherPhno
        function checkOtherPhno(patient_phone) {
            opening_point = 0;
            $('#opening_rdPoints').val(0);
            cancelownphno();
            $('#rdPoints_otherNo').text('0.00');
            $('#otherno_for_rdpoints').text('');
            $('#reward_redemption_other').text('');
            $('#other_phone_no').val('');
            $('#redeem_points_other_no').val('');
            $('#otp_otherNo').val('');
            $('#redeempoints_otherNo_div').show();
            $('#othPhnoDiv').show();
            $('#ownPhnoDiv').hide();
            $('.rewardOtherShow').hide();
            $('#reward_phone_no').val(patient_phone);
        }
        function checkOwnPhno(patient_phone, opening) {
            opening_point = Number(opening);
            $('#opening_rdPoints').val(opening_point);
            cancelothphno();
            cancelownphno();
            $('#ownPhnoDiv').show();
            $('#othPhnoDiv').hide();
            $('#reward_phone_no').val(patient_phone);
        }
        function checkRewardPointsOther(params) {
            let other_phone_no = $('#other_phone_no').val();
            console.log(other_phone_no,jwtToken);
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'getRewardPoints'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "other_phone_no": other_phone_no
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    console.log(data);

                    $(params).prop('disabled', false);
                    $(params).html(btn_text);

                    $('#otherno_for_rdpoints').text(data.phone_no);
                    $('#reward_phone_no').val(data.phone_no);
                    $('#rdPoints_otherNo').text(Number(data.points).toFixed(2));
                    $('#otherphno').show();
                    $('#redeempoints_otherNo_div').hide();
                    $('#redeempoints_div_other').show();
                    $('#opening_rdPoints').val(Number(data.points));
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
        function cancelothphno() {
            console.log(redeem_points);
            let payable_amount = Number($('#payable_amount').val()) + redeem_points;
            redeemPoints(payable_amount);
            redeem_points = 0;
            // not done
            $('#rdPoints_otherNo').text(opening_point.toFixed(2));
            $('#redeem_points_other_no').val('');
            $('.rewardRedemption_other').hide();
            $('#redeempoints_div_other').show();
            $('#otp_otherNo').val('');
            $('#otpdiv_other').hide();
            $('#reward_points_final').val(0);
            $('#actaulRdPointsDiv').hide();
        }
    </script>
    <?php echo $__env->make('billing::billing.js.payment', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Billing\resources/views/billing/cardBill.blade.php ENDPATH**/ ?>