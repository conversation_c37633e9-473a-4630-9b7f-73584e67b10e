<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e($row['created_at'] ?? ''); ?></td>
        <td><?php echo e($row['unique_id'] ?? ''); ?></td>

        <td><?php echo e($row['date_of_collection'] ?? ''); ?></td>


        <td><?php echo e($row['clinic_name'] ?? ''); ?></td>

        <td><?php echo e($row['created_at'] ?? ''); ?></td>
        <td><?php echo e($visit_type[$row['type_of_collection']] ?? ''); ?></td>

        <td><?php echo e($row['created_by']['username'] ?? ''); ?></td>
        <td>
            <table class="table table-sm m-0">

                <?php $__currentLoopData = $row['tests']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($testItem['test']['test_name'])): ?>
                        <tr>
                            <td class="word-wrap wht-space-custom w-75">
                                <?php echo e($testItem['test']['test_name']); ?>

                            </td>
                        </tr>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </table>
        </td>
        <td><?php echo e($row['patient_name'] ?? ''); ?></td>
        <td><?php echo e(Helper::ageCalculator($row['patient_birthdate'] ?? '')); ?></td>
        <td><?php echo e($row['patient_gender'] ?? ''); ?></td>
        <td><?php echo e($row['patient_mobile'] ?? ''); ?></td>
        <td><?php echo e($row['doctor_name'] ?? ''); ?></td>

        <td><?php echo e($diagnostic_docotor_type[$row['doctortype']] ?? ''); ?></td>

        <td><?php echo e($row['user_doctors']['diagnostic_doctor']['itdose_doctorid'] ?? ''); ?></td>
        <td>

            <?php if($row['appointment_type'] == 1): ?>
                <?php echo e('Walking'); ?>

            <?php else: ?>
                <?php echo e('Campaign'); ?>

            <?php endif; ?>

        </td>

        <td><?php echo e($row['offered_name'] ?? ''); ?></td>
        <td>
            <table class="table table-sm m-0">

                <?php $__currentLoopData = $row['collected_by']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rows): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($rows['sin_created_by_name'])): ?>
                        <tr>
                            <td class="word-wrap wht-space-custom w-75">
                                <?php echo e($rows['sin_created_by_name'] ?? ''); ?>

                            </td>
                        </tr>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </table>
        </td>
        <td><?php echo e($diagnostic_status_list[$row['status']] ?? ''); ?></td>
        <td>
            <?php echo e($row['bill_amount_sum'] ?? '0'); ?>

        </td>
        <td>
            <?php echo e($row['discount_sum'] ?? '0'); ?>

        </td>
        <td><?php echo e($row['payment_bill_reward']['redeem_point_sum'] ?? 0); ?></td>
        <td><?php echo e($row['home_collection_sum'] ?? '0'); ?></td>
        <td><?php echo e($row['total_amount_sum']); ?></td>
        <td><?php echo e($row['paid_amount_sum']); ?></td>
        <td><?php echo e($row['payment_bill_refund']['refund_amount_sum'] ?? 0); ?></td>
        <td><?php echo e($row['due_amount_sum']); ?></td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/diagnostic/api/list.blade.php ENDPATH**/ ?>