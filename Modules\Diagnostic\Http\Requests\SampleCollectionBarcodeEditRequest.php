<?php

namespace Modules\Diagnostic\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SampleCollectionBarcodeEditRequest extends FormRequest
{
    protected $id;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $this->id = $this->route('id') ? $this->route('id') : '';
        $rules = [
            'sample_id'  => 'required',
            'checked_sample_test'  => 'required',
        ];
        
        return $rules;
    }
    public function messages()
    {
        return [
            'sample_id.required' => 'Please select sample',
            'checked_sample_test.required' => 'Please select at least one sample',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
