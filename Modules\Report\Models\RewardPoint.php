<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RewardPoint extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'phone_no',
        'date',
        'type',
        'point',
        'credit_debit',
        'is_redeem',
        'bill_id',
        'it_dose_transtaction_id',
        'csqare_invNo',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function paymentBill(): HasMany
    {
        return $this->hasMany(PaymentBillMaster::class, 'bill_show_id', 'bill_id')->with('payments','payments.paymentDetails')->orderBy('id', 'desc');
    }
}
