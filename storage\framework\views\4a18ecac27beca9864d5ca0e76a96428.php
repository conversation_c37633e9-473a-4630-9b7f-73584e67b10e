<form class="clearfix" method="post" action="<?php echo e($id ? config('appointment.url') . 'addBill/' . $id : ''); ?>" data-mode="add"
    enctype="multipart/form-data" id="submitForm">
    
    <div class="card mb-3">
        <div class="card-header border-bottom py-2 px-3 px-md-4 align-items-center d-flex justify-content-between">
            <div class="header-title">
                <h5 class="h5 mb-0">Add New Payment</h5>
            </div>
            <div>
                <a href="<?php echo e(route('appointment.index', [$stat == 'all' ? $stat : 'today'])); ?>">
                    <button type="button"
                        class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center"
                        data-bs-toggle="modal" data-bs-target="#exampleModalCenteredScrollable">
                        <svg fill="none" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24">
                            <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5"
                                stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        Back
                    </button>
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <h6 class="h6 mb-1 mb-md-3 fw-bold">Patient Details</h6>
                            <div>
                                <div class="d-flex">
                                    <h6 class="h6 mb-2 text-gray"><span><?php echo e($list['patient_name']); ?></span></h6>
                                    (<span><?php echo e($list['patient_sex']); ?></span>/<span><?php echo e(Helper::ageCalculator($list['patient_age'])); ?></span>)
                                </div>
                                <?php if(!empty($list['patient_membership'])): ?>
                                    <?php $__currentLoopData = $list['patient_membership']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <p class="mb-1 text-gray"><span><?php echo e($row['registration_no']); ?></span></p>
                                        <p class="mb-1 text-gray">
                                            <?php echo e($row['memberships']['name']); ?>

                                            (<span><?php echo e($row['clinics']['clinic_name']); ?></span>)
                                        </p>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 mb-md-0">
                            <h6 class="h6 mb-1 mb-md-3 fw-bold">Appointment Date</h6>
                            <p class="mb-1 text-gray">
                                <?php echo e($data->date); ?>

                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 payment pad_bot">
                    <h5 class="h6 mb-1 mb-md-3 fw-bold">Refd By Doctor</h5>
                    <h6 class="h6 mb-2 text-gray">Dr. <?php echo e($list['doctor_name']); ?>

                    </h6>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="card mb-0">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2  payment">
                            <div class="form-group last">
                                <h5 class="h6 mb-3 fw-bold">Select :</h5>
                                <label for="doctor_consultanat_0">Doctor <?php echo e($list['appointment_type']); ?></label><br>
                            </div>
                        </div>
                        <div class="col-md-5 payment">
                            <div class="row">
                                <div class="col-md-12 mb-2 input-group d-flex  gap-3 align-items-center">
                                    <label for="exampleInputEmail1" class="col-md-3 col-5">Sub Total
                                    </label>
                                    <input type="text" class="form-control form-control-sm rounded-1   pay_in"
                                        name="sub_total" id="sub_total" value="<?php echo e($list['visit_price']); ?>"
                                        placeholder=" " readonly="">
                                </div>
                                <div class="col-md-12 mb-2 input-group d-flex gap-3 align-items-center">
                                    <label for="exampleInputEmail1" class="col-md-3 col-5">Discount
                                    </label>
                                    <input type="text" class="form-control form-control-sm rounded-1 pay_in"
                                        readonly="" name="discount" id="discount" value="<?php echo e($list['discount']); ?>"
                                        placeholder="">
                                    <input type="hidden" name="membership_registration_no"
                                        value="<?php echo e($list['membership_registration_no']); ?>">
                                </div>
                                <div class="col-md-12 mb-2 input-group gap-3 align-items-center" id="actaulRdPointsDiv"
                                    style="display: none !important;">
                                    <label for="exampleInputEmail1" class="col-md-3 col-5">Reward
                                        Points
                                    </label>
                                    <input type="text" class="form-control form-control-sm rounded-1"
                                        name="reward_points_final" id="reward_points_final" value="0"
                                        placeholder="">
                                </div>
                                <div class="col-md-12 input-group d-flex gap-3 align-items-center">
                                    <label for="exampleInputEmail1" class="col-md-3 col-5">Gross Total</label>
                                    <input type="text" class="form-control form-control-sm rounded-1 pay_in"
                                        name="gross_total" id="payable_amount" value="<?php echo e($list['gross_total']); ?>"
                                        placeholder=" " readonly="">
                                </div>
                                <div
                                    class="col-md-12 input-group d-flex gap-3 align-items-center mt-4 mb-3 payment right-six">
                                    <div class="payment_label col-5 col-md-3">
                                        <label for="exampleInputEmail1" class="">Note </label>
                                    </div>
                                    <textarea class="form-control form-control-sm rounded-1" rows="2" name="remarks" id=""
                                        placeholder=" "></textarea>
                                </div>
                            </div>
                        </div>
                        <!-----------------Rewards points cards starts here---------------------->
                        <input type="hidden" name="reward_phone_no" id="reward_phone_no"
                            value="<?php echo e($list['patient_phone']); ?>">
                        <input type="hidden" id="opening_rdPoints" value="<?php echo e($list['reward_points']); ?>">
                        <div class="col-lg-4 col-md-5 offset-lg-1" id="ownPhnoDiv">
                            <div
                                class="card card-block card-stretch card-height border border-5 shadow p-2 mb-0  rounded-3">
                                <div class="card-body px-0 px-md-3">



                                    <h2 class="counter h1 text-primary text-center" style="visibility: visible;">
                                        <svg width="45" viewBox="0 0 24 24" fill="#999"
                                            xmlns="http://www.w3.org/2000/svg" style="margin-top: -5px;">
                                            <path fill-rule="evenodd" fill="#999999" clip-rule="evenodd"
                                                d="M21.9964 8.37513H17.7618C15.7911 8.37859 14.1947 9.93514 14.1911 11.8566C14.1884 13.7823 15.7867 15.3458 17.7618 15.3484H22V15.6543C22 19.0136 19.9636 21 16.5173 21H7.48356C4.03644 21 2 19.0136 2 15.6543V8.33786C2 4.97862 4.03644 3 7.48356 3H16.5138C19.96 3 21.9964 4.97862 21.9964 8.33786V8.37513ZM6.73956 8.36733H12.3796H12.3831H12.3902C12.8124 8.36559 13.1538 8.03019 13.152 7.61765C13.1502 7.20598 12.8053 6.87318 12.3831 6.87491H6.73956C6.32 6.87664 5.97956 7.20858 5.97778 7.61852C5.976 8.03019 6.31733 8.36559 6.73956 8.36733Z">
                                            </path>
                                            <path opacity="0.4"
                                                d="M16.0374 12.2966C16.2465 13.2478 17.0805 13.917 18.0326 13.8996H21.2825C21.6787 13.8996 22 13.5715 22 13.166V10.6344C21.9991 10.2297 21.6787 9.90077 21.2825 9.8999H17.9561C16.8731 9.90338 15.9983 10.8024 16 11.9102C16 12.0398 16.0128 12.1695 16.0374 12.2966Z"
                                                fill="currentColor"></path>
                                            <circle cx="18" cy="11.8999" r="1" fill="currentColor"></circle>
                                        </svg>
                                        <span
                                            id="opening_rdPoints_text"><?php echo e(number_format($list['reward_points'], 2)); ?></span>
                                    </h2>
                                    <p class="mb-0 text-dark text-center">Your Available Reward Points</p>

                                    <div class="gap-2 justify-content-center align-items-center rewardRedemption"
                                        style="display: none !important;">
                                        <p class="mb-0 text-dark text-center" style="">
                                            Reward redemption requested :
                                            <span id="rewardRedemptionPoint"></span>
                                        </p>
                                        <div class="text-center mt-0" style=" font-size: 0;">
                                            <button onclick="cancelownphno()" title="Cancel redemption request"
                                                type="button"
                                                class="link-primary border-0 bg-transparent fw-bold text-decoration-underline"><svg
                                                    height="18" viewBox="0 0 512 512" width="18"
                                                    xmlns="http://www.w3.org/2000/svg" id="fi_9068699">
                                                    <g id="Layer_2" data-name="Layer 2">
                                                        <g id="close">
                                                            <circle id="background" cx="256" cy="256"
                                                                fill="#f44336" r="256"></circle>
                                                            <path
                                                                d="m348.6 391a42.13 42.13 0 0 1 -30-12.42l-62.6-62.58-62.6 62.61a42.41 42.41 0 1 1 -60-60l62.6-62.61-62.61-62.6a42.41 42.41 0 0 1 60-60l62.61 62.6 62.6-62.61a42.41 42.41 0 1 1 60 60l-62.6 62.61 62.61 62.6a42.41 42.41 0 0 1 -30 72.4z"
                                                                fill="#fff"></path>
                                                        </g>
                                                    </g>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="" id="redeempoints_div">
                                        <div class="input-group d-flex mt-4 align-items-center">
                                            <input type="number" class="form-control form-control-sm" value=""
                                                id="redeem_points" placeholder="Enter Reward Points"
                                                onchange="checkRedeemPoints(this, 0)"
                                                onkeyup="checkRedeemPoints(this,0)" min="1">
                                            <div class="" id="redeempointsbuttondiv">
                                                <button id="reddem_patientPhone" onclick="sendOtp(this, 0)"
                                                    type="button"
                                                    class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end"
                                                    <?php echo e($list['gross_total'] == 0 || $list['reward_points'] == 0 ? 'disabled' : ''); ?>>Redeem
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="" id="otpdiv" style="display: none;">
                                        <div class="form-group mt-3">
                                            <div class="input-group mb-3 mt-0">
                                                <input type="text" class="form-control form-control-sm"
                                                    id="otp" placeholder="Enter OTP" value="">
                                                <div class="" id="checkotpbuttondiv">
                                                    <button onclick="checkOTP(this, 0)" type="button"
                                                        class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Verify
                                                        OTP</button>
                                                </div>
                                            </div>
                                            <div class="col-lg-12 mb-4 mt-2">
                                                <div class="row justify-content-between">
                                                    <div class="col-auto">
                                                        <h6 class="d-flex flex-wrap gap-1">
                                                            Time Remaining
                                                            <span>:</span>
                                                            <span class="timerOtpExpire">00:00</span>
                                                        </h6>
                                                    </div>
                                                    <div class="col-auto">
                                                        <button type="button"
                                                            class="text-primary h6 border-0 p-0 bg-transparent resendOTP"
                                                            onclick="resendOTP()" disabled>
                                                            Resend OTP
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <h6 class="h6 mt-3 text-primary small text-center" id="error_msg"
                                                style="display: none;">
                                                Please Enter Correct OTP
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="d-block text-center mt-3">
                                        <button onclick="checkOtherPhno('<?php echo e($list['patient_phone']); ?>')"
                                            type="button"
                                            class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Other
                                            Phone No.</button>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <!-----------------Rewards points cards ends here---------------------->

                        <!-----------------Rewards points cards for other No starts here---------------------->
                        <div class="col-lg-4 col-md-5 offset-lg-1" id="othPhnoDiv" style="display: none;">
                            <div
                                class="card card-block card-stretch card-height border border-5 shadow p-2 mb-0  rounded-3">
                                <div class="card-body px-0 px-md-3">



                                    <h2 class="counter h1 text-primary text-center" style="visibility: visible;">
                                        <svg width="45" viewBox="0 0 24 24" fill="#999"
                                            xmlns="http://www.w3.org/2000/svg" style="margin-top: -5px;">
                                            <path fill-rule="evenodd" fill="#999999" clip-rule="evenodd"
                                                d="M21.9964 8.37513H17.7618C15.7911 8.37859 14.1947 9.93514 14.1911 11.8566C14.1884 13.7823 15.7867 15.3458 17.7618 15.3484H22V15.6543C22 19.0136 19.9636 21 16.5173 21H7.48356C4.03644 21 2 19.0136 2 15.6543V8.33786C2 4.97862 4.03644 3 7.48356 3H16.5138C19.96 3 21.9964 4.97862 21.9964 8.33786V8.37513ZM6.73956 8.36733H12.3796H12.3831H12.3902C12.8124 8.36559 13.1538 8.03019 13.152 7.61765C13.1502 7.20598 12.8053 6.87318 12.3831 6.87491H6.73956C6.32 6.87664 5.97956 7.20858 5.97778 7.61852C5.976 8.03019 6.31733 8.36559 6.73956 8.36733Z">
                                            </path>
                                            <path opacity="0.4"
                                                d="M16.0374 12.2966C16.2465 13.2478 17.0805 13.917 18.0326 13.8996H21.2825C21.6787 13.8996 22 13.5715 22 13.166V10.6344C21.9991 10.2297 21.6787 9.90077 21.2825 9.8999H17.9561C16.8731 9.90338 15.9983 10.8024 16 11.9102C16 12.0398 16.0128 12.1695 16.0374 12.2966Z"
                                                fill="currentColor"></path>
                                            <circle cx="18" cy="11.8999" r="1" fill="currentColor"></circle>
                                        </svg>
                                        <span id="rdPoints_otherNo">0.00</span>
                                    </h2>
                                    <p class="mb-0 text-dark text-center">Your Available Reward Points</p>
                                    <div class="justify-content-center flex-wrap">
                                        <p id="otherphno" class="mb-0 text-dark text-center rewardOtherShow"
                                            style="display: none;">Phone
                                            Number :
                                            <span id="otherno_for_rdpoints"></span>
                                        </p>

                                        <div class="gap-2 justify-content-center align-items-center rewardOtherShow rewardRedemption_other"
                                            style="display: none;">
                                            <p class="mb-0 text-dark text-center" id="frezeRdpointsptagothNo"
                                                style=" ">Reward redemption requested : <span
                                                    id="rewardRedemptionPoint_other"></span></p>
                                            <div class="text-center mt-0" id="cancelothno" style="font-size: 0;">
                                                <button onclick="cancelothphno()" type="button"
                                                    title="Cancel redemption request"
                                                    class="link-primary border-0 bg-transparent fw-bold text-decoration-underline"><svg
                                                        height="18" viewBox="0 0 512 512" width="18"
                                                        xmlns="http://www.w3.org/2000/svg" id="fi_9068699">
                                                        <g id="Layer_2" data-name="Layer 2">
                                                            <g id="close">
                                                                <circle id="background" cx="256" cy="256"
                                                                    fill="#f44336" r="256"></circle>
                                                                <path
                                                                    d="m348.6 391a42.13 42.13 0 0 1 -30-12.42l-62.6-62.58-62.6 62.61a42.41 42.41 0 1 1 -60-60l62.6-62.61-62.61-62.6a42.41 42.41 0 0 1 60-60l62.61 62.6 62.6-62.61a42.41 42.41 0 1 1 60 60l-62.6 62.61 62.61 62.6a42.41 42.41 0 0 1 -30 72.4z"
                                                                    fill="#fff"></path>
                                                            </g>
                                                        </g>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="" id="redeempoints_otherNo_div">
                                        <div class="input-group d-flex mt-4 align-items-center">
                                            <!-- <label for="email" class="form-label h6 fw-normal">Reward Points</label> -->
                                            <input type="number" class="form-control form-control-sm" value=""
                                                id="other_phone_no" placeholder="Enter Another Number">
                                            <div class="" id="redeempointsbuttondiv">
                                                <button onclick="checkRewardPointsOther(this)" type="button"
                                                    class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Check</button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="rewardOtherShow" id="redeempoints_div_other" style="">
                                        <div class="input-group d-flex mt-4 align-items-center">
                                            <!-- <label for="email" class="form-label h6 fw-normal">Reward Points</label> -->
                                            <input type="number" class="form-control form-control-sm" value=""
                                                id="redeem_points_other_no" placeholder="Enter Reward Points"
                                                onchange="checkRedeemPoints(this, 1)"
                                                onkeyup="checkRedeemPoints(this,1)" min="1">
                                            <div class="" id="redeempointsbuttondiv">
                                                <button id="reddem_otherPhone" onclick="sendOtp(this, 1)"
                                                    type="button"
                                                    class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Redeem</button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="rewardOtherShow" id="otpdiv_other" style="">
                                        <div class="form-group mt-3">
                                            <!-- <label for="email" class="form-label h6">OTP</label> -->
                                            <div class="input-group mb-3 mt-0">
                                                <input type="text" class="form-control form-control-sm"
                                                    id="otp_otherNo" placeholder="Enter OTP" value="">
                                                <div class="" id="checkotpbuttondivOtherNo">
                                                    <button onclick="checkOTP(this, 1)" type="button"
                                                        class="btn btn-sm input-group-text text-white btn-primary rounded-0 rounded-end">Verify
                                                        OTP</button>
                                                </div>
                                            </div>
                                            <div class="col-lg-12 mb-4 mt-2">
                                                <div class="row justify-content-between">
                                                    <div class="col-auto">
                                                        <h6 class="d-flex flex-wrap gap-1">
                                                            Time Remaining
                                                            <span>:</span>
                                                            <span class="timerOtpExpire_other">00:00</span>
                                                        </h6>
                                                    </div>
                                                    <div class="col-auto">
                                                        <button type="button"
                                                            class="text-primary h6 border-0 p-0 bg-transparent resendOTP_other"
                                                            onclick="resendOTPOther()" disabled>
                                                            Resend OTP
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <h6 class="h6 mt-3 text-primary small text-center" id="error_msg_other"
                                                style="display: none;">
                                                Please Enter Correct OTP
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="d-block text-center mt-3">
                                        <button
                                            onclick="checkOwnPhno('<?php echo e($list['patient_phone']); ?>','<?php echo e($list['reward_points']); ?>')"
                                            type="button"
                                            class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Patient
                                            Phone No.</button>
                                    </div>
                                    <div class="d-block text-center mt-1">
                                        <button onclick="checkOtherPhno('<?php echo e($list['patient_phone']); ?>')"
                                            type="button"
                                            class="link-primary border-0 bg-transparent fw-bold text-decoration-underline">Try
                                            Another Phone No.</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-----------------Rewards points cards  cards for other No ends here---------------------->
                    </div>
                    
                    <?php echo $__env->make('billing::billing.api.paymentForm', [
                        'payment_modes' => config('billing.payment_mode'),
                        'due' => $list['gross_total'],
                        'min_payment_percentage' => 100,
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <!-------------Payment total ends here----------------->
                    <div class=" mt-3 w-100 text-end">
                        <button type="submit" name="submit" class="btn btn-primary text-white" id="submitBtn"
                            <?php echo e($list['gross_total'] > 0 ? 'disabled' : ''); ?>>Submit</button>
                    </div>
                    <div class="form-group col-md-12">
                        <div id="errorMessage" class="" style="color: red;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/billForm.blade.php ENDPATH**/ ?>