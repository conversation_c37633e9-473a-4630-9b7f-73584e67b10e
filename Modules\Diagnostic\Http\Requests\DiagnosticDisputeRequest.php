<?php

namespace Modules\Diagnostic\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DiagnosticDisputeRequest extends FormRequest
{
    protected $id;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $this->id = $this->route('id') ? $this->route('id') : '';
        $rules = [
            // 'patient_id'  => 'required',
            // 'patient_phone'  => 'required',
            'type_of_collection'  => 'required',
            'date_of_collection'  => 'required',
            'status'  => 'required',
            'doctortype'  => 'required',
            'clinic_id'  => 'required',
            'full_address' => 'required_if:type_of_collection,HC',
            'landmark' => 'required_if:type_of_collection,HC',
            'city' => 'required_if:type_of_collection,HC',
            'pincode' => 'required_if:type_of_collection,HC',
            'home_collection_range' => 'required_if:type_of_collection,HC',
            'home_collection_charge' => 'required_if:type_of_collection,HC',
            'home_collection_quantity' => 'required_if:type_of_collection,HC|numeric|min:1',
        ];
        if (empty($this->id)) {
            $rules['patient_id'] = 'required';
            $rules['patient_phone'] = 'required';
        }
        return $rules;
    }
    public function messages()
    {
        return [
            'patient_id.required' => 'Patient is required',
            'patient_phone.required' => 'Patient phone is required',
            'type_of_collection.required' => 'Type of collection is required',
            'date_of_collection.required' => 'Date of collection is required',
            'status.required' => 'Status is required',
            'doctortype.required' => 'Doctor type is required',
            'clinic_id.required' => 'Clinic is required',
            'full_address.required_if' => 'Full address is required',
            'landmark.required_if' => 'Landmark is required',
            'city.required_if' => 'City is required',
            'pincode.required_if' => 'Pincode is required',
            'home_collection_range.required_if' => 'Home collection range is required',
            'home_collection_charge.required_if' => 'Home collection charge is required',
            'home_collection_quantity.required_if' => 'Home collection quantity is required',
            'home_collection_quantity.numeric' => 'Home collection quantity must be a number',
            'home_collection_quantity.min' => 'Home collection quantity must be greater than 0',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
