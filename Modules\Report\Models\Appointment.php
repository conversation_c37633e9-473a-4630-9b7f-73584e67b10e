<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\Billing\Models\PaymentBillMaster;

class Appointment extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'patient_id',
        'patient_phone',
        'doctor_id',
        'clinic_id',
        'date',
        'time_slot',
        'schedule_id',
        'remarks',
        'payment_status',
        'unique_bill_id',
        'unique_queue_number',
        'cancel_reason',
        'appointment_type',
        'hand_write_prescription',
        'e_prescription_upload_fairbase',
        'data_source',
        'is_exist',
        'executive_name',
        'executive_name_remarks',
        'meeting_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];

    public function clinics(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }
    public function patients(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }
    public function patientWithMembership(): BelongsTo
    {
        return $this->patients()->with('membershipRegistrationOpd:id,patient_id,card_type,registration_no,end_date');
    }
    public function userDoctors(): BelongsTo
    {
        return $this->belongsTo(User::class, 'doctor_id', 'id');
    }
    public function schedules(): BelongsTo
    {
        return $this->belongsTo(Schedule::class, 'schedule_id', 'id');
    }
    public function vital(): HasOne
    {
        return $this->hasOne(Vital::class, 'appointment_id', 'id');
    }
    public function prescription(): HasOne
    {
        return $this->hasOne(Prescription::class, 'appointment_id', 'id');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
    public function appointmentVideoConsult(): HasOne
    {
        return $this->hasOne(AppointmentVideoConsult::class, 'id', 'meeting_id');
    }
    public function paymentBill(): HasOne
    {
        return $this->hasOne(PaymentBillMaster::class, 'service_id', 'id')->with('payments','payments.paymentDetails')->where('type','OPD')->orderBy('id', 'desc');
    }
    public function sampleCollectionMapSource(): HasMany
    {
        return $this->hasMany(SampleCollectionMapSource::class, 'service_id', 'id')->where('type', 'OPD');
    }
}
