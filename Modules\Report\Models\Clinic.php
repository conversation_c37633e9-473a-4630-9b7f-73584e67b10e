<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Clinic extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'clinic_name',
        'clinic_slug',
        'c_square_id',
        'is_warehouse',
        'is_new',
        'clinic_address',
        'clinic_phno',
        'clinic_email',
        'latitude',
        'longitude',
        'map_embeded_src',
        'photo',
        'district_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function membershipRegistrations(): HasMany
    {
        return $this->hasMany(MembershipRegistration::class, 'clinic_id', 'id');
    }
     public function orderMedicines(): HasMany
    {
        return $this->hasMany(OrderMedicine::class, 'clinic_id', 'id');
    }
    
}
