<?php

namespace Modules\Settings\Models;

use App\Models\Patient as AuthPatient;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Patient extends AuthPatient
{
    public function parent()
    {
        return $this->belongsTo(Patient::class, 'parent_id');
    }
    public function reviewsSurveyResult(): HasMany
    {
        return $this->hasMany(ReviewsSurveyResult::class, 'patient_id', 'id');
    }
}
