<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Report\Http\Controllers\ReportController;
use Modules\Report\Http\Controllers\MembershipReportController;
use Modules\Report\Http\Controllers\OPDAppointmentReportController;
use Mo<PERSON><PERSON>\Report\Http\Controllers\SchedulewiseDoctorReportController;
use Mo<PERSON>les\Report\Http\Controllers\MedicineOrderReportController;
use Modules\Report\Http\Controllers\LeadGenerationReportController;
use Modules\Report\Http\Controllers\DiagnosticReportController;
use Modules\Report\Http\Controllers\SampleCollectionReportController;
use Mo<PERSON>les\Report\Http\Controllers\DownloadReportController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('report')->group(function () {
    Route::get('/otc-utilization', [ReportController::class, 'indexOtcUtilization'])->name('report.OtcUtilization.index');

    Route::get('/otc-utilization-export-link', [ReportController::class, 'exportOtcUtilizationLink'])->name('report.OtcUtilization.exportLink');

//Membership
    Route::get('/membership-details-report', [MembershipReportController::class, 'index'])->name('report.membershipDetails.index');
    Route::get('/membership-export-link', [MembershipReportController::class, 'exportMembershipLink'])->name('report.membership.exportLink');


   // opd_appointment_report
    Route::get('/appointment-report', [OPDAppointmentReportController::class, 'index'])->name('report.appointment.index');
    Route::get('/opdappointment-export-link', [OPDAppointmentReportController::class, 'opdAppointmentExport'])->name('report.opdappointment.exportLink');

    //schedulewise-doctor-report
    Route::get('/schedule-wise-doctor-report', [SchedulewiseDoctorReportController::class, 'index'])->name('report.sechedulewisedoctor.index');
    Route::get('/schedulewisedoctor-export-link', [SchedulewiseDoctorReportController::class, 'schedulewiseDoctorExportLink'])->name('report.schedulewisedoctor.exportLink');

    //medicine order report
     Route::get('/medicine-order-report', [MedicineOrderReportController::class, 'index'])->name('report.medicineorder.index');
     Route::get('/medicineorder-export-link', [MedicineOrderReportController::class, 'medicineOrderExportLink'])->name('report.medicineorder.exportLink');

     //lead generation report
     Route::get('/lead-generation-report', [LeadGenerationReportController::class, 'indexLeadGeneration'])->name('report.leadgenerationreport.index');
    // Route::get('/leadgeneration-export-link', [ReportController::class, 'leadgenerationexportLink'])->name('report.leadgeneration.exportLink');

    //report.diagnosticreport.index
    Route::get('/diagnostic-report', [DiagnosticReportController::class, 'indexDiagnosticReport'])->name('report.diagnosticreport.index');
    Route::get('/diagnosticreport-export-link', [DiagnosticReportController::class, 'diagnosticReportExportLink'])->name('report.diagnostic.exportLink');

    //-investigation-wise
    Route::get('/diagnostic-report-investigation-wise', [DiagnosticReportController::class, 'indexDiagnosticReportInvestigationWise'])->name('report.diagnosticreportinvestigation.index');
    Route::get('/diagnosticreportinvestigation-export-link', [DiagnosticReportController::class, 'diagnosticReportInvestigationExportLink'])->name('report.diagnosticreportinvestigation.exportLink');

   // report.samplecollectionreport.index
   
    Route::get('/sample-collection-report', [SampleCollectionReportController::class, 'index'])->name('report.samplecollectionreport.index');
    Route::get('/samplecollection-export-link', [SampleCollectionReportController::class, 'sampleCollectionExportLink'])->name('report.samplecollection.exportLink');

   //downloadreport
    Route::get('/download-report', [DownloadReportController::class, 'Index'])->name('report.downloadreport.index');
   ;
});
