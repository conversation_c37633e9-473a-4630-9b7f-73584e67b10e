<!DOCTYPE html>
<html>
<head>
    <title>Export Data</title>
    <meta charset="utf-8">
</head>
<body>
    <table>
        <thead>
            <tr>
                <th>Visit Date & Time</th>
                <th>WorkOrder-ID</th>
                <th>Clinic</th>
                <th>Patient</th>
                <th>Collection Date</th>
                <th>Visit Type</th>
                <th>Test Name</th>
                <th>SIN No</th>
                <th>Collected Amt</th>
                <th>Phlebotomist</th>
                <th>Assign By</th>
                <th>Collected By</th>
            </tr>
        </thead>
        <tbody>
            @if (!empty($data['rows']))
                @foreach ($data['rows'] as $row)
                    <tr>
                        <td>{{ $row['sample_collection']['date_of_collection'] ?? '' }}</td>
                        <td>{{ $row['unique_id'] ?? '' }}</td>
                        <td>{{ $row['sample_collection']['clinic']['clinic_name'] ?? '' }}</td>
                        <td>{{ $row['sample_collection']['patient']['name'] ?? '' }}</td>
                        <td>{{ $row['sample_collection']['date_of_collection'] ?? '' }}</td>
                        <td>{{ $visit_type[$row['sample_collection']['type_of_collection']] ?? '' }}</td>
                        <td>{{ $row['test']['test_name'] ?? '' }}</td>
                        <td>{{ $row['sin_no'] ?? '' }}</td>
                        <td>{{ $row['amount'] ?? '' }}</td>
                        <td>{{ $row['sample_collection']['phlebotomist']['name'] ?? '' }}</td>
                        <td>{{ $row['sample_collection']['phlebo_assigned_by']['username'] ?? '' }}</td>
                        <td>{{ $row['sin_created_by']['collected_by'] ?? '' }}</td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="12">No records found.</td>
                </tr>
            @endif
        </tbody>
    </table>
</body>
</html>
