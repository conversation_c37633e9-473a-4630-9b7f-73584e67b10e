<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>myMd</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('hope-ui/assets/images/favicon.ico') }}" />

    <!-- Library / Plugin Css Build -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/core/libs.min.css') }}" />

    <!-- Flaticon Css -->
    {{-- <link rel="stylesheet" href="{{ asset('hope-ui/assets/flaticon/font/flaticon.css') }}"/> --}}


    <!-- Hope Ui Design System Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/hope-ui.min.css?v=2.0.0') }}" />

    <!-- Custom Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/custom.min.css?v=2.0.0') }}" />

    <!-- Dark Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/dark.min.css') }}" />

    <!-- Customizer Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/customizer.min.css') }}" />

    <!-- RTL Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/rtl.min.css') }}" />

    <link rel="stylesheet" href="{{ asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.css') }}" />
    <!-- sweetalert Css -->
    <link rel="stylesheet" href="{{ asset('hope-ui/assets/css/sweetalert.min.css') }}" />

</head>

<body class="  ">
    <!-- loader Start -->
    <div id="loading">
        <div class="loader simple-loader">
            <div class="loader-body"></div>
        </div>
    </div>
    <!-- loader END -->

    <aside class="sidebar sidebar-default sidebar-white sidebar-base navs-rounded-all ">
        <div class="sidebar-header d-flex align-items-center justify-content-start">
            <a href="../dashboard/index.html" class="navbar-brand">
                <!--Logo start-->
                <!--logo End-->

                <!--Logo start-->
                <div
                    class="logo-main bg-white position-relative z-1 d-flex align-items-center justify-content-center flex-shrink-0">
                    <div class="logo-normal">

                        <svg class="icon-30" id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 19.23 19.24">
                            <g id="Layer_1-2" data-name="Layer 1">
                                <g>
                                    <line fill="#565355" x1="12.44" y1="19.24" x2="12.45" y2="12.45" />
                                    <rect fill="#dd2a1b" x="6.77" width="5.68" height="19.23" />
                                    <rect fill="#dd2a1b" y="6.77" width="19.23" height="5.68" />
                                    <polygon fill="#565355"
                                        points="12.44 6.78 19.22 6.78 19.23 12.45 12.45 12.45 12.45 19.23 6.77 19.23 6.77 12.45 12.44 6.78" />
                                    <polygon fill="#565355" points="14.23 19.23 14.22 14.23 19.22 14.23 14.23 19.23" />
                                </g>
                            </g>
                        </svg>
                    </div>
                    <div class="logo-mini">
                        <svg class="icon-30" id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 19.23 19.24">
                            <g id="Layer_1-2" data-name="Layer 1">
                                <g>
                                    <line fill="#565355" x1="12.44" y1="19.24" x2="12.45" y2="12.45" />
                                    <rect fill="#dd2a1b" x="6.77" width="5.68" height="19.23" />
                                    <rect fill="#dd2a1b" y="6.77" width="19.23" height="5.68" />
                                    <polygon fill="#565355"
                                        points="12.44 6.78 19.22 6.78 19.23 12.45 12.45 12.45 12.45 19.23 6.77 19.23 6.77 12.45 12.44 6.78" />
                                    <polygon fill="#565355" points="14.23 19.23 14.22 14.23 19.22 14.23 14.23 19.23" />
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
                <!--logo End-->




                <div class="logo-title flex-grow-1">
                    <!--Logo start-->
                    <!--logo End-->

                    <!--Logo start-->
                    <div class="logo-main-full">
                        <svg id="Layer_1" class="icon-40 w-auto" data-name="Layer 1"
                            xmlns="http://www.w3.org/2000/svg" width="181.72" height="65.04" viewBox="0 0 181.72 65.04">
                            <g id="Group_1" data-name="Group 1">
                                <rect id="Rectangle_1" data-name="Rectangle 1" width="5.68" height="19.23"
                                    transform="translate(159.35)" fill="#dd2a1b" />
                                <rect id="Rectangle_2" data-name="Rectangle 2" width="19.23" height="5.68"
                                    transform="translate(152.58 6.77)" fill="#dd2a1b" />
                                <path id="Path_1" data-name="Path 1"
                                    d="M131.91,14.93h-2.27l.1,18h.94c6.95,0,11.33-3.08,12.12-8.99,0-5.84-4.76-9.01-10.88-9.01h0Zm-.74-8.47V6.43h.76a22.559,22.559,0,0,1,14.86,5.15,16.474,16.474,0,0,1,4.93,12.4,16.338,16.338,0,0,1-4.93,12.36c-4.33,3.41-9.88,5.12-16.38,5.12H120.32v-35Z"
                                    fill="#dd2b1e" />
                                <path id="Path_2" data-name="Path 2" d="M69.57,33.95l-3.16,7.51H83.33V33.95Z"
                                    fill="#dd2b1e" fill-rule="evenodd" />
                                <path id="Path_3" data-name="Path 3"
                                    d="M77.55,6.33H70.57L64.73,20.29,59.37,6.33H52.38L61.2,28.54,55.68,41.46h7.17l5.44-12.95Z"
                                    fill="#565355" />
                                <path id="Path_4" data-name="Path 4"
                                    d="M42.75,6.3c-7.49,0-8.61,6.15-8.63,6.22l-.06.38-.11-.36c-.02-.06-1.89-6.06-7.87-6.22h-.27a7.658,7.658,0,0,0-7.41,5.23l-.19.62V6.43H11.56V29.59h6.61c.06-1.48.06-2.67.05-4.17,0-.63-.02-1.3-.02-2.06,0-.62-.02-1.29-.05-1.98-.08-2.39-.17-4.87.67-6.53a4.48,4.48,0,0,1,4.08-2.38,4.742,4.742,0,0,1,4.46,2.79c.67,1.59.57,3.87.48,6.07-.03.73-.06,1.51-.06,2.21v6.03c.68.03,1.44.05,2.4.05.65,0,1.3-.01,1.97-.01s1.33-.02,2-.02h.38V24.21c0-.81-.03-1.62-.06-2.41-.11-4-.24-7.77,2.97-9.01a5.542,5.542,0,0,1,1.97-.4,4.722,4.722,0,0,1,4.58,3.98,25.031,25.031,0,0,1,.16,5.39c-.03.81-.06,1.65-.06,2.44v5.39h6.57c.02-1.63.05-3.21.08-4.71.06-3.05.13-5.89,0-8.87-.11-2.92-1.14-9.71-7.96-9.71h0Z"
                                    fill="#565355" fill-rule="evenodd" />
                                <path id="Path_5" data-name="Path 5" d="M55.36,33.95H11.53v7.51H52.16Z"
                                    fill="#dd2b1e" fill-rule="evenodd" />
                                <path id="Path_6" data-name="Path 6"
                                    d="M107.59,6.23l-8.1,10.44L91.38,6.23H82.26V41.46h8.63V18.73l8.6,10.19,8.55-10.22V41.46h8.64V6.23Z"
                                    fill="#dd2b1e" />
                                <path id="Path_7" data-name="Path 7"
                                    d="M1.27,53.35v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.7,1.7,0,0,0,.36-1.25,1.752,1.752,0,0,0-.33-1.21,1.6,1.6,0,0,0-1.16-.35H1.27Zm1.35-1.08a3.023,3.023,0,0,1,2.14.6,2.728,2.728,0,0,1,.62,2.05,2.89,2.89,0,0,1-.6,2.01,2.452,2.452,0,0,1-1.87.65H1.28v4.28H0v-9.6H2.62Zm3.3,6.25a4.685,4.685,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.137,2.137,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82A2.262,2.262,0,0,1,6.5,61.2a4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.114,1.114,0,0,0,1.05.57,1.034,1.034,0,0,0,1.01-.57,4.437,4.437,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm4.95,5.85H12.25v-9.6h1.14Zm3.57,2.54H15.82l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23Zm7.58-4.79H25.7a2.479,2.479,0,0,1-.67,1.78,2.3,2.3,0,0,1-1.74.64,2.219,2.219,0,0,1-1.92-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.577,2.577,0,0,1,1.73.57,1.9,1.9,0,0,1,.65,1.51v.14H24.54a1.27,1.27,0,0,0-.32-.92,1.125,1.125,0,0,0-.87-.32,1.107,1.107,0,0,0-1.08.56,4.143,4.143,0,0,0-.33,1.97,4.26,4.26,0,0,0,.32,1.95,1.093,1.093,0,0,0,1.03.57,1.122,1.122,0,0,0,.9-.4,1.58,1.58,0,0,0,.35-1.05h0Zm3.52,2.25H26.92v-9.6h1.14Zm1.59-8.36V52.27h1.09v1.24Zm0,8.36V55.18h1.09v6.69Zm6.25,0V57.6a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H32.33V56.23a4.186,4.186,0,0,0-.02-.48,4.683,4.683,0,0,0-.05-.59h1.17l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.864,1.864,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04H35.89Zm2.7-8.36V52.27h1.11v1.24Zm0,8.36V55.18h1.11v6.69Zm6.31-2.25h1.14a2.387,2.387,0,0,1-.65,1.78,2.32,2.32,0,0,1-1.76.64,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.488,2.488,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14H44.9a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm8.61-7.93V65.03H52.7V51.69h.82Zm8.26,1.67v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.651,1.651,0,0,0,.38-1.25,1.862,1.862,0,0,0-.33-1.21,1.649,1.649,0,0,0-1.17-.35H61.79Zm1.35-1.08a3.115,3.115,0,0,1,2.16.6,2.773,2.773,0,0,1,.6,2.05,2.842,2.842,0,0,1-.59,2.01,2.486,2.486,0,0,1-1.89.65H61.78v4.28H60.51v-9.6h2.62Zm7.52,9.6V57.61a2.663,2.663,0,0,0-.25-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H67.08v-9.6h1.14v3.68a2.854,2.854,0,0,1,.79-.71,2.135,2.135,0,0,1,1-.22,1.781,1.781,0,0,1,1.3.49,1.75,1.75,0,0,1,.49,1.32v5.04H70.64Zm3.71-4.93H73.14v-.14a1.534,1.534,0,0,1,.6-1.32,2.888,2.888,0,0,1,1.76-.46,2.531,2.531,0,0,1,1.71.48,1.876,1.876,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9.03.3.05.59.1.87H76.74l-.08-.84a2.518,2.518,0,0,1-.79.68,2,2,0,0,1-.95.24,1.788,1.788,0,0,1-1.38-.57,2.35,2.35,0,0,1,.21-3.22,3.544,3.544,0,0,1,2.24-.59h.63v-.33a2.085,2.085,0,0,0-.22-1.1,1.006,1.006,0,0,0-.81-.28,1.4,1.4,0,0,0-.9.25.852.852,0,0,0-.32.74v.08h0Zm2.27,1.43a4.009,4.009,0,0,0-1.93.33,1.147,1.147,0,0,0-.54,1.08,1.208,1.208,0,0,0,.29.82,1.007,1.007,0,0,0,.76.33,1.162,1.162,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52h0Zm3.65-3.19.11.87a1.466,1.466,0,0,1,.62-.76,1.858,1.858,0,0,1,1.06-.27h.22V56.2h-.3a1.69,1.69,0,0,0-1.24.37,1.8,1.8,0,0,0-.36,1.27v4.04H79.25V56.31s-.02-.32-.06-.76c-.03-.16-.03-.29-.05-.36Zm3.98.78a2.462,2.462,0,0,1,.78-.71,2.135,2.135,0,0,1,1-.22,2.008,2.008,0,0,1,1.08.3,1.636,1.636,0,0,1,.64.82,2.009,2.009,0,0,1,.76-.84,2.094,2.094,0,0,1,1.1-.28,1.7,1.7,0,0,1,1.3.49,1.784,1.784,0,0,1,.48,1.32v5.04H90.26V57.62a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.118,1.118,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H86.68V57.61a2.577,2.577,0,0,0-.24-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H83.12V56.24c0-.14,0-.3-.01-.48s-.03-.36-.05-.59h1.17l.05.78Zm9.69.98H92.73v-.14a1.586,1.586,0,0,1,.6-1.32,2.893,2.893,0,0,1,1.78-.46,2.484,2.484,0,0,1,1.7.48,1.884,1.884,0,0,1,.56,1.48v3.12c0,.3,0,.6.03.9s.05.59.08.87H96.34l-.08-.84a2.316,2.316,0,0,1-.79.68,1.933,1.933,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.1,2.1,0,0,1-.52-1.49,2.025,2.025,0,0,1,.75-1.73,3.481,3.481,0,0,1,2.22-.59h.64v-.33a1.906,1.906,0,0,0-.22-1.1.988.988,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.878.878,0,0,0-.32.74v.08Zm2.27,1.43a4.083,4.083,0,0,0-1.94.33,1.147,1.147,0,0,0-.54,1.08,1.168,1.168,0,0,0,.3.82.943.943,0,0,0,.75.33,1.189,1.189,0,0,0,1.06-.54,2.674,2.674,0,0,0,.36-1.51v-.52h0Zm6.23,1.25h1.14a2.231,2.231,0,0,1-2.41,2.42,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.47,2.47,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm4.49,4.79H105.8l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23ZM117.12,51.7V65.04h-.82V51.7h.82Zm7,10.18v-9.6h2.3a7.214,7.214,0,0,1,1.85.16,2.05,2.05,0,0,1,.92.52,2.471,2.471,0,0,1,.7,1.24,15.106,15.106,0,0,1,.19,3,16.425,16.425,0,0,1-.13,2.36,3.542,3.542,0,0,1-.44,1.22,2.435,2.435,0,0,1-1.01.86,6.31,6.31,0,0,1-2.08.24h-2.3Zm1.28-1.1h1.35a3.634,3.634,0,0,0,1.1-.14,1.313,1.313,0,0,0,.62-.51,3.351,3.351,0,0,0,.29-1.06,17.269,17.269,0,0,0,.1-2.06c0-.63-.01-1.21-.06-1.7a4.944,4.944,0,0,0-.12-.92,1.432,1.432,0,0,0-.64-.82,2.648,2.648,0,0,0-1.27-.24h-1.35v7.46Zm6.2-7.26V52.28h1.11v1.24Zm0,8.36V55.19h1.11v6.69Zm3.81-4.93h-1.24v-.14a1.586,1.586,0,0,1,.6-1.32,2.934,2.934,0,0,1,1.78-.46,2.531,2.531,0,0,1,1.71.48,1.842,1.842,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9s.05.59.08.87h-1.14l-.08-.84a2.317,2.317,0,0,1-.79.68,1.906,1.906,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.381,2.381,0,0,1,.23-3.22,3.481,3.481,0,0,1,2.22-.59h.65v-.33a1.883,1.883,0,0,0-.24-1.1.961.961,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.867.867,0,0,0-.3.74v.08h0Zm2.27,1.43a4.029,4.029,0,0,0-1.94.33,1.153,1.153,0,0,0-.55,1.08,1.168,1.168,0,0,0,.3.82.963.963,0,0,0,.76.33,1.2,1.2,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52Zm3.62.14a4.648,4.648,0,0,0,.3,1.95,1.007,1.007,0,0,0,.98.6,1.166,1.166,0,0,0,1.06-.6,4.328,4.328,0,0,0,.33-1.95,4.576,4.576,0,0,0-.32-2.03,1.084,1.084,0,0,0-1.03-.62,1.056,1.056,0,0,0-1.03.59,4.707,4.707,0,0,0-.3,2.06h0Zm-.78,4.11h1.3a1.077,1.077,0,0,0,.27.73.9.9,0,0,0,.7.27,1.088,1.088,0,0,0,.89-.32,2.054,2.054,0,0,0,.25-1.22v-.97a2.661,2.661,0,0,1-.74.7,1.647,1.647,0,0,1-.86.22,1.838,1.838,0,0,1-1.68-.86,5.251,5.251,0,0,1-.54-2.73,4.73,4.73,0,0,1,.54-2.6,1.816,1.816,0,0,1,1.66-.82,1.929,1.929,0,0,1,.9.19,2.2,2.2,0,0,1,.71.62l.06-.65h1.16c-.02.16-.03.33-.05.52s-.01.41-.01.67v5.87a3.346,3.346,0,0,1-.1.95,1.586,1.586,0,0,1-.28.6,1.631,1.631,0,0,1-.76.54,3.513,3.513,0,0,1-1.17.16,2.441,2.441,0,0,1-1.63-.49,1.785,1.785,0,0,1-.62-1.38h0Zm9.63-.74V57.62a2.35,2.35,0,0,0-.26-1.33.924.924,0,0,0-.85-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85h-1.16V56.25c0-.14,0-.3-.01-.48a4.683,4.683,0,0,0-.05-.59h1.18l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.881,1.881,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04h-1.16Zm2.44-3.35a4.527,4.527,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.148,2.148,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82,2.23,2.23,0,0,1-1.93-.82,4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.089,1.089,0,0,0,1.04.57,1.058,1.058,0,0,0,1.02-.57,4.436,4.436,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm3.59,3.82h1.25a1.5,1.5,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.153,1.153,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.7,2.7,0,0,1-.92-.7,1.377,1.377,0,0,1-.27-.88,1.721,1.721,0,0,1,.63-1.4,2.538,2.538,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.586,1.586,0,0,1,.6,1.3v.13h-1.24a.986.986,0,0,0-1.08-1.06,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.573,1.573,0,0,0,.52.36l1.11.56a4.528,4.528,0,0,1,1.3.89,1.687,1.687,0,0,1,.33,1.05,1.824,1.824,0,0,1-.65,1.48,3.011,3.011,0,0,1-3.47,0,2.2,2.2,0,0,1-.59-1.64h0Zm7.47-6.55V55.2h1.33v.84h-1.33v4a1.222,1.222,0,0,0,.16.75.739.739,0,0,0,.6.21c.11,0,.29,0,.52-.02h.05l-.02.86a3.046,3.046,0,0,1-.49.1,3.517,3.517,0,0,1-.45.03,1.676,1.676,0,0,1-1.19-.34,1.649,1.649,0,0,1-.35-1.17V56.05h-1.02v-.84H165V53.88l1.16-.55Zm2.38.22V52.29h1.11v1.24Zm0,8.36V55.2h1.11v6.69Zm6.31-2.25H176a2.427,2.427,0,0,1-.65,1.78,2.381,2.381,0,0,1-1.76.64,2.243,2.243,0,0,1-1.92-.81,4.831,4.831,0,0,1-.59-2.68,4.831,4.831,0,0,1,.59-2.68,2.249,2.249,0,0,1,1.92-.82,2.541,2.541,0,0,1,1.73.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.089,1.089,0,0,0,.89-.4,1.534,1.534,0,0,0,.36-1.05h0Zm2.16.22h1.25a1.383,1.383,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.17,1.17,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.809,2.809,0,0,1-.92-.7,1.39,1.39,0,0,1-.29-.88,1.709,1.709,0,0,1,.65-1.4,2.557,2.557,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.577,1.577,0,0,1,.59,1.3v.13h-1.22a1.041,1.041,0,0,0-.29-.78,1.076,1.076,0,0,0-.79-.28,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.473,1.473,0,0,0,.52.36l1.11.56a4.179,4.179,0,0,1,1.29.89,1.586,1.586,0,0,1,.35,1.05,1.8,1.8,0,0,1-.65,1.48,3.012,3.012,0,0,1-3.47,0,2.126,2.126,0,0,1-.59-1.64h0Z"
                                    fill="#565355" />
                            </g>
                        </svg>

                    </div>
                    <!--logo End-->




                </div>
            </a>
            <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
                <i class="icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.25 12.2744L19.25 12.2744" stroke="currentColor" stroke-width="1.5"
                            stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M10.2998 18.2988L4.2498 12.2748L10.2998 6.24976" stroke="currentColor"
                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </i>
            </div>
        </div>
        <div class="sidebar-body pt-0 data-scrollbar">
            <div class="sidebar-list">
                <!-- Sidebar Menu Start -->


                <ul class="navbar-nav iq-main-menu" id="sidebar-menu">
                    <li class="nav-item ps-2">
                        <a class="nav-link" aria-current="page" href="../dashboard/index.html">
                            <i class="icon">
                                <svg width="20" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="icon-20">
                                    <path opacity="0.4"
                                        d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z"
                                        fill="currentColor"></path>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2ZM4.53852 13.4655H7.92449C9.32676 13.4655 10.463 14.6114 10.463 16.0255V19.44C10.463 20.8532 9.32676 22 7.92449 22H4.53852C3.13626 22 2 20.8532 2 19.44V16.0255C2 14.6114 3.13626 13.4655 4.53852 13.4655ZM19.4615 13.4655H16.0755C14.6732 13.4655 13.537 14.6114 13.537 16.0255V19.44C13.537 20.8532 14.6732 22 16.0755 22H19.4615C20.8637 22 22 20.8532 22 19.44V16.0255C22 14.6114 20.8637 13.4655 19.4615 13.4655Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Home</span>
                        </a>
                    </li>






                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-doctor" role="button"
                            aria-expanded="false" aria-controls="sidebar-doctor">
                            <i class="icon">

                                <svg class="icon-20" height="20" viewBox="0 0 100 100" width="20"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                        <path
                                            d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                            fill="currentColor" />
                                        <path
                                            d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                            fill="currentColor" opacity=".4" />
                                        <circle cx="75.521" cy="70.648" r="3" fill="currentColor" />
                                    </g>
                                </svg>
                            </i>
                            <span class="item-name">Doctor</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-doctor" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../doctor/index-doctor.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> List of Doctor </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../doctor/doctor-visit.html">
                                    <i class="icon svg-icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Doctor visit</span>
                                </a>
                            </li>

                        </ul>
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#manage-clinic" role="button"
                            aria-expanded="false" aria-controls="manage-clinic">
                            <i class="icon">
                                <svg class="icon-20" height="512" viewBox="0 0 32 32" width="512"
                                    xmlns="http://www.w3.org/2000/svg" data-name="line expand copy">
                                    <path
                                        d="m16 0a12.9 12.9 0 0 0 -12.17 8.21 12.91 12.91 0 0 0 3 14.36l9.17 8.82 9.13-8.82a12.91 12.91 0 0 0 3-14.36 12.9 12.9 0 0 0 -12.13-8.21zm7 20h-14v-11.54l7-4.66 7 4.66z"
                                        fill="currentColor" opacity=".4" />
                                    <path
                                        d="m11 9.54v8.46h10v-8.46l-5-3.34zm7.5 3v2h-1.5v1.46h-2v-1.5h-1.5v-2h1.5v-1.5h2v1.5z"
                                        fill="currentColor" />
                                </svg>
                            </i>
                            <span class="item-name">Manage Clinic</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="manage-clinic" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../clinic/clinic.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> List Of Clinics </span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link " href="../clinic/holidays.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Holidays</span>
                                </a>
                            </li>
                        </ul>
                    </li>



                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#horizontal-speciality" role="button"
                            aria-expanded="false" aria-controls="horizontal-speciality">
                            <i class="icon">

                                <svg width="20" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="icon-20">
                                    <path opacity="0.4"
                                        d="M10.0833 15.958H3.50777C2.67555 15.958 2 16.6217 2 17.4393C2 18.2559 2.67555 18.9207 3.50777 18.9207H10.0833C10.9155 18.9207 11.5911 18.2559 11.5911 17.4393C11.5911 16.6217 10.9155 15.958 10.0833 15.958Z"
                                        fill="currentColor"></path>
                                    <path opacity="0.4"
                                        d="M22.0001 6.37867C22.0001 5.56214 21.3246 4.89844 20.4934 4.89844H13.9179C13.0857 4.89844 12.4102 5.56214 12.4102 6.37867C12.4102 7.1963 13.0857 7.86 13.9179 7.86H20.4934C21.3246 7.86 22.0001 7.1963 22.0001 6.37867Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M8.87774 6.37856C8.87774 8.24523 7.33886 9.75821 5.43887 9.75821C3.53999 9.75821 2 8.24523 2 6.37856C2 4.51298 3.53999 3 5.43887 3C7.33886 3 8.87774 4.51298 8.87774 6.37856Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M21.9998 17.3992C21.9998 19.2648 20.4609 20.7777 18.5609 20.7777C16.6621 20.7777 15.1221 19.2648 15.1221 17.3992C15.1221 15.5325 16.6621 14.0195 18.5609 14.0195C20.4609 14.0195 21.9998 15.5325 21.9998 17.3992Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Manage Speciality</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="horizontal-speciality" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../speciality/speciality.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> List Of Specialities </span>
                                </a>
                            </li>
                        </ul>
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#manage-product" role="button"
                            aria-expanded="false" aria-controls="manage-product">
                            <i class="icon">
                                <svg id="fi_4526832" height="18" viewBox="0 0 100 100" width="18"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                        <path
                                            d="m84.234 78.581c-2.014-5.994-10.041-9.746-15.764-12.261-2.242-.982-8.449-2.648-9.195-5.471-.267-1.017-.231-1.976-.012-2.895-.345.066-.695.105-1.057.105h-3.729c-2.977 0-5.396-2.422-5.396-5.397 0-2.973 2.42-5.39 5.396-5.39h3.729c1.232 0 2.4.417 3.342 1.161 1.381-.184 2.713-.479 3.955-.866 1.631-3.417 2.903-7.503 3.188-11.02 1.217-15.048-8.008-23.852-21.235-22.33-9.617 1.107-15.362 8.278-15.983 17.51-.628 9.41 2.861 16.36 6.567 21.458 1.623 2.229 3.328 3.662 3.066 6.348-.304 3.176-3.7 4.061-6.129 5.037-2.878 1.156-5.978 2.91-7.442 3.721-5.043 2.785-10.578 6.139-11.822 10.727-2.755 10.168 6.549 13.248 14.23 14.67 6.592 1.216 14.025 1.312 20.139 1.312 11.059 0 30.945-.443 34.152-8.756.912-2.359.521-6.118 0-7.663z"
                                            fill="currentColor"></path>
                                        <path
                                            d="m60.566 51.143c-.506-.771-1.371-1.283-2.358-1.283h-3.729c-1.556 0-2.811 1.257-2.811 2.805 0 1.554 1.255 2.813 2.811 2.813h3.729c1.089 0 2.013-.621 2.479-1.519 5.199-.409 9.721-1.997 12.895-4.342.729.47 1.591.745 2.521.745h.234c2.592 0 4.688-2.098 4.688-4.693v-9.368c0-1.866-1.094-3.476-2.672-4.224-.688-15.043-13.141-27.077-28.353-27.077s-27.667 12.034-28.352 27.077c-1.581.749-2.674 2.358-2.674 4.224v9.368c0 2.595 2.098 4.693 4.684 4.693h.237c2.588 0 4.687-2.098 4.687-4.693v-9.368c0-1.839-1.063-3.425-2.608-4.192.669-12.675 11.187-22.778 24.026-22.778 12.834 0 23.357 10.103 24.023 22.778-1.543.768-2.605 2.353-2.605 4.192v9.368c0 .622.121 1.201.334 1.742-2.732 1.955-6.709 3.348-11.186 3.732z"
                                            fill="currentColor" opacity="0.4"></path>
                                    </g>
                                </svg>
                            </i>
                            <span class="item-name">Service Management</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="manage-product" data-bs-parent="#sidebar-menu">
                            <li class="nav-item ps-2">
                                <a class="nav-link " href="../service_management/service_management.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Service</span>
                                </a>
                            </li>

                        </ul>
                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-patient" role="button"
                            aria-expanded="false" aria-controls="sidebar-patient">
                            <i class="icon">
                                <svg class="icon-20" height="32" viewBox="0 0 100 100" width="32"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g>
                                        <path
                                            d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                                            fill="currentColor" />
                                        <path
                                            d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                            fill="currentColor" opacity=".4" />
                                    </g>
                                </svg>
                            </i>
                            <span class="item-name">Patient</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-patient" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../patient/patient.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Patient List </span>
                                </a>
                            </li>

                        </ul>
                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-schedule" role="button"
                            aria-expanded="false" aria-controls="sidebar-schedule">
                            <i class="icon">

                                <svg width="32" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="icon-20" height="32">
                                    <path opacity="0.4"
                                        d="M16.34 1.99976H7.67C4.28 1.99976 2 4.37976 2 7.91976V16.0898C2 19.6198 4.28 21.9998 7.67 21.9998H16.34C19.73 21.9998 22 19.6198 22 16.0898V7.91976C22 4.37976 19.73 1.99976 16.34 1.99976Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M15.5734 15.8143C15.4424 15.8143 15.3104 15.7803 15.1894 15.7093L11.2634 13.3673C11.0374 13.2313 10.8984 12.9863 10.8984 12.7223V7.67529C10.8984 7.26129 11.2344 6.92529 11.6484 6.92529C12.0624 6.92529 12.3984 7.26129 12.3984 7.67529V12.2963L15.9584 14.4193C16.3134 14.6323 16.4304 15.0923 16.2184 15.4483C16.0774 15.6833 15.8284 15.8143 15.5734 15.8143Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Schedule</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-schedule" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../schedule/schedule.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> All Schedule </span>
                                </a>
                            </li>

                        </ul>
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-appointment" role="button"
                            aria-expanded="false" aria-controls="sidebar-appointment">
                            <i class="icon">

                                <svg width="32" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="icon-20" height="32">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M3 16.8701V9.25708H21V16.9311C21 20.0701 19.0241 22.0001 15.8628 22.0001H8.12733C4.99561 22.0001 3 20.0301 3 16.8701ZM7.95938 14.4101C7.50494 14.4311 7.12953 14.0701 7.10977 13.6111C7.10977 13.1511 7.46542 12.7711 7.91987 12.7501C8.36443 12.7501 8.72997 13.1011 8.73985 13.5501C8.7596 14.0111 8.40395 14.3911 7.95938 14.4101ZM12.0198 14.4101C11.5653 14.4311 11.1899 14.0701 11.1701 13.6111C11.1701 13.1511 11.5258 12.7711 11.9802 12.7501C12.4248 12.7501 12.7903 13.1011 12.8002 13.5501C12.82 14.0111 12.4643 14.3911 12.0198 14.4101ZM16.0505 18.0901C15.596 18.0801 15.2305 17.7001 15.2305 17.2401C15.2206 16.7801 15.5862 16.4011 16.0406 16.3911H16.0505C16.5148 16.3911 16.8902 16.7711 16.8902 17.2401C16.8902 17.7101 16.5148 18.0901 16.0505 18.0901ZM11.1701 17.2401C11.1899 17.7001 11.5653 18.0611 12.0198 18.0401C12.4643 18.0211 12.82 17.6411 12.8002 17.1811C12.7903 16.7311 12.4248 16.3801 11.9802 16.3801C11.5258 16.4011 11.1701 16.7801 11.1701 17.2401ZM7.09989 17.2401C7.11965 17.7001 7.49506 18.0611 7.94951 18.0401C8.39407 18.0211 8.74973 17.6411 8.72997 17.1811C8.72009 16.7311 8.35456 16.3801 7.90999 16.3801C7.45554 16.4011 7.09989 16.7801 7.09989 17.2401ZM15.2404 13.6011C15.2404 13.1411 15.596 12.7711 16.0505 12.7611C16.4951 12.7611 16.8507 13.1201 16.8705 13.5611C16.8804 14.0211 16.5247 14.4011 16.0801 14.4101C15.6257 14.4201 15.2503 14.0701 15.2404 13.6111V13.6011Z"
                                        fill="currentColor"></path>
                                    <path opacity="0.4"
                                        d="M3.00293 9.25699C3.01577 8.66999 3.06517 7.50499 3.15803 7.12999C3.63224 5.02099 5.24256 3.68099 7.54442 3.48999H16.4555C18.7376 3.69099 20.3677 5.03999 20.8419 7.12999C20.9338 7.49499 20.9832 8.66899 20.996 9.25699H3.00293Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M8.30465 6.59C8.73934 6.59 9.06535 6.261 9.06535 5.82V2.771C9.06535 2.33 8.73934 2 8.30465 2C7.86996 2 7.54395 2.33 7.54395 2.771V5.82C7.54395 6.261 7.86996 6.59 8.30465 6.59Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M15.6953 6.59C16.1201 6.59 16.456 6.261 16.456 5.82V2.771C16.456 2.33 16.1201 2 15.6953 2C15.2606 2 14.9346 2.33 14.9346 2.771V5.82C14.9346 6.261 15.2606 6.59 15.6953 6.59Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Appointments</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-appointment" data-bs-parent="#sidebar-menu">


                            <li class="nav-item">
                                <a class="nav-link " href="../appointment/all.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> All </span>
                                </a>
                            </li>



                            <li class="nav-item">
                                <a class="nav-link " href="../appointment/todays.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Todays</span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../appointment/appointment_request.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Appointment Request</span>
                                </a>
                            </li>


                        </ul>
                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-collection" role="button"
                            aria-expanded="false" aria-controls="sidebar-Phlebotomist">
                            <i class="icon">
                                <svg id="fi_2183886" enable-background="new 0 0 512 512" height="20"
                                    viewBox="0 0 512 512" width="20" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="m199.463 116h-132c-8.284 0-15 6.716-15 15 0 6.528 4.178 12.067 10 14.128v83.872h142v-83.872c5.822-2.061 10-7.6 10-14.128 0-8.284-6.716-15-15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m224.495 30h10.042v163.249c-.025.01-.049.02-.073.03v118.728c.12-.001.237-.008.357-.008 9.186 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728 2.874 0 6.987-1.811 11.341-3.728 6.346-2.794 14.244-6.272 23.43-6.272s17.083 3.478 23.43 6.272c4.354 1.917 8.467 3.728 11.341 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 9.187 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 4.47 0 8.705.632 12.73 1.595-4.804-23.468-14.695-45.575-29.291-65.145-18.702-25.076-43.554-44-72.345-55.201v-163.248h10.042c8.284 0 15-6.716 15-15s-6.716-15-15-15h-140.084c-8.284 0-15 6.716-15 15s6.716 15 15 15zm103.509 223.999c8.262 0 15 6.738 15 15s-6.738 15-15 15-15-6.739-15-15c0-8.262 6.738-15 15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m459.53 346.224c-5.836-2.253-11.337-4.225-16.087-4.225-2.874 0-6.986 1.811-11.34 3.728-6.346 2.794-14.244 6.272-23.43 6.272s-17.083-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.341-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728-.117 0-.24.017-.357.019v98.971c0 19.39-5.497 37.519-15.008 52.917 22.532 11.564 48.058 18.094 75.08 18.094 90.981 0 165-74.019 165-165 0-.259-.006-.517-.007-.776zm-175.567 56.776c-8.262 0-15-6.739-15-15 0-8.262 6.738-15 15-15s15 6.738 15 15c0 8.261-6.738 15-15 15zm55 29.999c-8.262 0-15-6.738-15-15s6.739-15 15-15c8.262 0 15 6.738 15 15s-6.738 15-15 15z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m62.463 285.999h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30.001h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v4.989c0 39.149 31.851 71 71 71 39.15 0 71-31.851 71-71v-181.989h-142z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Diagnostic Collection</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-collection" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../collection/collection.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">All</span>
                                </a>
                            </li>
                        </ul>


                        <!-- <ul class="sub-nav collapse" id="sidebar-collection" data-bs-parent="#sidebar-menu">
                                                <li class="nav-item">
                                                    <a class="nav-link " href="collection/diagnostic_booking">
                                                        <i class="icon">
                                                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10" viewBox="0 0 24 24" fill="currentColor">
                                                                <g>
                                                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                                                </g>
                                                            </svg>
                                                        </i>
                                                        <i class="sidenav-mini-icon"> H </i>
                                                        <span class="item-name">Diagnostic Booking</span>
                                                    </a>
                                                </li>
                                            </ul> -->
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#manage-pharmacy" role="button"
                            aria-expanded="false" aria-controls="manage-pharmacy">
                            <i class="icon">
                                <svg id="fi_2055176" height="18" viewBox="0 0 64 64" width="18"
                                    xmlns="http://www.w3.org/2000/svg" data-name="Layer 2">
                                    <path
                                        d="m45 3h-26a1 1 0 0 0 -1 1v20a1 1 0 0 0 1 1h26a1 1 0 0 0 1-1v-20a1 1 0 0 0 -1-1zm-6 13a1 1 0 0 1 -1 1h-3v3a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1-1v-3h-3a1 1 0 0 1 -1-1v-4a1 1 0 0 1 1-1h3v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3h3a1 1 0 0 1 1 1z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m50.36 38a8.32 8.32 0 0 1 -5.36-1.94 8.32 8.32 0 0 1 -5.36 1.94c-1.26-.14-4.56.64-7.64-1.94a8.32 8.32 0 0 1 -5.36 1.94c-1.26-.14-4.56.64-7.64-1.94-3.08 2.58-6.38 1.8-7.64 1.94a8.36 8.36 0 0 1 -3.36-.71v22.71a1 1 0 0 0 1 1h5v-18a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1v18h26a1 1 0 0 0 1-1v-22.71c-2.31 1.02-4 .62-5.64.71zm-1.36 16a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1-1v-11a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m18 26.82a3 3 0 0 1 -2-2.82v-11h-7a1 1 0 0 0 -1 .81l-3 15.64a1.37 1.37 0 0 0 0 .19 6.36 6.36 0 0 0 6.36 6.36h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                                        fill="currentColor"></path>
                                    <path d="m31 27h-11v7.27a6.35 6.35 0 0 0 4.36 1.73h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                                        fill="currentColor"></path>
                                    <path d="m44 27h-11v7.27a6.35 6.35 0 0 0 4.36 1.73h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m59 29.45-3-15.64a1 1 0 0 0 -1-.81h-7v11a3 3 0 0 1 -2 2.82v7.45a6.35 6.35 0 0 0 4.36 1.73h2.28a6.36 6.36 0 0 0 6.36-6.36 1.37 1.37 0 0 0 0-.19z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Pharmacy</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="manage-pharmacy" data-bs-parent="#sidebar-menu">
                            <li class="nav-item ps-2">
                                <a class="nav-link " href="medicine/medicine_order_collection">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Order</span>
                                </a>
                            </li>

                        </ul>
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#manage-billing" role="button"
                            aria-expanded="false" aria-controls="manage-billing">
                            <i class="icon">
                                <svg width="18" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4"
                                        d="M16.191 2H7.81C4.77 2 3 3.78 3 6.83V17.16C3 20.26 4.77 22 7.81 22H16.191C19.28 22 21 20.26 21 17.16V6.83C21 3.78 19.28 2 16.191 2Z"
                                        fill="currentColor"></path>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M8.07996 6.6499V6.6599C7.64896 6.6599 7.29996 7.0099 7.29996 7.4399C7.29996 7.8699 7.64896 8.2199 8.07996 8.2199H11.069C11.5 8.2199 11.85 7.8699 11.85 7.4289C11.85 6.9999 11.5 6.6499 11.069 6.6499H8.07996ZM15.92 12.7399H8.07996C7.64896 12.7399 7.29996 12.3899 7.29996 11.9599C7.29996 11.5299 7.64896 11.1789 8.07996 11.1789H15.92C16.35 11.1789 16.7 11.5299 16.7 11.9599C16.7 12.3899 16.35 12.7399 15.92 12.7399ZM15.92 17.3099H8.07996C7.77996 17.3499 7.48996 17.1999 7.32996 16.9499C7.16996 16.6899 7.16996 16.3599 7.32996 16.1099C7.48996 15.8499 7.77996 15.7099 8.07996 15.7399H15.92C16.319 15.7799 16.62 16.1199 16.62 16.5299C16.62 16.9289 16.319 17.2699 15.92 17.3099Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Billing</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>

                        <ul class="sub-nav collapse" id="manage-billing" data-bs-parent="#sidebar-menu">
                            <li class="nav-item ps-2">
                                <a class="nav-link " href="billing">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Membership Billing</span>
                                </a>
                            </li>

                        </ul>

                        <ul class="sub-nav collapse" id="manage-billing" data-bs-parent="#sidebar-menu">
                            <li class="nav-item ps-2">
                                <a class="nav-link " href="billing/index_payment">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Advance Payment</span>
                                </a>
                            </li>
                        </ul>

                        <ul class="sub-nav collapse" id="manage-billing" data-bs-parent="#sidebar-menu">
                            <li class="nav-item ps-2">
                                <a class="nav-link " href="billing/ledger">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Ledger</span>
                                </a>
                            </li>

                        </ul>

                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-users" role="button"
                            aria-expanded="false" aria-controls="sidebar-users">
                            <i class="icon">

                                <svg width="32" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="icon-20" height="32">
                                    <path
                                        d="M9.34933 14.8577C5.38553 14.8577 2 15.47 2 17.9173C2 20.3665 5.364 20.9999 9.34933 20.9999C13.3131 20.9999 16.6987 20.3876 16.6987 17.9403C16.6987 15.4911 13.3347 14.8577 9.34933 14.8577Z"
                                        fill="currentColor"></path>
                                    <path opacity="0.4"
                                        d="M9.34935 12.5248C12.049 12.5248 14.2124 10.4062 14.2124 7.76241C14.2124 5.11865 12.049 3 9.34935 3C6.65072 3 4.48633 5.11865 4.48633 7.76241C4.48633 10.4062 6.65072 12.5248 9.34935 12.5248Z"
                                        fill="currentColor"></path>
                                    <path opacity="0.4"
                                        d="M16.1733 7.84873C16.1733 9.19505 15.7604 10.4513 15.0363 11.4948C14.961 11.6021 15.0275 11.7468 15.1586 11.7698C15.3406 11.7995 15.5275 11.8177 15.7183 11.8216C17.6165 11.8704 19.3201 10.6736 19.7907 8.87116C20.4884 6.19674 18.4414 3.79541 15.8338 3.79541C15.551 3.79541 15.2799 3.82416 15.0157 3.87686C14.9795 3.88453 14.9404 3.90177 14.9208 3.93244C14.8954 3.97172 14.914 4.02251 14.9394 4.05605C15.7232 5.13214 16.1733 6.44205 16.1733 7.84873Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M21.779 15.1693C21.4316 14.4439 20.593 13.9465 19.3171 13.7022C18.7153 13.5585 17.0852 13.3544 15.5695 13.3831C15.547 13.386 15.5343 13.4013 15.5324 13.4109C15.5294 13.4262 15.5363 13.4492 15.5656 13.4655C16.2662 13.8047 18.9737 15.2804 18.6332 18.3927C18.6185 18.5288 18.729 18.6438 18.867 18.6246C19.5333 18.5317 21.2476 18.1704 21.779 17.0474C22.0735 16.4533 22.0735 15.7634 21.779 15.1693Z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                            <span class="item-name">Users</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-users" data-bs-parent="#sidebar-menu">

                            <li class="nav-item">
                                <a class="nav-link " href="../users/nurse.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Nurse </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../users/receptionist.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Receptionist</span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../users/customer-care.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Customer Care</span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../users/pharmacist.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Pharmacist</span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../users/agent.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> D </i>
                                    <span class="item-name">Agent</span>
                                </a>
                            </li>

                        </ul>
                    </li>


                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-lab-tests" role="button"
                            aria-expanded="false" aria-controls="sidebar-lab-tests">
                            <i class="icon">

                                <svg height="32" class="icon-20" viewBox="1 0 511 512" width="32"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="m499.355469 315.707031-83.042969-83.058593c-13.617188 27.535156-36.03125 49.96875-63.546875 63.605468l83.246094 82.9375c17.480469 17.417969 45.898437 17.390625 63.34375-.0625 17.523437-17.527344 17.527343-45.894531 0-63.421875zm0 0"
                                        fill="currentColor" />
                                    <path
                                        d="m181.078125 170.859375c0 60.570313 49.269531 109.847656 109.824219 109.847656 60.558594 0 109.828125-49.277343 109.828125-109.847656 0-60.574219-49.269531-109.851563-109.828125-109.851563-60.554688 0-109.824219 49.277344-109.824219 109.851563zm0 0"
                                        fill="currentColor" />
                                    <path
                                        d="m63.589844 301.039062h176.214844c-51.898438-20.449218-88.722657-71.09375-88.722657-130.179687 0-66.933594 47.253907-123.023437 110.144531-136.675781v-4.179688h5c8.285157 0 15-6.71875 15-15 0-8.285156-6.714843-15.003906-15-15.003906-1.550781 0-157.757812 0-158.984374 0-8.28125 0-15 6.71875-15 15.003906 0 8.28125 6.714843 15 15 15h5v192.355469zm0 0"
                                        fill="currentColor" opacity=".4" />
                                    <path
                                        d="m328.433594 331.046875h-283.398438l-31.675781 51.222656c-34.996094 56.59375 5.519531 129.730469 72.375 129.730469h202.101563c66.628906-.003906 107.320312-73.058594 72.277343-129.730469zm-177.410156 105.011719c-16.289063 0-29.496094-13.207032-29.496094-29.503906 0-16.292969 13.207031-29.503907 29.496094-29.503907 16.292968 0 29.496093 13.210938 29.496093 29.503907 0 16.296874-13.203125 29.503906-29.496093 29.503906zm89.492187-45.003906c-8.285156 0-15-6.71875-15-15.003907 0-8.285156 6.714844-15 15-15s15 6.714844 15 15c0 8.285157-6.714844 15.003907-15 15.003907zm0 0"
                                        fill="currentColor" />
                                </svg>
                            </i>
                            <span class="item-name">Lab Tests</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-lab-tests" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../lab/test.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Tests </span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-medicine" role="button"
                            aria-expanded="false" aria-controls="sidebar-medicine">
                            <i class="icon">
                                <svg height="32" class="icon-20" viewBox="0 0 512 512" width="32"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <g id="MEDICAL">
                                        <path d="m.7 386.41c9.29 70 68.91 123.94 141.1 123.94s131.8-54 141.09-123.94z"
                                            fill="currentColor" opacity=".4" />
                                        <path
                                            d="m282.89 348.24c-9.29-70-68.91-123.93-141.09-123.93s-131.8 53.97-141.1 123.93z"
                                            fill="currentColor" opacity=".4" />
                                        <path
                                            d="m501 330.06-46-109.37a383.78 383.78 0 0 0 -128.45 30.14q-17.64 7.48-34.28 16.57a181.7 181.7 0 0 1 -3.64 205.17 129.62 129.62 0 0 0 143.24 28.62c66.13-28.07 97.13-104.69 69.13-171.13z"
                                            fill="currentColor" />
                                        <path
                                            d="m395.53 79.32c-27.94-66.43-104.21-97.53-170.34-69.45-66.13 28.07-97.08 104.69-69.13 171.13l2.48 5.91a179.41 179.41 0 0 1 109 50.57 429.35 429.35 0 0 1 44.18-21.81 421.5 421.5 0 0 1 127.62-32.11zm-41.81 39.68a19 19 0 0 1 -24.88-10.15 62.31 62.31 0 0 0 -41.79-36.37 19 19 0 0 1 9.7-36.74 100.07 100.07 0 0 1 67.12 58.37 19 19 0 0 1 -10.15 24.89z"
                                            fill="currentColor" />
                                    </g>
                                </svg>
                            </i>
                            <span class="item-name">Medicine</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>

                        <ul class="sub-nav collapse" id="sidebar-medicine" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="../medicine/medicine.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Medicine Lists </span>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link " href="../medicine/medicinecategory.html">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Medicine Category </span>
                                </a>
                            </li>


                        </ul>

                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#sidebar-Phlebotomist" role="button"
                            aria-expanded="false" aria-controls="sidebar-Phlebotomist">
                            <i class="icon">


                                <svg xmlns="http://www.w3.org/2000/svg" height="20" version="1.1"
                                    viewBox="0 0 134 134.16046" width="20" id="fi_1086932">
                                    <g id="surface1">
                                        <path
                                            d="M 109.738281 0.914062 C 108.523438 -0.304688 106.546875 -0.304688 105.328125 0.914062 C 104.109375 2.132812 104.109375 4.105469 105.328125 5.324219 L 111.871094 11.867188 L 106.125 17.402344 L 116.617188 27.902344 L 122.359375 22.371094 L 117.117188 17.125 L 128.902344 28.910156 C 130.121094 30.128906 132.09375 30.128906 133.3125 28.910156 C 134.53125 27.695312 134.53125 25.71875 133.3125 24.5 Z M 109.738281 0.914062 "
                                            fill="currentColor"></path>
                                        <path
                                            d="M 125.746094 41.1875 L 93.042969 8.476562 C 91.824219 7.261719 89.851562 7.261719 88.632812 8.476562 C 87.414062 9.695312 87.414062 11.671875 88.632812 12.890625 L 93.351562 17.609375 L 84.332031 26.632812 L 91.808594 34.109375 C 92.546875 34.851562 92.546875 36.050781 91.808594 36.785156 C 91.066406 37.53125 89.871094 37.53125 89.132812 36.785156 L 81.652344 29.308594 L 74.75 36.214844 L 82.226562 43.691406 C 82.984375 44.394531 83.023438 45.574219 82.320312 46.332031 C 81.617188 47.089844 80.429688 47.128906 79.679688 46.425781 C 79.644531 46.394531 79.617188 46.367188 79.585938 46.332031 L 72.109375 38.855469 L 65.203125 45.746094 L 72.683594 53.222656 C 73.4375 53.925781 73.476562 55.109375 72.773438 55.863281 C 72.070312 56.617188 70.886719 56.664062 70.132812 55.957031 C 70.101562 55.929688 70.070312 55.894531 70.042969 55.863281 L 62.566406 48.390625 L 55.660156 55.289062 L 63.136719 62.765625 C 63.878906 63.503906 63.878906 64.699219 63.140625 65.441406 C 62.410156 66.179688 61.210938 66.183594 60.472656 65.445312 L 52.996094 57.96875 L 46.082031 64.886719 L 53.554688 72.359375 C 54.3125 73.0625 54.355469 74.246094 53.652344 75.003906 C 52.949219 75.757812 51.761719 75.800781 51.007812 75.097656 C 50.976562 75.066406 50.945312 75.035156 50.914062 75.003906 L 43.4375 67.527344 L 36.546875 74.402344 L 44.023438 81.878906 C 44.78125 82.582031 44.820312 83.769531 44.117188 84.519531 C 43.414062 85.277344 42.230469 85.320312 41.476562 84.617188 C 41.441406 84.589844 41.414062 84.554688 41.382812 84.519531 L 33.90625 77.046875 L 24.886719 86.066406 L 31.277344 92.457031 L 23.566406 100.171875 L 27.488281 104.070312 L 0.679688 130.921875 C -0.078125 131.625 -0.121094 132.8125 0.582031 133.5625 C 1.285156 134.320312 2.46875 134.363281 3.226562 133.660156 C 3.253906 133.625 3.289062 133.597656 3.316406 133.5625 L 30.132812 106.75 L 34.058594 110.675781 L 41.78125 102.949219 L 48.171875 109.34375 L 116.628906 40.886719 L 121.273438 45.597656 C 122.496094 46.816406 124.464844 46.816406 125.683594 45.597656 C 126.90625 44.375 126.90625 42.40625 125.683594 41.1875 Z M 125.746094 41.1875 "
                                            fill="currentColor"></path>
                                    </g>
                                </svg>
                            </i>
                            <span class="item-name">Phlebotomist</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav collapse" id="sidebar-Phlebotomist" data-bs-parent="#sidebar-menu">
                            <li class="nav-item">
                                <a class="nav-link " href="phlebotomist">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Phlebotomist Lists </span>
                                </a>
                            </li>
                        </ul>

                    </li>

                    <li class="nav-item ps-2">
                        <a class="nav-link" aria-current="page" href="">
                            <i class="icon">
                                <svg width="20" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4"
                                        d="M21.25 13.4764C20.429 13.4764 19.761 12.8145 19.761 12.001C19.761 11.1865 20.429 10.5246 21.25 10.5246C21.449 10.5246 21.64 10.4463 21.78 10.3076C21.921 10.1679 22 9.97864 22 9.78146L21.999 7.10415C21.999 4.84102 20.14 3 17.856 3H6.144C3.86 3 2.001 4.84102 2.001 7.10415L2 9.86766C2 10.0648 2.079 10.2541 2.22 10.3938C2.36 10.5325 2.551 10.6108 2.75 10.6108C3.599 10.6108 4.239 11.2083 4.239 12.001C4.239 12.8145 3.571 13.4764 2.75 13.4764C2.336 13.4764 2 13.8093 2 14.2195V16.8949C2 19.158 3.858 21 6.143 21H17.857C20.142 21 22 19.158 22 16.8949V14.2195C22 13.8093 21.664 13.4764 21.25 13.4764Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M15.4303 11.5887L14.2513 12.7367L14.5303 14.3597C14.5783 14.6407 14.4653 14.9177 14.2343 15.0837C14.0053 15.2517 13.7063 15.2727 13.4543 15.1387L11.9993 14.3737L10.5413 15.1397C10.4333 15.1967 10.3153 15.2267 10.1983 15.2267C10.0453 15.2267 9.89434 15.1787 9.76434 15.0847C9.53434 14.9177 9.42134 14.6407 9.46934 14.3597L9.74734 12.7367L8.56834 11.5887C8.36434 11.3907 8.29334 11.0997 8.38134 10.8287C8.47034 10.5587 8.70034 10.3667 8.98134 10.3267L10.6073 10.0897L11.3363 8.61268C11.4633 8.35868 11.7173 8.20068 11.9993 8.20068H12.0013C12.2843 8.20168 12.5383 8.35968 12.6633 8.61368L13.3923 10.0897L15.0213 10.3277C15.2993 10.3667 15.5293 10.5587 15.6173 10.8287C15.7063 11.0997 15.6353 11.3907 15.4303 11.5887Z"
                                        fill="currentColor"></path>
                                </svg>
                                </svg>
                            </i>
                            <span class="item-name">Verify Membership</span>
                        </a>
                    </li>




                    <li class="nav-item ps-2">
                        <a class="nav-link" data-bs-toggle="collapse" href="#manage-report" role="button"
                            aria-expanded="false" aria-controls="manage-report">
                            <svg width="18" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M10.1528 5.55559C10.2037 5.65925 10.2373 5.77027 10.2524 5.8844L10.5308 10.0243L10.669 12.1051C10.6705 12.3191 10.704 12.5317 10.7687 12.7361C10.9356 13.1326 11.3372 13.3846 11.7741 13.3671L18.4313 12.9316C18.7196 12.9269 18.998 13.0347 19.2052 13.2313C19.3779 13.3952 19.4894 13.6096 19.5246 13.8402L19.5364 13.9802C19.2609 17.795 16.4592 20.9767 12.6524 21.7981C8.84555 22.6194 4.94186 20.8844 3.06071 17.535C2.51839 16.5619 2.17965 15.4923 2.06438 14.389C2.01623 14.0624 1.99503 13.7326 2.00098 13.4026C1.99503 9.31279 4.90747 5.77702 8.98433 4.92463C9.47501 4.84822 9.95603 5.10798 10.1528 5.55559Z"
                                    fill="currentColor"></path>
                                <path opacity="0.4"
                                    d="M12.8701 2.00082C17.43 2.11683 21.2624 5.39579 22.0001 9.81229L21.993 9.84488L21.9729 9.89227L21.9757 10.0224C21.9652 10.1947 21.8987 10.3605 21.784 10.4945C21.6646 10.634 21.5014 10.729 21.3217 10.7659L21.2121 10.7809L13.5313 11.2786C13.2758 11.3038 13.0214 11.2214 12.8314 11.052C12.6731 10.9107 12.5719 10.7201 12.5433 10.5147L12.0277 2.84506C12.0188 2.81913 12.0188 2.79102 12.0277 2.76508C12.0348 2.55367 12.1278 2.35384 12.2861 2.21023C12.4444 2.06662 12.6547 1.9912 12.8701 2.00082Z"
                                    fill="currentColor"></path>
                            </svg>
                            <span class="item-name">Report</span>
                            <i class="right-icon">
                                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </i>
                        </a>
                        <ul class="sub-nav pb-5 collapse" id="manage-report" data-bs-parent="#sidebar-menu">

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Center wise</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/report_details">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Details Report</span>
                                </a>
                            </li>

                            <!-- modification by Indranil -->

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/membership_details">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Membership Counter</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/membership_details_report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Membership Report</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="cronjobs/date_view_cron">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Upload Csquare Daily data</span>
                                </a>
                            </li>


                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/generate_csquare_report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Get Csquare Daily data</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/pharmacyadvancedpayment">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Pharmacy Advance Payment</span>
                                </a>
                            </li>


                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/opd_appointment_report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">OPD Appointment</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/campaign">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Campaign</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/pharmacy">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Pharmacy Report</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/diagnostic_report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Diagnostic Report</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/membership_reg_report">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Activity Tracker</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/doctor_e_prescription">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Doctor Wise E-Prescription</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/lead_generation">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">Lead Generation</span>
                                </a>
                            </li>

                            <li class="nav-item ps-2">
                                <a class="nav-link " href="report/emr_details">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name">EMR Details</span>
                                </a>
                            </li>




                        </ul>
                    </li>



                </ul>
                <!-- Sidebar Menu End -->
            </div>
        </div>
        <div class="sidebar-footer"></div>
    </aside>
    <main class="main-content d-flex flex-column">
        <nav class="nav navbar navbar-expand-lg navbar-light iq-navbar">
            <div class="container-fluid navbar-inner">
                <a href="../dashboard/index.html" class="navbar-brand">
                    <div class="logo-title flex-grow-1">
                        <!--Logo start-->
                        <!--logo End-->

                        <!--Logo start-->
                        <div class="logo-main-full">
                            <svg id="Layer_1" class="icon-40 w-auto" data-name="Layer 1"
                                xmlns="http://www.w3.org/2000/svg" width="181.72" height="65.04"
                                viewBox="0 0 181.72 65.04">
                                <g id="Group_1" data-name="Group 1">
                                    <rect id="Rectangle_1" data-name="Rectangle 1" width="5.68" height="19.23"
                                        transform="translate(159.35)" fill="#dd2a1b" />
                                    <rect id="Rectangle_2" data-name="Rectangle 2" width="19.23" height="5.68"
                                        transform="translate(152.58 6.77)" fill="#dd2a1b" />
                                    <path id="Path_1" data-name="Path 1"
                                        d="M131.91,14.93h-2.27l.1,18h.94c6.95,0,11.33-3.08,12.12-8.99,0-5.84-4.76-9.01-10.88-9.01h0Zm-.74-8.47V6.43h.76a22.559,22.559,0,0,1,14.86,5.15,16.474,16.474,0,0,1,4.93,12.4,16.338,16.338,0,0,1-4.93,12.36c-4.33,3.41-9.88,5.12-16.38,5.12H120.32v-35Z"
                                        fill="#dd2b1e" />
                                    <path id="Path_2" data-name="Path 2" d="M69.57,33.95l-3.16,7.51H83.33V33.95Z"
                                        fill="#dd2b1e" fill-rule="evenodd" />
                                    <path id="Path_3" data-name="Path 3"
                                        d="M77.55,6.33H70.57L64.73,20.29,59.37,6.33H52.38L61.2,28.54,55.68,41.46h7.17l5.44-12.95Z"
                                        fill="#565355" />
                                    <path id="Path_4" data-name="Path 4"
                                        d="M42.75,6.3c-7.49,0-8.61,6.15-8.63,6.22l-.06.38-.11-.36c-.02-.06-1.89-6.06-7.87-6.22h-.27a7.658,7.658,0,0,0-7.41,5.23l-.19.62V6.43H11.56V29.59h6.61c.06-1.48.06-2.67.05-4.17,0-.63-.02-1.3-.02-2.06,0-.62-.02-1.29-.05-1.98-.08-2.39-.17-4.87.67-6.53a4.48,4.48,0,0,1,4.08-2.38,4.742,4.742,0,0,1,4.46,2.79c.67,1.59.57,3.87.48,6.07-.03.73-.06,1.51-.06,2.21v6.03c.68.03,1.44.05,2.4.05.65,0,1.3-.01,1.97-.01s1.33-.02,2-.02h.38V24.21c0-.81-.03-1.62-.06-2.41-.11-4-.24-7.77,2.97-9.01a5.542,5.542,0,0,1,1.97-.4,4.722,4.722,0,0,1,4.58,3.98,25.031,25.031,0,0,1,.16,5.39c-.03.81-.06,1.65-.06,2.44v5.39h6.57c.02-1.63.05-3.21.08-4.71.06-3.05.13-5.89,0-8.87-.11-2.92-1.14-9.71-7.96-9.71h0Z"
                                        fill="#565355" fill-rule="evenodd" />
                                    <path id="Path_5" data-name="Path 5" d="M55.36,33.95H11.53v7.51H52.16Z"
                                        fill="#dd2b1e" fill-rule="evenodd" />
                                    <path id="Path_6" data-name="Path 6"
                                        d="M107.59,6.23l-8.1,10.44L91.38,6.23H82.26V41.46h8.63V18.73l8.6,10.19,8.55-10.22V41.46h8.64V6.23Z"
                                        fill="#dd2b1e" />
                                    <path id="Path_7" data-name="Path 7"
                                        d="M1.27,53.35v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.7,1.7,0,0,0,.36-1.25,1.752,1.752,0,0,0-.33-1.21,1.6,1.6,0,0,0-1.16-.35H1.27Zm1.35-1.08a3.023,3.023,0,0,1,2.14.6,2.728,2.728,0,0,1,.62,2.05,2.89,2.89,0,0,1-.6,2.01,2.452,2.452,0,0,1-1.87.65H1.28v4.28H0v-9.6H2.62Zm3.3,6.25a4.685,4.685,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.137,2.137,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82A2.262,2.262,0,0,1,6.5,61.2a4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.114,1.114,0,0,0,1.05.57,1.034,1.034,0,0,0,1.01-.57,4.437,4.437,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm4.95,5.85H12.25v-9.6h1.14Zm3.57,2.54H15.82l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23Zm7.58-4.79H25.7a2.479,2.479,0,0,1-.67,1.78,2.3,2.3,0,0,1-1.74.64,2.219,2.219,0,0,1-1.92-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.577,2.577,0,0,1,1.73.57,1.9,1.9,0,0,1,.65,1.51v.14H24.54a1.27,1.27,0,0,0-.32-.92,1.125,1.125,0,0,0-.87-.32,1.107,1.107,0,0,0-1.08.56,4.143,4.143,0,0,0-.33,1.97,4.26,4.26,0,0,0,.32,1.95,1.093,1.093,0,0,0,1.03.57,1.122,1.122,0,0,0,.9-.4,1.58,1.58,0,0,0,.35-1.05h0Zm3.52,2.25H26.92v-9.6h1.14Zm1.59-8.36V52.27h1.09v1.24Zm0,8.36V55.18h1.09v6.69Zm6.25,0V57.6a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H32.33V56.23a4.186,4.186,0,0,0-.02-.48,4.683,4.683,0,0,0-.05-.59h1.17l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.864,1.864,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04H35.89Zm2.7-8.36V52.27h1.11v1.24Zm0,8.36V55.18h1.11v6.69Zm6.31-2.25h1.14a2.387,2.387,0,0,1-.65,1.78,2.32,2.32,0,0,1-1.76.64,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.488,2.488,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14H44.9a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm8.61-7.93V65.03H52.7V51.69h.82Zm8.26,1.67v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.651,1.651,0,0,0,.38-1.25,1.862,1.862,0,0,0-.33-1.21,1.649,1.649,0,0,0-1.17-.35H61.79Zm1.35-1.08a3.115,3.115,0,0,1,2.16.6,2.773,2.773,0,0,1,.6,2.05,2.842,2.842,0,0,1-.59,2.01,2.486,2.486,0,0,1-1.89.65H61.78v4.28H60.51v-9.6h2.62Zm7.52,9.6V57.61a2.663,2.663,0,0,0-.25-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H67.08v-9.6h1.14v3.68a2.854,2.854,0,0,1,.79-.71,2.135,2.135,0,0,1,1-.22,1.781,1.781,0,0,1,1.3.49,1.75,1.75,0,0,1,.49,1.32v5.04H70.64Zm3.71-4.93H73.14v-.14a1.534,1.534,0,0,1,.6-1.32,2.888,2.888,0,0,1,1.76-.46,2.531,2.531,0,0,1,1.71.48,1.876,1.876,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9.03.3.05.59.1.87H76.74l-.08-.84a2.518,2.518,0,0,1-.79.68,2,2,0,0,1-.95.24,1.788,1.788,0,0,1-1.38-.57,2.35,2.35,0,0,1,.21-3.22,3.544,3.544,0,0,1,2.24-.59h.63v-.33a2.085,2.085,0,0,0-.22-1.1,1.006,1.006,0,0,0-.81-.28,1.4,1.4,0,0,0-.9.25.852.852,0,0,0-.32.74v.08h0Zm2.27,1.43a4.009,4.009,0,0,0-1.93.33,1.147,1.147,0,0,0-.54,1.08,1.208,1.208,0,0,0,.29.82,1.007,1.007,0,0,0,.76.33,1.162,1.162,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52h0Zm3.65-3.19.11.87a1.466,1.466,0,0,1,.62-.76,1.858,1.858,0,0,1,1.06-.27h.22V56.2h-.3a1.69,1.69,0,0,0-1.24.37,1.8,1.8,0,0,0-.36,1.27v4.04H79.25V56.31s-.02-.32-.06-.76c-.03-.16-.03-.29-.05-.36Zm3.98.78a2.462,2.462,0,0,1,.78-.71,2.135,2.135,0,0,1,1-.22,2.008,2.008,0,0,1,1.08.3,1.636,1.636,0,0,1,.64.82,2.009,2.009,0,0,1,.76-.84,2.094,2.094,0,0,1,1.1-.28,1.7,1.7,0,0,1,1.3.49,1.784,1.784,0,0,1,.48,1.32v5.04H90.26V57.62a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.118,1.118,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H86.68V57.61a2.577,2.577,0,0,0-.24-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H83.12V56.24c0-.14,0-.3-.01-.48s-.03-.36-.05-.59h1.17l.05.78Zm9.69.98H92.73v-.14a1.586,1.586,0,0,1,.6-1.32,2.893,2.893,0,0,1,1.78-.46,2.484,2.484,0,0,1,1.7.48,1.884,1.884,0,0,1,.56,1.48v3.12c0,.3,0,.6.03.9s.05.59.08.87H96.34l-.08-.84a2.316,2.316,0,0,1-.79.68,1.933,1.933,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.1,2.1,0,0,1-.52-1.49,2.025,2.025,0,0,1,.75-1.73,3.481,3.481,0,0,1,2.22-.59h.64v-.33a1.906,1.906,0,0,0-.22-1.1.988.988,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.878.878,0,0,0-.32.74v.08Zm2.27,1.43a4.083,4.083,0,0,0-1.94.33,1.147,1.147,0,0,0-.54,1.08,1.168,1.168,0,0,0,.3.82.943.943,0,0,0,.75.33,1.189,1.189,0,0,0,1.06-.54,2.674,2.674,0,0,0,.36-1.51v-.52h0Zm6.23,1.25h1.14a2.231,2.231,0,0,1-2.41,2.42,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.47,2.47,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm4.49,4.79H105.8l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23ZM117.12,51.7V65.04h-.82V51.7h.82Zm7,10.18v-9.6h2.3a7.214,7.214,0,0,1,1.85.16,2.05,2.05,0,0,1,.92.52,2.471,2.471,0,0,1,.7,1.24,15.106,15.106,0,0,1,.19,3,16.425,16.425,0,0,1-.13,2.36,3.542,3.542,0,0,1-.44,1.22,2.435,2.435,0,0,1-1.01.86,6.31,6.31,0,0,1-2.08.24h-2.3Zm1.28-1.1h1.35a3.634,3.634,0,0,0,1.1-.14,1.313,1.313,0,0,0,.62-.51,3.351,3.351,0,0,0,.29-1.06,17.269,17.269,0,0,0,.1-2.06c0-.63-.01-1.21-.06-1.7a4.944,4.944,0,0,0-.12-.92,1.432,1.432,0,0,0-.64-.82,2.648,2.648,0,0,0-1.27-.24h-1.35v7.46Zm6.2-7.26V52.28h1.11v1.24Zm0,8.36V55.19h1.11v6.69Zm3.81-4.93h-1.24v-.14a1.586,1.586,0,0,1,.6-1.32,2.934,2.934,0,0,1,1.78-.46,2.531,2.531,0,0,1,1.71.48,1.842,1.842,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9s.05.59.08.87h-1.14l-.08-.84a2.317,2.317,0,0,1-.79.68,1.906,1.906,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.381,2.381,0,0,1,.23-3.22,3.481,3.481,0,0,1,2.22-.59h.65v-.33a1.883,1.883,0,0,0-.24-1.1.961.961,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.867.867,0,0,0-.3.74v.08h0Zm2.27,1.43a4.029,4.029,0,0,0-1.94.33,1.153,1.153,0,0,0-.55,1.08,1.168,1.168,0,0,0,.3.82.963.963,0,0,0,.76.33,1.2,1.2,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52Zm3.62.14a4.648,4.648,0,0,0,.3,1.95,1.007,1.007,0,0,0,.98.6,1.166,1.166,0,0,0,1.06-.6,4.328,4.328,0,0,0,.33-1.95,4.576,4.576,0,0,0-.32-2.03,1.084,1.084,0,0,0-1.03-.62,1.056,1.056,0,0,0-1.03.59,4.707,4.707,0,0,0-.3,2.06h0Zm-.78,4.11h1.3a1.077,1.077,0,0,0,.27.73.9.9,0,0,0,.7.27,1.088,1.088,0,0,0,.89-.32,2.054,2.054,0,0,0,.25-1.22v-.97a2.661,2.661,0,0,1-.74.7,1.647,1.647,0,0,1-.86.22,1.838,1.838,0,0,1-1.68-.86,5.251,5.251,0,0,1-.54-2.73,4.73,4.73,0,0,1,.54-2.6,1.816,1.816,0,0,1,1.66-.82,1.929,1.929,0,0,1,.9.19,2.2,2.2,0,0,1,.71.62l.06-.65h1.16c-.02.16-.03.33-.05.52s-.01.41-.01.67v5.87a3.346,3.346,0,0,1-.1.95,1.586,1.586,0,0,1-.28.6,1.631,1.631,0,0,1-.76.54,3.513,3.513,0,0,1-1.17.16,2.441,2.441,0,0,1-1.63-.49,1.785,1.785,0,0,1-.62-1.38h0Zm9.63-.74V57.62a2.35,2.35,0,0,0-.26-1.33.924.924,0,0,0-.85-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85h-1.16V56.25c0-.14,0-.3-.01-.48a4.683,4.683,0,0,0-.05-.59h1.18l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.881,1.881,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04h-1.16Zm2.44-3.35a4.527,4.527,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.148,2.148,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82,2.23,2.23,0,0,1-1.93-.82,4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.089,1.089,0,0,0,1.04.57,1.058,1.058,0,0,0,1.02-.57,4.436,4.436,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm3.59,3.82h1.25a1.5,1.5,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.153,1.153,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.7,2.7,0,0,1-.92-.7,1.377,1.377,0,0,1-.27-.88,1.721,1.721,0,0,1,.63-1.4,2.538,2.538,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.586,1.586,0,0,1,.6,1.3v.13h-1.24a.986.986,0,0,0-1.08-1.06,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.573,1.573,0,0,0,.52.36l1.11.56a4.528,4.528,0,0,1,1.3.89,1.687,1.687,0,0,1,.33,1.05,1.824,1.824,0,0,1-.65,1.48,3.011,3.011,0,0,1-3.47,0,2.2,2.2,0,0,1-.59-1.64h0Zm7.47-6.55V55.2h1.33v.84h-1.33v4a1.222,1.222,0,0,0,.16.75.739.739,0,0,0,.6.21c.11,0,.29,0,.52-.02h.05l-.02.86a3.046,3.046,0,0,1-.49.1,3.517,3.517,0,0,1-.45.03,1.676,1.676,0,0,1-1.19-.34,1.649,1.649,0,0,1-.35-1.17V56.05h-1.02v-.84H165V53.88l1.16-.55Zm2.38.22V52.29h1.11v1.24Zm0,8.36V55.2h1.11v6.69Zm6.31-2.25H176a2.427,2.427,0,0,1-.65,1.78,2.381,2.381,0,0,1-1.76.64,2.243,2.243,0,0,1-1.92-.81,4.831,4.831,0,0,1-.59-2.68,4.831,4.831,0,0,1,.59-2.68,2.249,2.249,0,0,1,1.92-.82,2.541,2.541,0,0,1,1.73.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.089,1.089,0,0,0,.89-.4,1.534,1.534,0,0,0,.36-1.05h0Zm2.16.22h1.25a1.383,1.383,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.17,1.17,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.809,2.809,0,0,1-.92-.7,1.39,1.39,0,0,1-.29-.88,1.709,1.709,0,0,1,.65-1.4,2.557,2.557,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.577,1.577,0,0,1,.59,1.3v.13h-1.22a1.041,1.041,0,0,0-.29-.78,1.076,1.076,0,0,0-.79-.28,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.473,1.473,0,0,0,.52.36l1.11.56a4.179,4.179,0,0,1,1.29.89,1.586,1.586,0,0,1,.35,1.05,1.8,1.8,0,0,1-.65,1.48,3.012,3.012,0,0,1-3.47,0,2.126,2.126,0,0,1-.59-1.64h0Z"
                                        fill="#565355" />
                                </g>
                            </svg>

                        </div>
                        <!--logo End-->




                    </div>
                </a>
                <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
                    <i class="icon">
                        <svg width="20px" class="icon-20" viewBox="0 0 24 24">
                            <path fill="currentColor"
                                d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z" />
                        </svg>
                    </i>
                </div>
                <div class="input-group search-input">
                    <span class="input-group-text" id="search-input">
                        <svg class="icon-18" width="18" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <circle cx="11.7669" cy="11.7666" r="8.98856" stroke="currentColor"
                                stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></circle>
                            <path d="M18.0186 18.4851L21.5426 22" stroke="currentColor" stroke-width="1.5"
                                stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </span>
                    <input type="search" class="form-control" placeholder="Search...">
                </div>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon">
                        <span class="mt-2 navbar-toggler-bar bar1"></span>
                        <span class="navbar-toggler-bar bar2"></span>
                        <span class="navbar-toggler-bar bar3"></span>
                    </span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="mb-2 navbar-nav ms-auto align-items-center navbar-list mb-lg-0">


                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link" id="notification-drop" data-bs-toggle="dropdown">
                                <svg class="icon-24" width="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M19.7695 11.6453C19.039 10.7923 18.7071 10.0531 18.7071 8.79716V8.37013C18.7071 6.73354 18.3304 5.67907 17.5115 4.62459C16.2493 2.98699 14.1244 2 12.0442 2H11.9558C9.91935 2 7.86106 2.94167 6.577 4.5128C5.71333 5.58842 5.29293 6.68822 5.29293 8.37013V8.79716C5.29293 10.0531 4.98284 10.7923 4.23049 11.6453C3.67691 12.2738 3.5 13.0815 3.5 13.9557C3.5 14.8309 3.78723 15.6598 4.36367 16.3336C5.11602 17.1413 6.17846 17.6569 7.26375 17.7466C8.83505 17.9258 10.4063 17.9933 12.0005 17.9933C13.5937 17.9933 15.165 17.8805 16.7372 17.7466C17.8215 17.6569 18.884 17.1413 19.6363 16.3336C20.2118 15.6598 20.5 14.8309 20.5 13.9557C20.5 13.0815 20.3231 12.2738 19.7695 11.6453Z"
                                        fill="currentColor"></path>
                                    <path opacity="0.4"
                                        d="M14.0088 19.2283C13.5088 19.1215 10.4627 19.1215 9.96275 19.2283C9.53539 19.327 9.07324 19.5566 9.07324 20.0602C9.09809 20.5406 9.37935 20.9646 9.76895 21.2335L9.76795 21.2345C10.2718 21.6273 10.8632 21.877 11.4824 21.9667C11.8123 22.012 12.1482 22.01 12.4901 21.9667C13.1083 21.877 13.6997 21.6273 14.2036 21.2345L14.2026 21.2335C14.5922 20.9646 14.8734 20.5406 14.8983 20.0602C14.8983 19.5566 14.4361 19.327 14.0088 19.2283Z"
                                        fill="currentColor"></path>
                                </svg>
                                <span class="bg-danger dots"></span>
                            </a>
                            <div class="p-0 sub-drop dropdown-menu dropdown-menu-end"
                                aria-labelledby="notification-drop">
                                <div class="m-0 shadow-none card">
                                    <div class="py-3 card-header d-flex justify-content-between bg-primary">
                                        <div class="header-title">
                                            <h5 class="mb-0 text-white">All Notifications</h5>
                                        </div>
                                    </div>
                                    {{-- <div class="p-0 card-body">
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/01.png" alt="">
                                <div class="ms-3 w-100">
                                  <h6 class="mb-0 ">Emma Watson Bni</h6>
                                  <div class="d-flex justify-content-between align-items-center">
                                      <p class="mb-0">95 MB</p>
                                      <small class="float-end font-size-12">Just Now</small>
                                  </div>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/02.png" alt="">
                                </div>
                                <div class="ms-3 w-100">
                                  <h6 class="mb-0 ">New customer is join</h6>
                                  <div class="d-flex justify-content-between align-items-center">
                                      <p class="mb-0">Cyst Bni</p>
                                      <small class="float-end font-size-12">5 days ago</small>
                                  </div>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/03.png" alt="">
                                <div class="ms-3 w-100">
                                  <h6 class="mb-0 ">Two customer is left</h6>
                                  <div class="d-flex justify-content-between align-items-center">
                                      <p class="mb-0">Cyst Bni</p>
                                      <small class="float-end font-size-12">2 days ago</small>
                                  </div>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/04.png" alt="">
                                <div class="w-100 ms-3">
                                  <h6 class="mb-0 ">New Mail from Fenny</h6>
                                  <div class="d-flex justify-content-between align-items-center">
                                      <p class="mb-0">Cyst Bni</p>
                                      <small class="float-end font-size-12">3 days ago</small>
                                  </div>
                                </div>
                            </div>
                          </a>
                      </div> --}}
                                </div>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link" id="mail-drop" data-bs-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                                <svg class="icon-24" width="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4"
                                        d="M22 15.94C22 18.73 19.76 20.99 16.97 21H16.96H7.05C4.27 21 2 18.75 2 15.96V15.95C2 15.95 2.006 11.524 2.014 9.298C2.015 8.88 2.495 8.646 2.822 8.906C5.198 10.791 9.447 14.228 9.5 14.273C10.21 14.842 11.11 15.163 12.03 15.163C12.95 15.163 13.85 14.842 14.56 14.262C14.613 14.227 18.767 10.893 21.179 8.977C21.507 8.716 21.989 8.95 21.99 9.367C22 11.576 22 15.94 22 15.94Z"
                                        fill="currentColor"></path>
                                    <path
                                        d="M21.4759 5.67351C20.6099 4.04151 18.9059 2.99951 17.0299 2.99951H7.04988C5.17388 2.99951 3.46988 4.04151 2.60388 5.67351C2.40988 6.03851 2.50188 6.49351 2.82488 6.75151L10.2499 12.6905C10.7699 13.1105 11.3999 13.3195 12.0299 13.3195C12.0339 13.3195 12.0369 13.3195 12.0399 13.3195C12.0429 13.3195 12.0469 13.3195 12.0499 13.3195C12.6799 13.3195 13.3099 13.1105 13.8299 12.6905L21.2549 6.75151C21.5779 6.49351 21.6699 6.03851 21.4759 5.67351Z"
                                        fill="currentColor"></path>
                                </svg>
                                <span class="bg-primary count-mail"></span>
                            </a>
                            <div class="p-0 sub-drop dropdown-menu dropdown-menu-end" aria-labelledby="mail-drop">
                                <div class="m-0 shadow-none card">
                                    <div class="py-3 card-header d-flex justify-content-between bg-primary">
                                        <div class="header-title">
                                            <h5 class="mb-0 text-white">All Message</h5>
                                        </div>
                                    </div>
                                    {{-- <div class="p-0 card-body ">
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/01.png" alt="">
                                </div>
                                <div class="ms-3">
                                  <h6 class="mb-0 ">Bni Emma Watson</h6>
                                  <small class="float-start font-size-12">13 Jun</small>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/02.png" alt="">
                                </div>
                                <div class="ms-3">
                                  <h6 class="mb-0 ">Lorem Ipsum Watson</h6>
                                  <small class="float-start font-size-12">20 Apr</small>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/03.png" alt="">
                                </div>
                                <div class="ms-3">
                                  <h6 class="mb-0 ">Why do we use it?</h6>
                                  <small class="float-start font-size-12">30 Jun</small>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/04.png" alt="">
                                </div>
                                <div class="ms-3">
                                  <h6 class="mb-0 ">Variations Passages</h6>
                                  <small class="float-start font-size-12">12 Sep</small>
                                </div>
                            </div>
                          </a>
                          <a href="#" class="iq-sub-card">
                            <div class="d-flex align-items-center">
                                <div class="">
                                  <img class="p-1 avatar-40 rounded-pill bg-soft-primary" src="/images/shapes/05.png" alt="">
                                </div>
                                <div class="ms-3">
                                  <h6 class="mb-0 ">Lorem Ipsum generators</h6>
                                  <small class="float-start font-size-12">5 Dec</small>
                                </div>
                            </div>
                          </a>
                      </div> --}}
                                </div>
                            </div>
                        </li>
                        {{-- <li class="nav-item dropdown">
                <a class="py-0 nav-link d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <img src="/images/avatars/01.png" alt="User-Profile" class="theme-color-default-img img-fluid avatar avatar-50 avatar-rounded">
                  <img src="/images/avatars/avtar_1.png" alt="User-Profile" class="theme-color-purple-img img-fluid avatar avatar-50 avatar-rounded">
                  <img src="/images/avatars/avtar_2.png" alt="User-Profile" class="theme-color-blue-img img-fluid avatar avatar-50 avatar-rounded">
                  <img src="/images/avatars/avtar_4.png" alt="User-Profile" class="theme-color-green-img img-fluid avatar avatar-50 avatar-rounded">
                  <img src="/images/avatars/avtar_5.png" alt="User-Profile" class="theme-color-yellow-img img-fluid avatar avatar-50 avatar-rounded">
                  <img src="/images/avatars/avtar_3.png" alt="User-Profile" class="theme-color-pink-img img-fluid avatar avatar-50 avatar-rounded">
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                  <li><a class="dropdown-item" href="../dashboard/app/user-profile.html">Profile</a></li>
                  <li><a class="dropdown-item" href="../dashboard/app/user-privacy-setting.html">Privacy Setting</a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="../dashboard/auth/sign-in.html">Logout</a></li>
                </ul>
              </li> --}}
                    </ul>
                </div>
            </div>
        </nav>
        <div class="container-fluid content-inner p-3">


            <style>
                html {
                    font-size: 62.5%;
                }

                .sidebar,
                .nav.navbar,
                .footer {
                    display: none !important;
                }

                .main-content {
                    margin: 0 !important;
                }

                body .content-inner {
                    padding: 0 !important;
                }
            </style>

            <!-- <div class="container">
            <form class="form-signin" method="post" action="auth/sign_up">
                <h2 class="login form-signin-heading">Doctor Clinic<br/><br/><img style="height: 60px;"
                alt="" src="https://ik.imagekit.io/clirnetPlatform/mymd/wp-content/themes/mymdnew/images/logo.svg">
               Sign UP test</h2>
                <div id="infoMessage" style="color: green;"></div>
                <div class="login-wrap">
                    <input type="text" class="form-control" name="identity" placeholder="User Email" autofocus>
                    <input type="password" class="form-control"  name="password" placeholder="Password">



                    <button class="btn btn-lg btn-login btn-block" type="submit">Sign in</button>
                </div>



            </form>

        </div>









        <div aria-hidden="true" aria-labelledby="myModalLabel" role="dialog" tabindex="-1" id="myModal" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form method="post" action="auth/forgot_password">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                            <h4 class="modal-title">Forgot Password ?</h4>
                        </div>

                        <div class="modal-body">
                            <p>Enter your e-mail address below to reset your password.</p>
                            <input type="text" name="email" placeholder="Email" autocomplete="off" class="form-control placeholder-no-fix">

                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" class="btn btn-default" type="button">Cancel</button>
                            <input class="btn detailsbutton" type="submit" name="submit" value="submit">
                        </div>
                    </form>
                </div>
            </div>
        </div> -->




            <!-------New login page starts here-------->




            <div class="wrapper1">
                <section class="login-content w-100 d-flex justify-content-center vh-100  p-md-5 "
                    style="background-image:url(https://storage.googleapis.com/medwiki/43_server/images/1692177310_login-bg-wrap.jpg); background-repeat:no-repeat; background-size:cover; ">


                    <div class="col-md-12 col-lg-10 py-5 ">
                        <div class="row m-0 align-items-center bg-white overflow-hidden shadow-lg rounded-5 h-100">

                            <div
                                class="col-md-6  d-none  h-100 d-md-flex align-items-center bg-light justify-content-center ">
                                <img src="https://storage.googleapis.com/medwiki/43_server/images/1692183648_login-infographic1.jpg"
                                    class="img-fluid  w-75" alt="images">
                            </div>



                            <div class="col-md-6 h-100 align-items-center justify-content-center d-flex"
                                style="background-image: url(https://storage.googleapis.com/medwiki/43_server/images/1692017071_login-icons-bg.png); background-size: 100%; background-repeat: no-repeat; background-position: bottom left;">
                                <div class="row align-items-center justify-content-center w-100">
                                    <div class="col-md-8 ">
                                        <div
                                            class="card card-transparent shadow-none  d-flex justify-content-center mb-0 auth-card">
                                            <div class="card-body flex-wrap w-100">

                                                <div class="w-100  text-center logo-main">
                                                    <div class="logo-normal">
                                                        <img style="height: 60px;" alt=""
                                                            src="https://storage.googleapis.com/prod-mymd/image/profile_image/1707204126_MYMD.png">
                                                    </div>
                                                </div>

                                                <form class="form-signin position-relative mt-4 pt-5" method="post"
                                                    action="{{ config('authorization.auth_url') . 'login' }}"
                                                    id="login">

                                                    <div class="row justify-content-center">
                                                        <div id="infoMessage"
                                                            class="col-lg-12 position-absolute top-0 h5 mb-2 text-center"
                                                            style="color: red;"></div>
                                                        <div class="col-lg-12">


                                                            <div class="form-group">
                                                                <label for="email"
                                                                    class="form-label h5">Email</label>
                                                                <input type="text" class="form-control"
                                                                    name="email" id="email"
                                                                    placeholder="User Email" autofocus="">
                                                                <!-- <input type="email" class="form-control" id="email" aria-describedby="email" placeholder=" "> -->
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12">
                                                            <div class="form-group">
                                                                <label for="password"
                                                                    class="form-label h5">Password</label>
                                                                <input type="password" class="form-control"
                                                                    name="password" id="password"
                                                                    placeholder="Password">
                                                                <!-- <input type="password" class="form-control" id="password" aria-describedby="password" placeholder=" "> -->
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12 d-flex justify-content-end mb-3">
                                                            <a href="#" class="h5 text-primary" onclick="forgotPassword()">Forgot
                                                                Password?</a>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-center">
                                                        <button class="btn btn-lg btn-primary py-3 px-5 h4 rounded-2"
                                                            type="submit">Sign in</button>
                                                    </div>
                                                </form>
                                                <form class="form-signin position-relative mt-4 pt-5" method="post"
                                                    action="{{ config('authorization.auth_url') . 'otpLogin' }}"
                                                    id="loginClinic" style="display: none;     min-height: 204px;">

                                                    <div class="row justify-content-center">
                                                        <div id="infoMessageClinic"
                                                            class="col-lg-12 position-absolute top-0 h5 mb-2 text-center"
                                                            style="color: red;"></div>
                                                        <div class="col-lg-12">


                                                            <div class="form-group">
                                                                <label for="phone"
                                                                    class="form-label h5">Phone</label>
                                                                <input type="number" class="form-control"
                                                                    name="phone" id="phone"
                                                                    placeholder="User Phone Number" autofocus="">
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="d-flex justify-content-center">
                                                        <button class="btn btn-lg btn-primary py-3 px-5 h4 rounded-2"
                                                            type="submit">Send OTP</button>
                                                    </div>
                                                </form>
                                                <form class="form-signin position-relative mt-4 pt-5" method="post"
                                                    action="{{ config('authorization.auth_url') . 'otpVerifyLogin' }}"
                                                    id="loginVerifyClinic" style="display:none; min-height: 204px;">

                                                    <div class="row justify-content-center">
                                                        <div class="col-md-10">
                                                            <div class="position-relative col-md-12 bg-light  h5 mb-2 text-start py-4 w-100 border-start border-3 border-primary rounded-2 shadow-sm"
                                                                style="top: -10px; padding-left:15px;">
                                                                <div
                                                                    class="fw-normal text-start row flex-nowrap mb-2">
                                                                    <span
                                                                        class="fw-normal h5 text-dark col-md-4 col-4 ">
                                                                        Name
                                                                    </span>
                                                                    <span class="text-dark fw-bold col-md-8 col-8"
                                                                        id="profilename">
                                                                    </span>
                                                                </div>
                                                                <div
                                                                    class="fw-normal text-start row flex-nowrap mb-2">
                                                                    <span
                                                                        class="fw-normal h5 text-dark col-md-4 col-4 ">
                                                                        Phone
                                                                    </span>
                                                                    <span class="text-dark fw-bold col-md-8 col-8"
                                                                        id="profilephno">
                                                                    </span>
                                                                </div>
                                                                <div class="fw-normal text-start row flex-nowrap">
                                                                    <span
                                                                        class="fw-normal h5 text-dark col-md-4 col-4 ">
                                                                        Designation
                                                                    </span>
                                                                    <span class="text-dark fw-bold col-md-8 col-8"
                                                                        id="profiledesignation">
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="infoMessageVerifyClinic"
                                                            class="col-lg-12 position-absolute top-0 h5 mb-2 text-center"
                                                            style="color: red;"></div>
                                                        <div class="col-lg-12">
                                                            <input type="hidden" name="phone"
                                                                id="verify_phone">
                                                            <div class="form-group">
                                                                <label for="otp" class="form-label h5">Please
                                                                    enter OTP</label>
                                                                <input type="number" class="form-control"
                                                                    name="otp" id="otp"
                                                                    placeholder="OTP" autofocus="">
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12 mb-4 mt-2">
                                                            <div class="row justify-content-between">
                                                                <div class="col-auto">
                                                                    <h6 class="d-flex flex-wrap gap-1">
                                                                        Time Remaining
                                                                        <span>:</span>
                                                                        <span class="timerOtpExpire">00:00</span>
                                                                    </h6>
                                                                </div>
                                                                <div class="col-auto">
                                                                    <button type="button" class="text-primary h6 border-0 p-0 bg-transparent resendOTP" onclick="resendOTP()" disabled>
                                                                        Resend OTP
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-center">
                                                        <button class="btn btn-lg btn-primary py-3 px-5 h4 rounded-2"
                                                            type="submit">Verify OTP</button>
                                                    </div>
                                                    <div class="d-flex justify-content-center mt-5">
                                                        <h5>
                                                            <a href="#"
                                                                class="h5 text-dark fw-medium text-decoration-underline"
                                                                onclick="previousDiv()">
                                                                Not Your Number? Switch Account
                                                            </a>
                                                        </h5>
                                                    </div>
                                                </form>
                                                <form class="form-signin position-relative mt-4 pt-5" method="post"
                                                    action="{{ config('authorization.auth_url') . 'forgotPassword' }}"
                                                    id="forgotPasswordForm" style="display: none;     min-height: 204px;">

                                                    <div class="row justify-content-center">
                                                        <div id="infoMessageForgotPassword"
                                                            class="col-lg-12 position-absolute top-0 h5 mb-2 text-center"
                                                            style="color: red;"></div>
                                                        <div class="col-lg-12">


                                                            <div class="form-group">
                                                                <label for="email"
                                                                    class="form-label h5">Email</label>
                                                                <input type="text" class="form-control"
                                                                    name="email" id="email"
                                                                    placeholder="User Email" autofocus="">
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-12 d-flex justify-content-end mb-3">
                                                            <a href="#" class="h5 text-primary" onclick="previousforgotPassword()">Back</a>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-center">
                                                        <button class="btn btn-lg btn-primary py-3 px-5 h4 rounded-2"
                                                            type="submit">Submit</button>
                                                    </div>
                                                </form>
                                                <div class="d-flex justify-content-center mt-5">
                                                    <h5>
                                                        <a href="#"
                                                            class="h5 text-dark fw-medium text-decoration-underline"
                                                            data-id="Clinic" onclick="changeLoginType(this)">
                                                            Clinic Login
                                                        </a>
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>


                        <!-- <div class="sign-bg">
               <svg class="icon-150"  width="150" viewBox="0 0 20 20" opacity="0.07" fill="none" xmlns="http://www.w3.org/2000/svg">
               <g>
                      <line fill="#565355" x1="12.44" y1="19.24" x2="12.45" y2="12.45"/>
                      <rect fill="#dd2a1b" x="6.77" width="5.68" height="19.23"/>
                      <rect fill="#dd2a1b" y="6.77" width="19.23" height="5.68"/>
                      <polygon fill="#565355" points="12.44 6.78 19.22 6.78 19.23 12.45 12.45 12.45 12.45 19.23 6.77 19.23 6.77 12.45 12.44 6.78"/>
                      <polygon fill="#dd2a1b" points="14.23 19.23 14.22 14.23 19.22 14.23 14.23 19.23"/>
                    </g>
                </svg>
               </div> -->
                    </div>
                </section>
            </div>

            <!-------New login page ends here-------->







        </div>

        <!-- Footer Section Start -->
        <footer class="footer">
            <div class="footer-body">
                <ul class="left-panel list-inline mb-0 p-0">
                    <li class="list-inline-item"><a href="../dashboard/extra/privacy-policy.html">Privacy Policy</a>
                    </li>
                    <li class="list-inline-item"><a href="../dashboard/extra/terms-of-service.html">Terms of Use</a>
                    </li>
                </ul>
                <div class="right-panel">
                    ©
                    <script>
                        document.write(new Date().getFullYear())
                    </script> myMd, Made with
                    <span class="">
                        <svg class="icon-15" width="15" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M15.85 2.50065C16.481 2.50065 17.111 2.58965 17.71 2.79065C21.401 3.99065 22.731 8.04065 21.62 11.5806C20.99 13.3896 19.96 15.0406 18.611 16.3896C16.68 18.2596 14.561 19.9196 12.28 21.3496L12.03 21.5006L11.77 21.3396C9.48102 19.9196 7.35002 18.2596 5.40102 16.3796C4.06102 15.0306 3.03002 13.3896 2.39002 11.5806C1.26002 8.04065 2.59002 3.99065 6.32102 2.76965C6.61102 2.66965 6.91002 2.59965 7.21002 2.56065H7.33002C7.61102 2.51965 7.89002 2.50065 8.17002 2.50065H8.28002C8.91002 2.51965 9.52002 2.62965 10.111 2.83065H10.17C10.21 2.84965 10.24 2.87065 10.26 2.88965C10.481 2.96065 10.69 3.04065 10.89 3.15065L11.27 3.32065C11.3618 3.36962 11.4649 3.44445 11.554 3.50912C11.6104 3.55009 11.6612 3.58699 11.7 3.61065C11.7163 3.62028 11.7329 3.62996 11.7496 3.63972C11.8354 3.68977 11.9247 3.74191 12 3.79965C13.111 2.95065 14.46 2.49065 15.85 2.50065ZM18.51 9.70065C18.92 9.68965 19.27 9.36065 19.3 8.93965V8.82065C19.33 7.41965 18.481 6.15065 17.19 5.66065C16.78 5.51965 16.33 5.74065 16.18 6.16065C16.04 6.58065 16.26 7.04065 16.68 7.18965C17.321 7.42965 17.75 8.06065 17.75 8.75965V8.79065C17.731 9.01965 17.8 9.24065 17.94 9.41065C18.08 9.58065 18.29 9.67965 18.51 9.70065Z"
                                fill="currentColor"></path>
                        </svg>
                    </span> by <a href="https://www.mymdindia.com/">myMD</a>.
                </div>
            </div>
        </footer>
        <!-- Footer Section End -->
    </main>

    <!-- Wrapper End-->
    <!-- offcanvas start -->

    <!-- Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/libs.min.js') }}"></script>

    <!-- External Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/external.min.js') }}"></script>

    <!-- Widgetchart Script -->
    <script src="{{ asset('hope-ui/assets/js/charts/widgetcharts.js') }}"></script>

    <!-- mapchart Script -->
    <script src="{{ asset('hope-ui/assets/js/charts/vectore-chart.js') }}"></script>
    <script src="{{ asset('hope-ui/assets/js/charts/dashboard.js') }}"></script>

    <!-- fslightbox Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/fslightbox.js') }}"></script>

    <!-- Settings Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/setting.js') }}"></script>

    <!-- Slider-tab Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/slider-tabs.js') }}"></script>

    <!-- Form Wizard Script -->
    <script src="{{ asset('hope-ui/assets/js/plugins/form-wizard.js') }}"></script>

    <!-- AOS Animation Plugin-->

    <!-- App Script -->
    <script src="{{ asset('hope-ui/assets/js/hope-ui.js') }}" defer></script>

    <!-- Flatpickr css -->
    <script src="{{ asset('hope-ui/assets/vendor/flatpickr/dist/flatpickr.min.js') }}"></script>





    <!-- Patient portal js start -->

    {{-- <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script> --}}
    <!--  Flatpickr  -->


    <!-- apexchart external js for delete  -->

    <!-- External Library Bundle Script -->
    <script src="{{ asset('hope-ui/assets/js/core/external.min.js') }}"></script>



    <!-- apexchart external js for delete  -->

    <script src="{{ asset('hope-ui/assets/js/charts/utility.min.js') }}"></script>
    <script src="{{ asset('hope-ui/assets/js/custom/health_summary_apexcharts.js') }}"></script>






    <!--Select2 js-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- sweetalert Js -->
    <script rel='text/javascript' src="{{ asset('hope-ui/assets//js/sweetalert.min.js') }}"></script>
    <script src="{{ asset('hope-ui/assets/js/custom.js') }}"></script>

    {{-- <script>
    $(function test() {
        $('.select2-multpl-custom').select2({
            dropdownParent: $('.selct-modal')
        });
    });
    </script>

    <script>
    $(function test() {
        $('.select2-multpl-custom1').select2({
            dropdownParent: $('.selct-modal1')
        });
    });

    </script>

    <script>
    $(function test() {
        $('.select2-multpl-custom2').select2({
            dropdownParent: $('.selct-modal2')
        });
    });
    $(function test() {
        $('.select2-multpl-custom4').select2({
            dropdownParent: $('.selct-modal4')
        });
    });
    </script> --}}
    <script>
        // setCookie(cookieAuth, '', 15);
        // console.log(getCookie(cookieAuth));
        let redirect = "{{ route('user.dashboard') }}";
        $(document).ready(function() {
            localStorage.removeItem('targetId');
            success();
        });

        function success() {
            // user.dashboard
            let user = getCookie(cookieAuth);
            if (!user) {
                return false;
            }
            let path = redirect.replace(/^https?:\/\/[^\/]+/, '');
            if(path == '/appointment/list/all'){
                localStorage.setItem('targetId', 'appointment-all');
            }
            else{
                localStorage.removeItem('targetId');
            }
            window.location.href = redirect;
        }
        let divOtp = false;
        function changeLoginType(params) {
            let loginType = $(params).attr('data-id');
            let textId;
            $('#login').hide();
            $('#loginClinic').hide();
            $('#loginVerifyClinic').hide();
            $('#forgotPasswordForm').hide();
            if (loginType == 'Clinic') {
                if (divOtp == true) {
                    $('#loginVerifyClinic').show();
                } else {
                    $('#loginClinic').show();
                }
                textId = 'Admin';
            } else {
                $('#login').show();
                textId = 'Clinic';
            }
            $(params).attr('data-id', textId);
            $(params).text(textId + ' Login');
            console.log(loginType);
        }
        function forgotPassword() {
            $('#login').hide();
            $('#loginClinic').hide();
            $('#loginVerifyClinic').hide();
            $('#forgotPasswordForm').show();
        }
        function previousforgotPassword() {
            $('#login').show();
            $('#loginClinic').hide();
            $('#loginVerifyClinic').hide();
            $('#forgotPasswordForm').hide();
        }
        function previousDiv() {
            $('#login').hide();
            $('#loginClinic').hide();
            $('#loginVerifyClinic').hide();
            $('#loginClinic').show();
        }
        $("#login").submit(function(e) {
            e.preventDefault();
            var formData = new FormData($("#login")[0]);

            // var loginForm = $(this).serializeArray();
            // var loginFormObject = {};
            // $.each(loginForm, function(i, v) {
            //     loginFormObject[v.name] = v.value;
            // });
            // console.log(formData,loginFormObject);
            // return false;
            var parentForm = $(this).closest('form');
            // console.log(parentForm);
            var action = $(this).attr("action");
            var method = $(this).attr("method");
            var btn_text = $(this).find(":submit").html();
            $.ajax({
                type: method,
                url: action,
                crossDomain: true,
                dataType: 'json',
                catch: false,
                // contentType: "application/json", // Correct header for sending JSON data
                contentType: false,
                headers: {
                    'Accept': 'application/json', // 'Accept' should be capitalized
                    // 'Authorization': 'Bearer ' + yourJwtToken // Include JWT token if authentication is required
                },
                // data: JSON.stringify(loginFormObject), // Convert data object to JSON string
                data: formData,
                processData: false,
                beforeSend: function() {
                    parentForm.find(":submit").prop('disabled', true);
                    btn_text = parentForm.find(":submit").html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    parentForm.find(":submit").html(ht_data);
                },
                success: function(data) {
                    // console.log(data.redirect);
                    expires_minutes = Number(data.expires_in) / 60;
                    console.log(expires_minutes);
                    setCookie(cookieAuth, data.access_token, expires_minutes);
                    // let user = getCookie(cookieAuth);
                    // console.log(user);
                    redirect = data.redirect;
                    success();
                },
                error: function(response) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);

                    $('#infoMessage').html('');
                    if (response.status === 422) {
                        var errors = response.responseJSON;
                        // console.error(errors);
                        $.each(errors, function(key, value) {
                            $('#infoMessage').append('<p>' + value[0] + '</p>');
                        });
                    } else if (response.status === 401) {
                        var errors = response.responseJSON;
                        // console.error(errors.error);
                        $('#infoMessage').append('<p>' + errors.error + '</p>');
                    } else {
                        $('#infoMessage').append('<p>Something went wrong. Please try again.</p>');
                    }
                }
            });
        });
        
        function resendOTP() {
            $("#loginClinic").submit();
        }
        $("#loginClinic").submit(function(e) {
            $('#otp').prop('disabled', false);
            $('.resendOTP').prop('disabled', true);
            e.preventDefault();
            var formData = new FormData($("#loginClinic")[0]);
            var parentForm = $(this).closest('form');
            // console.log(parentForm);
            var action = $(this).attr("action");
            var method = $(this).attr("method");
            var btn_text = $(this).find(":submit").html();
            $.ajax({
                type: method,
                url: action,
                crossDomain: true,
                dataType: 'json',
                catch: false,
                // contentType: "application/json", // Correct header for sending JSON data
                contentType: false,
                headers: {
                    'Accept': 'application/json', // 'Accept' should be capitalized
                    // 'Authorization': 'Bearer ' + yourJwtToken // Include JWT token if authentication is required
                },
                // data: JSON.stringify(loginFormObject), // Convert data object to JSON string
                data: formData,
                processData: false,
                beforeSend: function() {
                    parentForm.find(":submit").prop('disabled', true);
                    btn_text = parentForm.find(":submit").html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    parentForm.find(":submit").html(ht_data);
                },
                success: function(data) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);
                    $('#verify_phone').val(data.data.user.phone);
                    $('#profilename').html(data.data.user.username);
                    $('#profilephno').html(data.data.user.phone);
                    $('#profiledesignation').html(data.data.role);
                    $('#loginClinic').hide();
                    $('#loginVerifyClinic').show();
                    divOtp = true;
                    $('#infoMessageClinic').html('');
                    // expires_minutes = Number(data.expires_in) / 60;
                    // console.log(expires_minutes);
                    // setCookie(cookieAuth, data.access_token, expires_minutes);
                    // // let user = getCookie(cookieAuth);
                    // console.log(data.data.otp_expire);
                    // $('.timerOtpExpire').html(data.data.otp_expire);
                    let durationInSeconds = data.data.otp_expire * 60;
                    startOTPTimer(durationInSeconds,'timerOtpExpire','resendOTP','otp');
                    // success();
                },
                error: function(response) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);

                    $('#infoMessageClinic').html('');
                    if (response.status === 422) {
                        var errors = response.responseJSON;
                        // console.error(errors);
                        $.each(errors, function(key, value) {
                            $('#infoMessageClinic').append('<p>' + value[0] + '</p>');
                        });
                    } else if (response.status === 401) {
                        var errors = response.responseJSON;
                        // console.error(errors.error);
                        $('#infoMessageClinic').append('<p>' + errors.error + '</p>');
                    } else {
                        $('#infoMessageClinic').append(
                            '<p>Something went wrong. Please try again.</p>');
                    }
                }
            });
        });
        $("#loginVerifyClinic").submit(function(e) {
            e.preventDefault();
            var formData = new FormData($("#loginVerifyClinic")[0]);
            var parentForm = $(this).closest('form');
            // console.log(parentForm);
            var action = $(this).attr("action");
            var method = $(this).attr("method");
            var btn_text = $(this).find(":submit").html();
            $.ajax({
                type: method,
                url: action,
                crossDomain: true,
                dataType: 'json',
                catch: false,
                // contentType: "application/json", // Correct header for sending JSON data
                contentType: false,
                headers: {
                    'Accept': 'application/json', // 'Accept' should be capitalized
                    // 'Authorization': 'Bearer ' + yourJwtToken // Include JWT token if authentication is required
                },
                // data: JSON.stringify(loginFormObject), // Convert data object to JSON string
                data: formData,
                processData: false,
                beforeSend: function() {
                    parentForm.find(":submit").prop('disabled', true);
                    btn_text = parentForm.find(":submit").html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    parentForm.find(":submit").html(ht_data);
                },
                success: function(data) {
                    expires_minutes = Number(data.expires_in) / 60;
                    console.log(expires_minutes);
                    setCookie(cookieAuth, data.access_token, expires_minutes);
                    // let user = getCookie(cookieAuth);
                    // console.log(user);
                    success();
                },
                error: function(response) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);

                    $('#infoMessageVerifyClinic').html('');
                    if (response.status === 422) {
                        var errors = response.responseJSON;
                        // console.error(errors);
                        $.each(errors, function(key, value) {
                            $('#infoMessageVerifyClinic').append('<p>' + value[0] + '</p>');
                        });
                    } else if (response.status === 401) {
                        var errors = response.responseJSON;
                        // console.error(errors.error);
                        $('#infoMessageVerifyClinic').append('<p>' + errors.error + '</p>');
                    } else {
                        $('#infoMessageVerifyClinic').append(
                            '<p>Something went wrong. Please try again.</p>');
                    }
                }
            });
        });
        $("#forgotPasswordForm").submit(function(e) {
            e.preventDefault();
            var formData = new FormData($("#forgotPasswordForm")[0]);
            var parentForm = $(this).closest('form');
            // console.log(parentForm);
            var action = $(this).attr("action");
            var method = $(this).attr("method");
            var btn_text = $(this).find(":submit").html();
            $.ajax({
                type: method,
                url: action,
                crossDomain: true,
                dataType: 'json',
                catch: false,
                // contentType: "application/json", // Correct header for sending JSON data
                contentType: false,
                headers: {
                    'Accept': 'application/json', // 'Accept' should be capitalized
                    // 'Authorization': 'Bearer ' + yourJwtToken // Include JWT token if authentication is required
                },
                // data: JSON.stringify(loginFormObject), // Convert data object to JSON string
                data: formData,
                processData: false,
                beforeSend: function() {
                    parentForm.find(":submit").prop('disabled', true);
                    btn_text = parentForm.find(":submit").html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    parentForm.find(":submit").html(ht_data);
                },
                success: function(data) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);
                    console.log(data);
                    if (data.hasOwnProperty('success')) {
                        $("#forgotPasswordForm")[0].reset();
                        if (data.success == true) {
                            successAlertSwal(data);
                        }
                        else {
                            errorAlertSwal(data);
                        }
                    }
                },
                error: function(response) {
                    parentForm.find(":submit").prop('disabled', false);
                    parentForm.find(":submit").html(btn_text);

                    $('#infoMessageForgotPassword').html('');
                    if (response.status === 422) {
                        var errors = response.responseJSON;
                        // console.error(errors);
                        $.each(errors, function(key, value) {
                            $('#infoMessageForgotPassword').append('<p>' + value[0] + '</p>');
                        });
                    } else if (response.status === 401) {
                        var errors = response.responseJSON;
                        // console.error(errors.error);
                        $('#infoMessageForgotPassword').append('<p>' + errors.error + '</p>');
                    } else {
                        $('#infoMessageForgotPassword').append(
                            '<p>Something went wrong. Please try again.</p>');
                    }
                }
            });
        });
    </script>

</body>

</html>
