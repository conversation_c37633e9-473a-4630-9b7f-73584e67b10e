<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['patient']['name'] ?? ''); ?></td>
        <td><?php echo e($source_type_list[$row['source_type']] ?? ''); ?></td>
        <td><?php echo e(date('Y-m-d', strtotime($row['created_at']))); ?></td>
        <td><?php echo e($row['patient']['phone'] ?? '' ? $row['patient']['phone'] ?? '' : $row['patient']['parent']['phone'] ?? ''); ?>

        </td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
        <td>
            <?php if(count($row['reviews_survey_result_child']) > 0): ?>
                <div class="table-content">
                    <div class="tooltip1">
                        <div class="tr" onclick="toggleTooltip(this)">
                            <div
                                class="td btn btn-light mt-0 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center">
                                <svg clip-rule=evenodd fill-rule=evenodd height=20 id=fi_11524741
                                    image-rendering=optimizeQuality shape-rendering=geometricPrecision
                                    text-rendering=geometricPrecision viewBox="0 0 14872 14872"width=20
                                    xmlns=http://www.w3.org/2000/svg>
                                    <g id=Layer_x0020_1>
                                        <g id=_959695072>
                                            <path
                                                d="m0 0h10896v1147c-194 9-386 32-572 66v-640h-9751v13726h9751v-754h572v1327h-10896zm10896 9746v1673h-572v-1739c186 34 378 57 572 66z"
                                                fill=#333></path>
                                            <path
                                                d="m12361 11959h-528v1046h528c288 0 523-235 523-523 0-288-235-523-523-523zm-7364 0h6459v1046h-6459zm-338 0-1055 399h-426c-69 0-124 56-124 124 0 68 55 124 124 124h426l1055 399v-523z"
                                                fill=#04599c></path>
                                        </g>
                                        <path
                                            d="m11110 1685c2078 0 3762 1684 3762 3761 0 2078-1684 3762-3762 3762-406 0-797-65-1164-184-396 360-1050 681-1870 569 0 0 565-491 838-1093-948-683-1565-1796-1565-3054 0-2077 1684-3761 3761-3761zm0 2433c341 0 618-277 618-618s-277-617-618-617c-340 0-617 276-617 617s277 618 617 618zm-348 3805h697c128 0 232-105 232-233v-2741c0-128-104-232-232-232h-697c-128 0-232 104-232 232v2741c0 128 104 233 232 233z"
                                            fill=#04599c></path>
                                        <g fill=#333>
                                            <path d="m4096 1866h3447v697h-3447z"></path>
                                            <path d="m4096 3319h2576v697h-2576z"></path>
                                            <path d="m4096 6840h2816v697h-2816z"></path>
                                            <path d="m4096 8293h2576v697h-2576z"></path>
                                        </g>
                                        <path
                                            d="m1392 2794 677 462 1071-1540 476 331-1234 1776-164 236-238-162-915-625z"
                                            fill=#04599c fill-rule=nonzero></path>
                                        <path
                                            d="m1392 7651 677 463 1071-1541 476 332-1234 1776-164 236-238-162-915-625z"
                                            fill=#04599c fill-rule=nonzero></path>
                                    </g>
                                    <g id=boxes>
                                        <path d="m0 0h14872v14872h-14872z" fill=none></path>
                                    </g>
                                </svg>

                                <span class="tooltiptext">
                                    <span class="d-flex gap-2 "
                                        style="border-bottom: 1px solid #c2c2c2; background-color:#343434; padding:8px;     border-top-left-radius: 6px;     border-top-right-radius: 6px;">
                                        <p class="mb-0" style="white-space: normal;">
                                            <?php echo e($row['reviews_survey_master']['survey_title'] ?? ''); ?>

                                        </p>
                                    </span>
                                    <span class=" p-2 d-block"
                                        style="padding:8px;background-color: #626262;border-bottom: 1px solid #c2c2c2; border-top-left-radius: 6px;     border-top-right-radius: 6px;">
                                        <?php $__currentLoopData = $row['reviews_survey_result_child']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <p class="mb-0 w-100" style="white-space: normal;">
                                                <strong><?php echo e($row2['question']); ?>:</strong>
                                                <?php echo e($row2['answer']); ?>

                                            </p>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Settings\resources/views/reviewSurvey/api/viewResponseList.blade.php ENDPATH**/ ?>