<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\Test;
use Modules\Report\Models\Department;
use DB;

class DiagnosticTestService extends ApplicationDefaultService
{
    public $entity;
    public $entityDepartment;
    public $columns = [
        'id',
        'itdose_testid',
        'test_name',
        'test_price',
        'test_code',
        'sample_type_id',
        'sample_type',
        'delivery_date',
        'department_id',
        'sub_parameter_name',
        'method',
        'test_information',
        'sample_quantity',
        'package_test',
        'is_package',
        'is_inhouse',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'itdose_testid',
        'test_name',
        'test_price',
        'test_code',
        'sample_type_id',
        'sample_type',
        'delivery_date',
        'department_id',
        'sub_parameter_name',
        'method',
        'test_information',
        'sample_quantity',
        'package_test',
        'is_package',
        'is_inhouse',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(Test $entity,Department $entityDepartment) {
        $this->entity =$entity;
        $this->entityDepartment =$entityDepartment;
    }
    public function allDepartments(){
        $this->entityDepartment = $this->entityDepartment->select('id','name')
            ->where('status',1)
            ->get();
        return $this->entityDepartment;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            Test::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
        });
    }
}
