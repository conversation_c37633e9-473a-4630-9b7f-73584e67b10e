@foreach ($data['rows'] as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row['sample_collectionbreakup']['unique_id'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['date_of_collection'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['clinic']['clinic_name'] ?? '' }}</td>
        <td>{{ date('Y-m-d H:i:s', strtotime($row['sample_collectionbreakup']['created_at'])) ?? '' }}</td>
        <td>{{ $visit_type[$row['sample_collectionbreakup']['type_of_collection']] ?? '' }}</td>
        <td>{{ $row['test']['test_name'] ?? '' }}</td>
        <td>{{ $row['test']['department_id'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['patient']['name'] ?? '' }}</td>
        <td>{{ Helper::ageCalculator($row['sample_collectionbreakup']['patient']['birthdate'] ?? '') }}</td>
        <td>{{ $row['sample_collectionbreakup']['patient']['sex'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['patient']['phone'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['doctor_name'] ?? '' }}</td>
        <td>{{ $diagnostic_docotor_type[$row['sample_collectionbreakup']['doctortype']] ?? '' }}</td>
        <td>{{ $row['user_doctors']['schedule_doctor']['itdose_doctorid'] ?? '' }}</td>
        <td>{{ $row['sample_collectionbreakup']['offered_name'] ?? '' }}</td>

        <td></td>
        <td>{{ $row['amount'] ?? '0' }}</td>
        <td>{{ $row['discount'] ?? '' }}</td>
        <td> {{ $row['net_amount'] ?? '' }}</td>

    </tr>
@endforeach
