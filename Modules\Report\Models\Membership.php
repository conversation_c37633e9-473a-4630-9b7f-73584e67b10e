<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Membership extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'category_id',
        'name',
        'price',
        'duration',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function serviceCategorys(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'category_id', 'id');
    }
    public function membershipRegistrations(): HasMany
    {
        return $this->hasMany(MembershipRegistration::class, 'card_type', 'id');
    }

 public function mbPaymentBill()
{
    return $this->hasMany(PaymentBillMaster::class, 'service_id', 'id')
        ->where('type', 'MB');
}

}
