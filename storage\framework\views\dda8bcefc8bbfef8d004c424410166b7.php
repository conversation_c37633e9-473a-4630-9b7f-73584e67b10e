<?php $__env->startSection('title'); ?>
    <?php echo e(config('users.title', 'User')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title"><?php echo e($id ? 'Update' : 'Add'); ?> User</h4>
                </div>
            </div>
            <div class="card-body placeholder-glow" id="data-add-edit">
                <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4,4,4,4,4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(route('user.index')); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('users.url') . 'edit/'.$id : config('users.url') . 'create'); ?>";
            setListUrl(url); // api url for show table
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit","#submitForm",function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
            $(document).on("click","#next",function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $('#submitForm').closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
        
        function resChildHtml(data) {
            let prefix = data.res_data.prefix;
            console.log(prefix);
            $('#step-1').hide();
            $('#step-2').show();
            $('.all-role').hide();
            $('#'+prefix).show();
            $('#step').val(2);
        }
        function doctorTypePrice(id) {
            let price = $('#doctor_doctor_type option[value="' + id + '"]').attr('data-price');
            if (id == 2) {
                $('#doctor_visit_price').prop('readonly', false);
            }
            else {
                $('#doctor_visit_price').prop('readonly', true);
            }
            $('#doctor_visit_price').val(price);
            console.log(id,price);
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Users\resources/views/user/add.blade.php ENDPATH**/ ?>