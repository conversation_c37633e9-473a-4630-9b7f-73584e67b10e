<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Diagnostic\Models\SampleCollection;

class PaymentBillMaster extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'payment_bill_master';
    protected $fillable = [
        'id',
        'bill_show_id',
        'type',
        'service_id',
        'patient_id',
        'bill_amount',
        'discount',
        'total_amount',
        'paid_amount',
        'due_amount',
        'membership_registration_no',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'bill_id', 'id');
    }
    public function serviceCategorys(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class, 'type', 'id');
    }
    public function membershipRegistrations(): BelongsTo
    {
        return $this->belongsTo(MembershipRegistration::class, 'service_id', 'id');
    }
    public function sampleCollection(): BelongsTo
    {
        return $this->belongsTo(SampleCollection::class, 'service_id', 'id');
    }
    public function sampleCollectionPatientClinic()
    {
        return $this->sampleCollection()->with('patient:id,name', 'clinic:id,clinic_name','diagnosticBillHC:sample_collection_id,net_amount');
    }
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
