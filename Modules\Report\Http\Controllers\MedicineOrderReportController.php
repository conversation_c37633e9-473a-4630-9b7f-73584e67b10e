<?php

namespace Modules\Report\Http\Controllers;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\MedicineOrderReportService;
use Modules\Report\Services\PharmacyChildService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use App\Exports\MedicineOrderReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;
class MedicineOrderReportController extends Controller
{
    private $medicineOrderReportService;
    private $pharmacyChildService;
    public function __construct(MedicineOrderReportService $medicineOrderReportService, PharmacyChildService $pharmacyChildService)
    {
        $this->medicineOrderReportService = $medicineOrderReportService;
        $this->pharmacyChildService = $pharmacyChildService;
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->medicineOrderReportService->allClinics()->toArray(),
            'data_source_order_list' => config('report.data_source_order_list'),

        ];
        //  dd($data);
        return view('report::medicineorder.index', compact('data'));
    }

    public function medicineOrder(Request $request)
    {
        try {
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ],
                'clinics' => [
                    'reletion' => [
                        'prefix' => 'clinic_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $request['with'] = [
                'orderChildMedicines' => 'id,order_id,medicine_id,quantity',
                'orderChildMedicines.medicine' => 'id,name',
                'createdBy' => 'id,username',
            ];

            $filter = $request['filter'];
            array_push($this->medicineOrderReportService->columns, 'patients.name as patient_name', 'clinics.clinic_name as clinic_name');
            $this->medicineOrderReportService->setRequest($request);
            $this->medicineOrderReportService->findAll();
            $this->response['success']  = true;
            $data = $this->medicineOrderReportService->getRows();
            $status_list = Arr::except(config('report.status_list'), []);
            $this->response['tbody'] = view('report::medicineorder.api.list', compact('data', 'status_list'))->render();
            $this->response['tfoot'] = $this->medicineOrderReportService->paginationCustom();

            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function medicineOrderExport(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.medicineorder.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function medicineOrderExportLink()
    {
      
        $request = request('req');
        $request['join'] = [
            'patients' => [
                'reletion' => [
                    'prefix' => 'patient_id',
                    'suffix' => 'id'
                ]
            ],
            'clinics' => [
                'reletion' => [
                    'prefix' => 'clinic_id',
                    'suffix' => 'id'
                ]
            ]
        ];
        $request['with'] = [
            'orderChildMedicines' => 'id,order_id,medicine_id,quantity',
            'orderChildMedicines.medicine' => 'id,name',
            'createdBy' => 'id,username',


        ];

         array_push($this->medicineOrderReportService->columns, 'patients.name as patient_name', 'clinics.clinic_name as clinic_name');
            $this->medicineOrderReportService->setRequest($request);
            $this->medicineOrderReportService->findAll();
            $this->response['success']  = true;
            $data = $this->medicineOrderReportService->getRows();
            $status_list = Arr::except(config('report.status_list'), []);

        $data = Excel::download(new MedicineOrderReportExport($data, $status_list), 'medicine-order.xlsx');
        return $data;
    }
}
