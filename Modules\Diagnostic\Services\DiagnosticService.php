<?php

namespace Modules\Diagnostic\Services;

use App\Services\ApplicationDefaultService;
use Modules\Diagnostic\Models\SampleCollection;
use Modules\Diagnostic\Models\Clinic;
use Modules\Diagnostic\Models\Doctor;
use Modules\Diagnostic\Models\Patient;
use Modules\Diagnostic\Models\MembershipRegistration;
use Modules\Diagnostic\Models\Membership;
use Modules\Diagnostic\Models\Test;
use Modules\Diagnostic\Models\LabTimingMaster;
use Modules\Diagnostic\Models\SampleHomecollectionCharge;
use Modules\Diagnostic\Models\CampaignMaster;
use Modules\Diagnostic\Models\CampaignTestDetail;
use DB;
use Carbon\Carbon;

class DiagnosticService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;
    public $entityDoctor;
    public $entityPatient;
    public $entityTest;
    public $entityLabTimingMaster;
    public $entitySampleHomecollectionCharge;
    private $offerNotAvil;

    public $columns = [
        'sample_collections.id',
        'sample_collections.patient_id',
        'sample_collections.patient_phone',
        'sample_collections.test_id',
        'sample_collections.date_of_collection',
        'sample_collections.type_of_collection',
        'sample_collections.appointment_type',
        'sample_collections.clinic_id',
        'sample_collections.clinic_assign',
        'sample_collections.building_no',
        'sample_collections.full_address',
        'sample_collections.landmark',
        'sample_collections.city',
        'sample_collections.pincode',
        'sample_collections.phlebo_assign_status',
        'sample_collections.prev_assignment_history',
        'sample_collections.phlebo_id',
        'sample_collections.prescription_upload',
        'sample_collections.unique_bill_id',
        'sample_collections.unique_queue_number',
        'sample_collections.data_source',
        'sample_collections.source',
        'sample_collections.coupon_code',
        'sample_collections.coupon_status',
        'sample_collections.date_of_extends',
        'sample_collections.unique_id',
        'sample_collections.doctortype',
        'sample_collections.doctor_id',
        'sample_collections.doctor_name',
        'sample_collections.remarks',
        'sample_collections.latitude',
        'sample_collections.longitude',
        'sample_collections.offered_type',
        'sample_collections.offered_id',
        'sample_collections.is_homecollection',
        'sample_collections.hc_quantity_arearange',
        'sample_collections.patient_esign',
        'sample_collections.fully_paid',
        'sample_collections.status',
        'sample_collections.created_by',
        'sample_collections.modified_by',
        'sample_collections.deleted_by',
        'sample_collections.created_at',
        'sample_collections.updated_at',
        'sample_collections.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'patient_phone',
        'test_id',
        'date_of_collection',
        'type_of_collection',
        'appointment_type',
        'clinic_id',
        'clinic_assign',
        'building_no',
        'full_address',
        'landmark',
        'city',
        'pincode',
        'phlebo_assign_status',
        'prev_assignment_history',
        'phlebo_id',
        'prescription_upload',
        'unique_bill_id',
        'unique_queue_number',
        'data_source',
        'source',
        'coupon_code',
        'coupon_status',
        'date_of_extends',
        'unique_id',
        'doctortype',
        'doctor_id',
        'doctor_name',
        'remarks',
        'latitude',
        'longitude',
        'offered_type',
        'offered_id',
        'is_homecollection',
        'hc_quantity_arearange',
        'patient_esign',
        'fully_paid',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(SampleCollection $entity,Clinic $entityClinic, Doctor $entityDoctor, Test $entityTest, Patient $entityPatient, LabTimingMaster $entityLabTimingMaster, SampleHomecollectionCharge $entitySampleHomecollectionCharge) {
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
        $this->entityDoctor =$entityDoctor;
        $this->entityTest =$entityTest;
        $this->entityPatient =$entityPatient;
        $this->entityLabTimingMaster = $entityLabTimingMaster;
        $this->entitySampleHomecollectionCharge = $entitySampleHomecollectionCharge;
    }
    public function allSampleCollections(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function allDoctors(){
        $this->entityDoctor = $this->entityDoctor->select('id','user_id','doctor_type')
            ->where('status',1)
            ->with('user:id,username')
            ->get();
        return $this->entityDoctor;
    }
    public function allTests(){
        $this->entityTest = $this->entityTest->select('*')
            ->where('status',1)
            ->get();
        return $this->entityTest;
    }
    public function allSampleHomecollectionCharges(){
        $this->entitySampleHomecollectionCharge = $this->entitySampleHomecollectionCharge->select('id','area_range','max_distance','charges')
            ->where('status',1)
            ->get();
        return $this->entitySampleHomecollectionCharge;
    }
    public function allSampleHomecollectionChargesForCC(){
        $this->entitySampleHomecollectionCharge = $this->entitySampleHomecollectionCharge->select('id','area_range','max_distance','charges')
            ->where('status',3)
            ->get();
        return $this->entitySampleHomecollectionCharge;
    }
    public function getSampleHomecollection($id){
        $this->entitySampleHomecollectionCharge = $this->entitySampleHomecollectionCharge->find($id);
        return $this->entitySampleHomecollectionCharge;
    }
    public function getPatientID($phone){
        $patient_id = Patient::where('phone',$phone)->value('id');
        return $patient_id;
    }
    public function getFamilys($phone){
        $family_id = $this->entityPatient->where('phone',$phone)->value('family_id');
        $this->entityPatient = $this->entityPatient->select('id','family_id','name','sex')
            ->where('family_id',$family_id)
            ->with('membershipRegistrations')
            ->get();
        return $this->entityPatient;
    }
    public function findPatient($patient_id){
        $this->entityPatient = $this->entityPatient->find($patient_id);
        return $this->entityPatient;
    }
    public function activeMembership($patient_id){
        $data = MembershipRegistration::where('patient_id',$patient_id)->select('card_type','end_date','category_id','clinic_id','registration_no');
        $data = $data->where('status',3);
        $data = $data->where('category_id',1);
        $data = $data->with('memberships:id,name');
        $data = $data->with('clinics:id,clinic_name');
        $data = $data->get()->toArray();
        return $data;
    }
    public function checkLabTiming($delivery_date){
        $delivery_str = strtotime($delivery_date);
        $this->entityLabTimingMaster = $this->entityLabTimingMaster->find(1);
        $openning_time = date('H', strtotime($this->entityLabTimingMaster->openning_time));
        $closing_time = date('H', strtotime($this->entityLabTimingMaster->closing_time));
        $delivery_time = date('H', $delivery_str);
        if($openning_time >= $delivery_time && $closing_time <= $delivery_time){
            $delivery_str = strtotime('+1 day', $delivery_str);
        }
        if (strtolower($this->entityLabTimingMaster->holiday) == strtolower(date('l', $delivery_str))) {
            $delivery_str = strtotime('+1 day', $delivery_str);
        }        
        return date('Y-m-d', $delivery_str);
    }
    public function offerNotAvil(){
        $depID = ['ULTRASOUND','X-RAY','CT SCAN','MRI','DENTAL','CARDIOLOGY'];
        $this->offerNotAvil = DB::table('tests')->whereIn('department_id', $depID)->pluck('id')->toArray();
        array_push($this->offerNotAvil, 572);
        return $this->offerNotAvil;
    }
    public function membershipOffer($patient,$offer_list,$offered_id = null){
        $ab_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 1,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'AROGYA BANDHU',//'Arogya Bandhu',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 10,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        $abp_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 2,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'AROGYA BANDHU PLUS',//'Arogya Bandhu Plus',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 20,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        $abd_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 7,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'Aarogya Bandhu Diabetes Savings',//'Aarogya Bandhu Diabetes Savings',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 20,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        $abh_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 8,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'Arogya Bandhu Hypertension Savings',//'Arogya Bandhu Hypertension Savings',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 20,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        $abg_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 9,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'Arogya Bandhu General Savings',//'Arogya Bandhu General Savings',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 20,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        $abdh_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => $offered_id ? $offered_id : 10,
            'offered_type' => 1,//1:membership,2:campaign,3:employee
            'title' => 'Arogya Bandhu Diebetes & Hypertension Savings',//'Arogya Bandhu Diebetes & Hypertension Savings',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 20,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        // dd($patient->membershipRegistrations);
        if (count($patient->membershipRegistrations) > 0) {
            if($patient->membershipRegistrations->where('card_type',1)->where('status',3)->count() > 0){
                $ab_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',1)->where('status',3)->first();
                // new
                if (strtotime($membership->start_date) > strtotime('2024-07-03')){
                    $ab_offer['title'] = 'AROGYA BANDHU(NEW)';
                    $ab_offer['rate'] = $membership->memberships->percentage_discount_offered;
                    $clinic_list = [];
                    array_push($clinic_list,$membership->clinic_id);
                    $ab_offer['clinic_list'] = $clinic_list;
                }
                else {
                    $ab_offer['title'] = 'AROGYA BANDHU(OLD)';
                    $clinic_list = [];
                    array_push($clinic_list,$membership->clinic_id);
                    $ab_offer['clinic_list'] = $clinic_list;
                }
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $ab_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
            if($patient->membershipRegistrations->where('card_type',2)->where('status',3)->count() > 0){
                $abp_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',2)->where('status',3)->first();
                // new
                if (strtotime($membership->start_date) > strtotime('2024-07-03')){
                    $abp_offer['title'] = 'AROGYA BANDHU PLUS(NEW)';
                    $abp_offer['rate'] = $membership->memberships->percentage_discount_offered;
                }
                else {
                    $abp_offer['title'] = 'AROGYA BANDHU PLUS(OLD)';
                }
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $abp_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
            if($patient->membershipRegistrations->where('card_type',7)->where('status',3)->count() > 0){
                $abd_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',7)->where('status',3)->first();
                $abd_offer['title'] = $membership->memberships->name;
                $abd_offer['rate'] = $membership->memberships->percentage_discount_offered;
                $complementory_diagnostic = explode(',',$membership->memberships->complementory_diagnostic);
                $no_of_occurrence = explode(',',$membership->memberships->no_of_occurrence);
                $test_list = [];
                foreach ($complementory_diagnostic as $key => $row) {
                    $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                    ->where('item_id', $row)
                    ->where('membership_no', $membership->registration_no)
                    ->where('patient_id', $patient->id)
                    ->count();
                    if ($check_occurrence < $no_of_occurrence[$key]) {
                        array_push($test_list,$row);
                    }
                }
                // dd($test_list);
                $abd_offer['test_list'] = $test_list;
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $abd_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
            if($patient->membershipRegistrations->where('card_type',8)->where('status',3)->count() > 0){
                $abh_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',8)->where('status',3)->first();
                $abh_offer['title'] = $membership->memberships->name;
                $abh_offer['rate'] = $membership->memberships->percentage_discount_offered;
                $complementory_diagnostic = explode(',',$membership->memberships->complementory_diagnostic);
                $no_of_occurrence = explode(',',$membership->memberships->no_of_occurrence);
                $test_list = [];
                foreach ($complementory_diagnostic as $key => $row) {
                    $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                    ->where('item_id', $row)
                    ->where('membership_no', $membership->registration_no)
                    ->where('patient_id', $patient->id)
                    ->count();
                    if ($check_occurrence < $no_of_occurrence[$key]) {
                        array_push($test_list,$row);
                    }
                }
                // dd($test_list);
                $abh_offer['test_list'] = $test_list;
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $abh_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
            if($patient->membershipRegistrations->where('card_type',9)->where('status',3)->count() > 0){
                $abg_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',9)->where('status',3)->first();
                $abg_offer['title'] = $membership->memberships->name;
                $abg_offer['rate'] = $membership->memberships->percentage_discount_offered;
                $complementory_diagnostic = explode(',',$membership->memberships->complementory_diagnostic);
                $no_of_occurrence = explode(',',$membership->memberships->no_of_occurrence);
                $test_list = [];
                foreach ($complementory_diagnostic as $key => $row) {
                    $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                    ->where('item_id', $row)
                    ->where('membership_no', $membership->registration_no)
                    ->where('patient_id', $patient->id)
                    ->count();
                    if ($check_occurrence < $no_of_occurrence[$key]) {
                        array_push($test_list,$row);
                    }
                }
                // dd($test_list);
                $abg_offer['test_list'] = $test_list;
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $abg_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
            if($patient->membershipRegistrations->where('card_type',10)->where('status',3)->count() > 0){
                $abdh_offer['status'] = 0;
                $membership = $patient->membershipRegistrations->where('card_type',10)->where('status',3)->first();
                $abdh_offer['title'] = $membership->memberships->name;
                $abdh_offer['rate'] = $membership->memberships->percentage_discount_offered;
                $complementory_diagnostic = explode(',',$membership->memberships->complementory_diagnostic);
                $no_of_occurrence = explode(',',$membership->memberships->no_of_occurrence);
                $test_list = [];
                foreach ($complementory_diagnostic as $key => $row) {
                    $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                    ->where('item_id', $row)
                    ->where('membership_no', $membership->registration_no)
                    ->where('patient_id', $patient->id)
                    ->count();
                    if ($check_occurrence < $no_of_occurrence[$key]) {
                        array_push($test_list,$row);
                    }
                }
                // dd($test_list);
                $abdh_offer['test_list'] = $test_list;
                if($membership->memberships->home_collection_distance_offer && $membership->homecollection_no_of_occurrence < $membership->memberships->home_collection_free_distance_occerence){
                    $abdh_offer['hc_list'] = [
                        'id' => $membership->memberships->home_collection_distance_offer,
                        'discount' => DB::table('sample_homecollection_charges')->where('id',$membership->memberships->home_collection_distance_offer)->value('charges'),
                    ];
                }
            }
        }
        if ($offered_id == 1) {
            array_push($offer_list, $ab_offer);
        }
        elseif ($offered_id == 2) {
            array_push($offer_list, $abp_offer);
        }
        elseif ($offered_id == 7) {
            array_push($offer_list, $abd_offer);
        }
        elseif ($offered_id == 8) {
            array_push($offer_list, $abh_offer);
        }
        elseif ($offered_id == 9) {
            array_push($offer_list, $abg_offer);
        }
        elseif ($offered_id == 10) {
            array_push($offer_list, $abdh_offer);
        }
        else {
            array_push($offer_list, $ab_offer);
            array_push($offer_list, $abp_offer);
            array_push($offer_list, $abd_offer);
            array_push($offer_list, $abh_offer);
            array_push($offer_list, $abg_offer);
            array_push($offer_list, $abdh_offer);
        }
        return $offer_list;
    }
    public function employeeOffer($patient,$offer_list,$offered_id = null){
        $emp_offer = [
            'status' => 1,// 0 = active, 1 = inactive
            'offered_id' => ($offered_id ? $offered_id : 4),
            'offered_type' => 3,//1:membership,2:campaign,3:employee
            'title' => 'Employee Discount',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => 40,
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        if ($patient->is_employee == 2) {
            // Membership
            $emp_offer['status'] = 0;
            if($patient->membershipRegistrations->where('card_type',1)->count() > 0){
                $emp_offer['status'] = 0;
                $emp_offer['rate'] = Membership::where('id',4)->value('percentage_discount_offered');
            }
            
        }
        array_push($offer_list, $emp_offer);
        return $offer_list;
    }
    public function disputeOffer($patient,$offer_list,$offered_id = null){
        $emp_offer = [
            'status' => 0,// 0 = active, 1 = inactive
            'offered_id' => ($offered_id ? $offered_id : 11),
            'offered_type' => 4,//1:membership,2:campaign,3:employee,4:dispute
            'title' => 'Dispute Discount',
            'description' => 'On all type of diagnostic',
            'rate_type' => 0,// 0 = percentage, 1 = fixed
            'rate' => Membership::where('id',11)->value('percentage_discount_offered'),
            'test_list' => [],
            'hc_list' => '',
            'clinic_list' => []
        ];
        array_push($offer_list, $emp_offer);
        return $offer_list;
    }
    public function campaignOffer($patient,$offer_list,$offered_id = null){
        // dd($offered_id);
        $campaignOffer = CampaignMaster::where('campaign_type', 1)
            ->where('status', 1)
            ->whereRaw('CURDATE() BETWEEN start_date AND end_date');
        if ($offered_id) {
            $campaignOffer = $campaignOffer->where('id', $offered_id);
        }
        $campaignOffer = $campaignOffer->get();
        if (count($campaignOffer) > 0) {
            foreach ($campaignOffer as $key => $row) {
                $cam_offer = [
                    'status' => 0,// 0 = active, 1 = inactive
                    'offered_id' => $row->id,
                    'offered_type' => 2,//1:membership,2:campaign,3:employee
                    'title' => $row->campaign_name,
                    'description' => 'On all type of diagnostic',
                    'rate_type' => 0,// 0 = percentage, 1 = fixed
                    'rate' => $row->campaignTestDetail->max('discount'),
                    'test_list' => [],
                    'hc_list' => '',
                    'clinic_list' => json_decode($row->clinic)
                ];
                // dd($row->campaignTestDetail->max('discount'));
                $test_list = [];
                foreach ($row->campaignTestDetail as $keyTest => $rowTest) {
                    $test = [
                        'test_type' => $rowTest->test_type,//1=>test,2=>package
                        'test_ids' => json_decode($rowTest->test_ids),
                        'rate_type' => $rowTest->discount_type,//"Percentage"=>1, "Fixed"=>2
                        'rate' => $rowTest->discount,
                        'discount_for' => $rowTest->discount_for//For Diagnostic Individual - "Default"=>0, "all"=>1, "individual"=>2
                    ];
                    array_push($test_list,$test);
                }
                $cam_offer['test_list'] = $test_list;
                // $cam_offer['hc_list'] = $row->is_hc_free == 1 ? 'paid' : 'free';
                $cam_offer['hc_list'] = [
                    'id' => 0,
                    'discount' =>  $row->is_hc_free == 1 ? 'paid' : 'free',
                ];
                array_push($offer_list, $cam_offer);
            }
        }
        // dd($offer_list);
        return $offer_list;
    }
    public function queueIncrementId($clinic_id)
    {
        $max_queue_number = DB::table('sample_collections')->where(['clinic_id' => $clinic_id])->max('unique_queue_number');
        if ($max_queue_number) {
            $max_queue_number += 1;
        }
        else {
            $max_queue_number = 1;
        }
        return $max_queue_number;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            SampleCollection::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
            DB::table('temp_increment_migrations')
                ->where('table_name', 'sample_collections')
                ->increment('count');
        });
    }
    public function getPhleboWithAssignCount($pincode,$date_of_collection){
        $phlebo_list = DB::table('users as u')
        ->join('phlebotomists as phlebo', 'u.id', '=', 'phlebo.user_id')
        ->select([
            'u.id',
            'u.username',
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '07:00' 
                        AND pa.schedule_time <= '08:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_7_9"),
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '09:00' 
                        AND pa.schedule_time <= '10:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_9_11"),
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '11:00' 
                        AND pa.schedule_time <= '12:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_11_13"),
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '01:00' 
                        AND pa.schedule_time <= '02:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_13_15"),
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '03:00' 
                        AND pa.schedule_time <= '04:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_15_17"),
            DB::raw("(SELECT COUNT(pa.id) 
                    FROM phlebo_assignments pa 
                    INNER JOIN sample_collections sc ON sc.id = pa.sample_id 
                    WHERE pa.phlebo_id = u.id 
                        AND pa.role_id = 7 
                        AND pa.schedule_time >= '05:00' 
                        AND pa.schedule_time <= '06:59' 
                        AND sc.date_of_collection = '$date_of_collection') as count_17_19")
        ])
        ->where('u.status', 1)
        ->whereRaw("FIND_IN_SET(?, phlebo.working_city_pincode)", [$pincode])
        ->get();
        return $phlebo_list;
    }
}
