@foreach ($data['rows'] as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ date('Y-m-d H:i:s', strtotime($row['created_at'])) }}</td>
        <td>{{ $row['patient_name'] ?? '' }}</td>
        <td>{{ $row['patient_phone'] ?? '' }}</td>
        <td>
            <table class="table table-sm m-0">
                @if (count($row['order_child_medicines']) > 0)
                    @foreach ($row['order_child_medicines'] as $row2)
                        <tr>
                            <td class="word-wrap wht-space-custom w-75">{{ $row2['medicine']['name'] }}</td>
                            <td class="w-25 text-center">{{ $row2['quantity'] }}</td>
                        </tr>
                    @endforeach
                @endif
            </table>
        </td>
       
        <td>{{ $row['total_amount'] ?? '' }}</td>
        <td>{{ $row['type_of_collection'] ?? '' }}</td>
        <td>{{ $row['full_address'] }} {{ $row['landmark'] }} {{ $row['city'] }} {{ $row['pincode'] }}</td>
        <td>{{ $row['clinic_name'] ?? '' }}</td>
        <td>{{ $row['created_by']['username'] ?? '' }}</td>
        <td> {{ config('report.data_source_order_list')[$row['data_source']] }}</td>
        <td>{{ '' }}</td>
        <td> {{ $row['status'] == 6 ? 'Rejected' : $status_list[$row['status']] }}
            @if ($row['status'] == 6 && $row['reject_remarks'])
                <br>
                {{ $row['reject_remarks'] }}
            @endif
        </td>
    </tr>
@endforeach
