<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\OrderMedicine;
use Modules\Report\Models\Clinic;
use Modules\Report\Models\Patient;
use Modules\Report\Models\MembershipRegistration;
use Modules\Report\Models\Medicine;
use DB;
use Carbon\Carbon;

class MedicineOrderReportService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;
    public $entityPatient;
    public $entityMedicine;

    public $columns = [
        'order_medicines.id',
        'order_medicines.patient_id',
        'order_medicines.patient_phone',
        'order_medicines.type_of_collection',
        'order_medicines.clinic_id',
        'order_medicines.building_no',
        'order_medicines.full_address',
        'order_medicines.landmark',
        'order_medicines.city',
        'order_medicines.pincode',
        'order_medicines.prescription_upload',
        'order_medicines.data_source',
        'order_medicines.source',
        'order_medicines.remarks',
        'order_medicines.latitude',
        'order_medicines.longitude',
        'order_medicines.total_amount',
        'order_medicines.reject_remarks',
        'order_medicines.is_accept',
        'order_medicines.status',
        'order_medicines.created_by',
        'order_medicines.modified_by',
        'order_medicines.deleted_by',
        'order_medicines.created_at',
        'order_medicines.updated_at',
        'order_medicines.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'patient_phone',
        'type_of_collection',
        'clinic_id',
        'building_no',
        'full_address',
        'landmark',
        'city',
        'pincode',
        'prescription_upload',
        'data_source',
        'source',
        'remarks',
        'latitude',
        'longitude',
        'total_amount',
        'reject_remarks',
        'is_accept',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(OrderMedicine $entity,Clinic $entityClinic,Patient $entityPatient,Medicine $entityMedicine) {
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
        $this->entityPatient =$entityPatient;
        $this->entityMedicine =$entityMedicine;
    }
    public function allOrderMedicines(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function allMedicines(){
        $this->entityMedicine = $this->entityMedicine->select('id','name','price')
            ->where('status',1)
            ->get();
        return $this->entityMedicine;
    }
    public function getPatientID($phone){
        $patient_id = Patient::where('phone',$phone)->value('id');
        return $patient_id;
    }
    public function getFamilys($phone){
        $family_id = $this->entityPatient->where('phone',$phone)->value('family_id');
        $this->entityPatient = $this->entityPatient->select('id','family_id','name','sex')
            ->where('family_id',$family_id)
            ->with('membershipRegistrations')
            ->get();
        return $this->entityPatient;
    }
    public function findPatient($patient_id){
        $this->entityPatient = $this->entityPatient->find($patient_id);
        return $this->entityPatient;
    }
    public function activeMembership($patient_id){
        $data = MembershipRegistration::where('patient_id',$patient_id)->select('card_type','end_date','category_id','clinic_id','registration_no');
        $data = $data->where('status',3);
        $data = $data->where('category_id',1);
        $data = $data->with('memberships:id,name');
        $data = $data->with('clinics:id,clinic_name');
        $data = $data->get()->toArray();
        return $data;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            OrderMedicine::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
            DB::table('temp_increment_migrations')
                ->where('table_name', 'order_medicines')
                ->increment('count');
        });
    }
}
