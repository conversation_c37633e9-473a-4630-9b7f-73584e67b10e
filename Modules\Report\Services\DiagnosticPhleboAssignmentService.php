<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\PhleboAssignment;
use DB;
use Carbon\Carbon;

class DiagnosticPhleboAssignmentService extends ApplicationDefaultService
{
    public $entity;

    public $columns = [
        'id',
        'sample_id',
        'role_id',
        'phlebo_id',
        'schedule_time',
        'remarks',
        'test_count',
        'collected_testcount',
        'handover_testcount',
        'actual_date_time_of_collection',
        'temp_of_bag_at_collection',
        'payment_status',
        'amount',
        'mode_of_payment',
        'collection_status',
        'date_time_at_handover',
        'temp_of_bag_at_handover',
        'amount_at_handover',
        'handover_status',
        'collected_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'sample_id',
        'role_id',
        'phlebo_id',
        'schedule_time',
        'remarks',
        'test_count',
        'collected_testcount',
        'handover_testcount',
        'actual_date_time_of_collection',
        'temp_of_bag_at_collection',
        'payment_status',
        'amount',
        'mode_of_payment',
        'collection_status',
        'date_time_at_handover',
        'temp_of_bag_at_handover',
        'amount_at_handover',
        'handover_status',
        'collected_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(PhleboAssignment $entity) {
        $this->entity =$entity;
    }
    public function allPhleboAssignments(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            PhleboAssignment::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
            DB::table('temp_increment_migrations')
                ->where('table_name', 'phlebo_assignments')
                ->increment('count');
        });
    }
}
