<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
        <td><?php echo e($row['patient_name'] ?? ''); ?></td>
        <td><?php echo e($row['patient_phone'] ?? ''); ?></td>
        <td>
            <table class="table table-sm m-0">
                <?php if(count($row['order_child_medicines']) > 0): ?>
                    <?php $__currentLoopData = $row['order_child_medicines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="word-wrap wht-space-custom w-75"><?php echo e($row2['medicine']['name']); ?></td>
                            <td class="w-25 text-center"><?php echo e($row2['quantity']); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </table>
        </td>
       
        <td><?php echo e($row['total_amount'] ?? ''); ?></td>


        <td><?php echo e($row['type_of_collection'] ?? ''); ?></td>


        <td><?php echo e($row['full_address']); ?> <?php echo e($row['landmark']); ?> <?php echo e($row['city']); ?> <?php echo e($row['pincode']); ?></td>
        <td><?php echo e($row['clinic_name'] ?? ''); ?></td>
        <td><?php echo e($row['created_by']['username'] ?? ''); ?></td>

        <td> <?php echo e(config('report.data_source_order_list')[$row['data_source']]); ?></td>
        <td><?php echo e(''); ?></td>
        <td> <?php echo e($row['status'] == 6 ? 'Rejected' : $status_list[$row['status']]); ?>

            <?php if($row['status'] == 6 && $row['reject_remarks']): ?>
                <br>
                <?php echo e($row['reject_remarks']); ?>

            <?php endif; ?>
        </td>

    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/medicineorder/api/list.blade.php ENDPATH**/ ?>