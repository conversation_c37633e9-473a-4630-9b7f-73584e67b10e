<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['survey_title']); ?></td>
        <td><?php echo e($row['language']); ?></td>
        <td>
            <?php echo e($row['url_slag']); ?>

        </td>
        <td>
            <?php if($row['short_link']): ?>
                <a href="https://<?php echo e($row['short_link']); ?>" target="_blank">
                    <?php echo e($row['short_link']); ?>

                </a>
            <?php endif; ?>
        </td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
        <td><?php echo e($row['status'] == 1 ? 'Active' : 'Inactive'); ?></td>
        <td>
            <div class="d-flex flex-nowrap gap-2">
                <?php if(array_intersect(['edit_review_survey'], $permissionPage)): ?>
                    <a class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" title="Short Link" data-bs-toggle="modal" data-bs-target="#exampleInfoModal"
                        onclick="shortLink('<?php echo e($row['id']); ?>','<?php echo e($row['short_link']); ?>')">
                        <svg fill="none" height="20" viewBox="0 0 20 20" width="20"
                            xmlns="http://www.w3.org/2000/svg" id="fi_7640062">
                            <path clip-rule="evenodd"
                                d="m12.5858 4.58579c.781-.78105 2.0473-.78105 2.8284 0 .781.78104.781 2.04737 0 2.82842l-3 2.99999c-.7811.7811-2.0474.7811-2.82843 0-.39053-.3905-1.02369-.3905-1.41421 0-.39053.3905-.39053 1.0237 0 1.4142 1.56209 1.5621 4.09474 1.5621 5.65684 0l3-2.99997c1.5621-1.5621 1.5621-4.09476 0-5.65686-1.5621-1.56209-4.0947-1.56209-5.6568 0l-1.50004 1.5c-.39053.39053-.39053 1.02369 0 1.41422.39054.39052 1.02364.39052 1.41424 0zm-5.00001 5c.78104-.78105 2.04737-.78105 2.82841 0 .3905.39052 1.0237.39052 1.4142 0 .3906-.39053.3906-1.02369 0-1.41422-1.5621-1.56209-4.09473-1.56209-5.65683 0l-3 3.00003c-1.56209 1.5621-1.56209 4.0947 0 5.6568 1.5621 1.5621 4.09476 1.5621 5.65686 0l1.49997-1.5c.3906-.3905.3906-1.0237 0-1.4142-.3905-.3905-1.02366-.3905-1.41419 0l-1.5 1.5c-.78105.7811-2.04737.7811-2.82842 0-.78105-.781-.78105-2.0474 0-2.8284z"
                                fill="rgb(0,0,0)" fill-rule="evenodd"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['edit_review_survey'], $permissionPage)): ?>
                    <a href="<?php echo e(route('reviewSurvey.addForm', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Edit" role="button">
                        <svg width="32" class="icon-16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4"
                                d="M19.9927 18.9534H14.2984C13.7429 18.9534 13.291 19.4124 13.291 19.9767C13.291 20.5422 13.7429 21.0001 14.2984 21.0001H19.9927C20.5483 21.0001 21.0001 20.5422 21.0001 19.9767C21.0001 19.4124 20.5483 18.9534 19.9927 18.9534Z"
                                fill="currentColor"></path>
                            <path
                                d="M10.309 6.90385L15.7049 11.2639C15.835 11.3682 15.8573 11.5596 15.7557 11.6929L9.35874 20.0282C8.95662 20.5431 8.36402 20.8344 7.72908 20.8452L4.23696 20.8882C4.05071 20.8903 3.88775 20.7613 3.84542 20.5764L3.05175 17.1258C2.91419 16.4915 3.05175 15.8358 3.45388 15.3306L9.88256 6.95545C9.98627 6.82108 10.1778 6.79743 10.309 6.90385Z"
                                fill="currentColor"></path>
                            <path opacity="0.4"
                                d="M18.1208 8.66544L17.0806 9.96401C16.9758 10.0962 16.7874 10.1177 16.6573 10.0124C15.3927 8.98901 12.1545 6.36285 11.2561 5.63509C11.1249 5.52759 11.1069 5.33625 11.2127 5.20295L12.2159 3.95706C13.126 2.78534 14.7133 2.67784 15.9938 3.69906L17.4647 4.87078C18.0679 5.34377 18.47 5.96726 18.6076 6.62299C18.7663 7.3443 18.597 8.0527 18.1208 8.66544Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['change_status_review_survey'], $permissionPage)): ?>
                    <?php
                        $status = $row['status'] == 0 ? 1 : 0;
                    ?>
                    <a href="#" title="<?php echo e($row['status'] == 0 ? 'Deactive' : 'Active'); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" data-stat="<?php echo e($status); ?>"
                        onclick="updateStatus('<?php echo e(config('settings.url_review_survey') . 'updateStatus/' . $row['id']); ?>','POST',this)">
                        <svg class=" text-primary" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24"
                            fill="currentColor">
                            <g>
                                <circle cx="12" cy="12" r="8"
                                    fill="<?php echo e($row['status'] == 0 ? '#F10F0F' : '#13B907'); ?>"></circle>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['info_review_survey'], $permissionPage)): ?>
                    <a href="<?php echo e(route('reviewSurvey.viewResponse', ['id' => base64_encode(base64_encode($row['id']))])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="View Responses" role="button">
                        <svg width="32" class="icon-16" height="32" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12 5C5.63636 5 2 12 2 12C2 12 5.63636 19 12 19C18.3636 19 22 12 22 12C22 12 18.3636 5 12 5Z"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                            <path
                                d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            </path>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Settings\resources/views/reviewSurvey/api/list.blade.php ENDPATH**/ ?>