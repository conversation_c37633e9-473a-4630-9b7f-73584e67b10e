@foreach ($data['rows'] as $row)
     <tr>
         <td>{{ $loop->iteration }}</td>
         <td>{{ $row['registration_date'] ?? '' }}</td>
         <td>{{ $row['patients']['name'] ?? '' }}</td>
         <td>{{ $row['phone'] ?? '' }}</td>
         <td>{{ $row['patients']['sex'] ?? '' }}</td>
         <td>{{ Helper::ageCalculator($row['patients']['birthdate'] ?? '') }}</td>
          
         <td>{{ $row['registration_no'] ?? '' }}</td>


         <td>{{ $statusLabels[$row['status']] ?? '' }}</td>
         <td>{{ $row['end_date'] ?? '' }}</td>
         <td>{{ $row['memberships']['name'] ?? '' }}</td>

         <td>{{ $row['payment_bill']['0']['total_amount'] ?? 0 }}</td>
         <td>{{ $row['clinics']['clinic_name'] ?? '' }}</td> 
         <td>{{ $row['data_source'] ?? '' }}</td>
         {{-- <td>{{ $row['start_date'] }} - {{ $row['end_date'] }}</td> --}}
         {{-- <td>{{ $row['current_start_date'] }} - {{ $row['current_end_date'] }}</td> --}}
         <td>{{ $row['created_by']['username'] ?? '-' }}</td>
         <td>{{ ' ' }}</td>
         <td>{{ $row['remarks'] ?? '' }}</td>
     </tr>
@endforeach
