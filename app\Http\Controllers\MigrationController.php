<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Settings\Models\PharmacySalesRegLineDetail;
use Modules\Diagnostic\Models\SampleHomecollectionCharge;
use Modules\Diagnostic\Models\SampleCollection;
use Modules\Diagnostic\Models\SampleCollectionBreakupBill;
use Modules\Diagnostic\Models\SampleCollectionBreakupTestItdose;
use Modules\Users\Services\UserService;
use Modules\Doctor\Services\DoctorService;
use Modules\Users\Services\ClusterUserService;
use Modules\Users\Services\CenterManagerService;
use Modules\Users\Services\ReceptionistService;
use Modules\Users\Services\NurseService;
use Modules\Users\Services\PhlebotomistService;
use Modules\Users\Services\PharmacistService;
use Modules\Agent\Services\AgentService;
use Modules\Users\Services\CustomerCareService;
use Modules\Patient\Services\PatientService;
use Modules\Billing\Services\BillingService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Reward\Services\RewardService;
use Modules\Appointment\Services\AppointmentService;
use Modules\Appointment\Services\VitalsService;
use Modules\Prescription\Services\PrescriptionService;
use Modules\Prescription\Services\PrescriptionChildService;
use Modules\Settings\Services\UploadCsquareService;
use Modules\Pharmacy\Services\PharmacyService;
use Modules\Pharmacy\Services\PharmacyChildService;
use Modules\Diagnostic\Services\DiagnosticService;
use Modules\Diagnostic\Services\DiagnosticBreakupTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupBillService;
use Modules\Diagnostic\Services\DiagnosticPhleboAssignmentService;
use Modules\Diagnostic\Services\DiagnosticApiService;
use Modules\Diagnostic\Services\DiagnosticBarcodeResubmissionService;
use Modules\Diagnostic\Services\EstimationService;
use App\Traits\DBConnection;
use Illuminate\Support\Arr;
use DB;
use Log;

class MigrationController extends Controller
{
    use DBConnection;
    private $queryBuilder;
    private $userService;
    private $doctorService;
    private $clusterUserService;
    private $centerManagerService;
    private $receptionistService;
    private $nurseService;
    private $phlebotomistService;
    private $pharmacistService;
    private $agentService;
    private $customerCareService;
    private $patientService;
    private $billingService;
    private $paymentBillService;
    private $paymentService;
    private $paymentDetailService;
    private $rewardService;
    private $appointmentService;
    private $vitalsService;
    private $prescriptionService;
    private $prescriptionChildService;
    private $uploadCsquareService;
    private $pharmacyService;
    private $pharmacyChildService;
    private $diagnosticService;
    private $diagnosticBreakupTestService;
    private $diagnosticBreakupBillService;
    private $diagnosticPhleboAssignmentService;
    private $diagnosticApiService;
    private $diagnosticBarcodeResubmissionService;
    private $estimationService;

    public function __construct(
        UserService $userService, DoctorService $doctorService, ClusterUserService $clusterUserService, 
        CenterManagerService $centerManagerService, ReceptionistService $receptionistService, 
        NurseService $nurseService, PhlebotomistService $phlebotomistService, PharmacistService $pharmacistService, 
        AgentService $agentService, CustomerCareService $customerCareService, PatientService $patientService,
        BillingService $billingService, PaymentBillService $paymentBillService, PaymentService $paymentService,
        PaymentDetailService $paymentDetailService, RewardService $rewardService, AppointmentService $appointmentService,
        VitalsService $vitalsService, PrescriptionService $prescriptionService, PrescriptionChildService $prescriptionChildService,
        UploadCsquareService $uploadCsquareService, PharmacyService $pharmacyService, PharmacyChildService $pharmacyChildService,
        DiagnosticService $diagnosticService, DiagnosticBreakupTestService $diagnosticBreakupTestService,
        DiagnosticBreakupBillService $diagnosticBreakupBillService, DiagnosticPhleboAssignmentService $diagnosticPhleboAssignmentService,
        DiagnosticApiService $diagnosticApiService, DiagnosticBarcodeResubmissionService $diagnosticBarcodeResubmissionService,
        EstimationService $estimationService
    )
    {
        $this->setMymdDBConnection();
        $this->userService = $userService;
        $this->doctorService = $doctorService;
        $this->clusterUserService = $clusterUserService;
        $this->centerManagerService = $centerManagerService;
        $this->receptionistService = $receptionistService;
        $this->nurseService = $nurseService;
        $this->phlebotomistService = $phlebotomistService;
        $this->pharmacistService = $pharmacistService;    
        $this->agentService = $agentService;
        $this->customerCareService = $customerCareService;
        $this->patientService = $patientService;
        $this->billingService = $billingService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->rewardService = $rewardService;
        $this->appointmentService = $appointmentService;
        $this->vitalsService = $vitalsService;
        $this->prescriptionService = $prescriptionService;
        $this->prescriptionChildService = $prescriptionChildService;
        $this->uploadCsquareService = $uploadCsquareService;
        $this->pharmacyService = $pharmacyService;
        $this->pharmacyChildService = $pharmacyChildService;
        $this->diagnosticService = $diagnosticService;
        $this->diagnosticBreakupTestService = $diagnosticBreakupTestService;
        $this->diagnosticBreakupBillService = $diagnosticBreakupBillService;
        $this->diagnosticPhleboAssignmentService = $diagnosticPhleboAssignmentService;
        $this->diagnosticApiService = $diagnosticApiService;
        $this->diagnosticBarcodeResubmissionService = $diagnosticBarcodeResubmissionService;
        $this->estimationService = $estimationService;
    }
    public function dataMigrateIndex()
    {
        return view('admin.dataMigrate');
    }
    public function dataMigrate()
    {
        $group_ids = explode(",", request('g_id'));// doctor
        // dd($group_ids);
        $all_content_users = $this->dbConnectionInstance->table('users')
            ->join('users_groups', 'users.id', '=', 'users_groups.user_id')
            // ->join('doctor', 'users.id', '=', 'doctor.ion_user_id')
            ->whereIn('users_groups.group_id', $group_ids)
            ->count();
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = $this->dbConnectionInstance->table('users')
                ->join('users_groups', 'users.id', '=', 'users_groups.user_id')
                ->select('users.*','users_groups.group_id as ci_role_id')
                ->whereIn('users_groups.group_id', $group_ids)
                ->orderBy('users.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertDoctor = [];
                $contentInsertClusterUser = [];
                $contentInsertCenterManager = [];
                $contentInsertReceptionist = [];
                $contentInsertNurse = [];
                $contentInsertPhlebotomist = [];
                $contentInsertPharmacist = [];
                $contentInsertAgent = [];
                $contentInsertCare = [];
                $laravel_role_id = 0;                
                foreach ($chunk_content_users as $content) {
                    // user
                    $user_created_at = ($content->created_on != 0 ? date('Y-m-d H:i:s', $content->created_on) : date('Y-m-d H:i:s'));
                    $contentInsert[] = [
                        'id' => $content->id,
                        'ip_address' => $content->ip_address,
                        'username' => $content->username,
                        'email' => $content->email,
                        'phone' => $content->phone,
                        'password' => $content->password,
                        'active' => 1,
                        'last_login' => date('Y-m-d H:i:s', $content->last_login),
                        'status' => ($content->active == 1 ? 1 : 0),
                        'created_by' => 1,
                        'created_at' => $user_created_at,
                    ];
                    
                    // user details
                    switch ($content->ci_role_id) {
                        case '4':// doctor 4,25,28,30
                        case '25':
                        case '28':
                        case '30':
                            $laravel_role_id = 2;
                            $doctor = $this->dbConnectionInstance->table('doctor')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($doctor)) {
                                $name = explode(" ",str_replace("Dr. ", "", $doctor->name));
                                $doctor_type = match ($doctor->doctor_type) {
                                    0 => 1,
                                    1 => 2,
                                    2 => 3,
                                    default => 4,
                                };
                                
                                $contentInsertDoctor[] = [
                                    'id' => $doctor->id,
                                    'user_id' => $content->id,
                                    'itdose_doctorid' => $doctor->itdose_doctorid,
                                    'first_name' => $name[0] ?? '',
                                    'last_name' => implode(' ', array_slice($name, 1)) ?? '',
                                    'img_url' => $doctor->img_url,
                                    'secret_key' => $doctor->secret_key,
                                    'doctor_slug' => $doctor->doctor_slug,
                                    'address' => $doctor->address,
                                    'profile' => $doctor->profile,
                                    'registration_no' => $doctor->registration_no,
                                    'docor_mail_verification_frontend' => $doctor->docor_mail_verification_frontend,
                                    'doctor_visit' => null,
                                    'visit_price' => $doctor->visit_price,
                                    'degree' => $doctor->degree,
                                    'description' => $doctor->description,
                                    'featured_doctor' => $doctor->featured_doctor,
                                    'doctor_type' => $doctor_type,
                                    'speciality' => $doctor->speciality,
                                    'key_procedure_performed' => $doctor->key_procedure_performed,
                                    'status' => $doctor->status,
                                    'doctor_esign' => $doctor->doctor_esign,
                                    'data_source' => $doctor->data_source,
                                    'manage_bill' => $doctor->manage_bill,
                                    'created_by' => 1,
                                    'deleted_by' => ($doctor->is_deleted == 1 ? 1 : null),
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                    'deleted_at' => ($doctor->is_deleted == 1 ? date('Y-m-d H:i:s') : null),
                                ];
                            }
                            
                            break;
                        case '26':// Cluster Manager
                            $laravel_role_id = 3;
                            $cluster_user = $this->dbConnectionInstance->table('cluster_user')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($cluster_user)) {                                
                                $contentInsertClusterUser[] = [
                                    'id' => $cluster_user->id,
                                    'user_id' => $content->id,
                                    'img_url' => $cluster_user->img_url,
                                    'address' => $cluster_user->address,
                                    'clinic_id' => $cluster_user->clinic_id,
                                    'status' => $cluster_user->status,
                                    'created_by' => $cluster_user->created_by,
                                    'modified_by' => ($cluster_user->updated_by != 0 ? $cluster_user->updated_by : null),
                                    'created_at' => $cluster_user->created_at,
                                    'updated_at' => $cluster_user->updated_at,
                                ];
                            }
                            break;
                        case '27':// Center Manager 27,36
                        case '36':
                            $laravel_role_id = 4;
                            $center_manager = $this->dbConnectionInstance->table('center_manager')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($center_manager)) {                                
                                $contentInsertCenterManager[] = [
                                    'id' => $center_manager->id,
                                    'user_id' => $content->id,
                                    'img_url' => $center_manager->img_url,
                                    'clinic_id' => $center_manager->clinic_id,
                                    'status' => $center_manager->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            break;
                        case '10':// Receptionist 10,35
                        case '35':
                            $laravel_role_id = 5;
                            $receptionist = $this->dbConnectionInstance->table('receptionist')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($receptionist)) {                                
                                $contentInsertReceptionist[] = [
                                    'id' => $receptionist->id,
                                    'user_id' => $content->id,
                                    'img_url' => $receptionist->img_url,
                                    'address' => $receptionist->address,
                                    'clinic_id' => $receptionist->clinic_id,
                                    'status' => $receptionist->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            break;
                        case '6':// Nurse 6,37
                        case '37':
                            $laravel_role_id = 6;
                            $nurse = $this->dbConnectionInstance->table('nurse')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($nurse)) {                                
                                $contentInsertNurse[] = [
                                    'id' => $nurse->id,
                                    'user_id' => $content->id,
                                    'img_url' => $nurse->img_url,
                                    'address' => $nurse->address,
                                    'clinic_id' => $nurse->clinic_id,
                                    'status' => $nurse->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            break;
                        case '13':// Phlebotomist 13,38
                        case '38':
                            $laravel_role_id = 7;
                            $phlebotomist = $this->dbConnectionInstance->table('phlebotomist')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($phlebotomist)) {                                
                                $contentInsertPhlebotomist[] = [
                                    'id' => $phlebotomist->id,
                                    'user_id' => $content->id,
                                    'img_url' => $phlebotomist->img_url,
                                    'gender' => $phlebotomist->gender,
                                    'dob' => $phlebotomist->dob,
                                    'city' => $phlebotomist->city,
                                    'pincode' => $phlebotomist->pincode,
                                    'address' => $phlebotomist->address,
                                    'dl_no' => $phlebotomist->dl_no,
                                    'vehicle_no' => $phlebotomist->vehicle_no,
                                    'working_city' => $phlebotomist->working_city,
                                    'working_city_pincode' => $phlebotomist->working_city_pincode,
                                    'phlebotomist_slug' => $phlebotomist->phlebotomist_slug,
                                    'clinic_id' => $phlebotomist->clinic_id,
                                    'status' => $phlebotomist->is_active,
                                    'created_by' => 1,
                                    'deleted_by' => ($phlebotomist->is_deleted != 0 ? 1 : null),
                                    'created_at' => $phlebotomist->date_of_creation,
                                    'updated_at' => $phlebotomist->date_of_creation,
                                    'deleted_at' => ($phlebotomist->is_deleted != 0 ? date('Y-m-d H:i:s') : null),
                                ];
                            }
                            break;
                        case '7':// Pharmacist 7,31,33
                        // case '31':
                        // case '33':
                            $laravel_role_id = 8;
                            $pharmacist = $this->dbConnectionInstance->table('pharmacist')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($pharmacist)) {                                
                                $contentInsertPharmacist[] = [
                                    'id' => $pharmacist->id,
                                    'user_id' => $content->id,
                                    'img_url' => $pharmacist->img_url,
                                    'address' => $pharmacist->address,
                                    'clinic_id' => $pharmacist->clinic_id,
                                    'status' => $pharmacist->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            break;
                        case '22':// Agent 22,23
                        case '23':
                            $laravel_role_id = 9;
                            $agent = $this->dbConnectionInstance->table('agent')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($agent)) {
                                $contentInsertAgent[] = [
                                    'id' => $agent->id,
                                    'user_id' => $content->id,
                                    'address' => $agent->address,
                                    'image' => $agent->image,
                                    'designation' => $agent->designation,
                                    'team' => $agent->team,
                                    'status' => $agent->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            break;
                        case '14':// Customer Care 14,29
                        // case '29':
                            $laravel_role_id = 10;
                            $care = $this->dbConnectionInstance->table('customer_care')
                                ->where('ion_user_id', $content->id)
                                ->first();
                            if (!empty($care)) {                                
                                $contentInsertCare[] = [
                                    'id' => $care->id,
                                    'user_id' => $content->id,
                                    'img_url' => $care->img_url,
                                    'status' => $care->is_active,
                                    'created_by' => 1,
                                    'created_at' => $user_created_at,
                                    'updated_at' => $user_created_at,
                                ];
                            }
                            
                            break;
                        default:
                            $laravel_role_id = 0;
                            break;
                    }
                    // role
                    $contentInsertRole[] = [
                        'role_id' => $laravel_role_id,
                        'model_type' => 'Modules\Users\Models\User',
                        'model_id' => $content->id
                    ];
                }
                // dd($contentInsertRole);
                if (!empty($contentInsert)) {
                    $this->userService->dataMigrate($contentInsert);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        DB::table('model_has_roles')->upsert(
                            $contentInsertRole,
                            ['model_id'], // Unique columns to check for duplicates
                            ['role_id'] // Columns to update if a duplicate is found
                        );
                    });
                }
                if (!empty($contentInsertDoctor)) {
                    $this->doctorService->dataMigrate($contentInsertDoctor);
                }
                if (!empty($contentInsertCare)) {
                    $this->customerCareService->dataMigrate($contentInsertCare);
                }
                if (!empty($contentInsertClusterUser)) {
                    $this->clusterUserService->dataMigrate($contentInsertClusterUser);
                }
                if (!empty($contentInsertCenterManager)) {
                    $this->centerManagerService->dataMigrate($contentInsertCenterManager);
                }
                if (!empty($contentInsertReceptionist)) {
                    $this->receptionistService->dataMigrate($contentInsertReceptionist);
                }
                if (!empty($contentInsertNurse)) {
                    $this->nurseService->dataMigrate($contentInsertNurse);
                }
                if (!empty($contentInsertPhlebotomist)) {
                    $this->phlebotomistService->dataMigrate($contentInsertPhlebotomist);
                }
                if (!empty($contentInsertPharmacist)) {
                    $this->pharmacistService->dataMigrate($contentInsertPharmacist);
                }
                if (!empty($contentInsertAgent)) {
                    $this->agentService->dataMigrate($contentInsertAgent);
                }
                // dd('each loop completed');
            });
            // dd("Loop iteration completed for district table");
        }
        dd("completed migration");
        // return back();
    }
    public function dataMigrateMymdDoctor()
    {
        $all_content_users = $this->dbConnectionInstance->table('doctor')
            ->join('users', 'doctor.ion_user_id', '=', 'users.id')
            ->whereIn('doctor.doctor_type', [1,2,3])
            ->where('doctor.ion_user_id', '!=', '')
            ->count();
            
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = $i * $chunkSize_users;
            $this->queryBuilder = $this->dbConnectionInstance->table('doctor')
                ->select('doctor.*')
                ->join('users', 'doctor.ion_user_id', '=', 'users.id')
                ->whereIn('doctor.doctor_type', [1,2,3])
                ->where('doctor.ion_user_id', '!=', '')
                ->orderBy('doctor.id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertDoctor = [];
                $max_user_id = 0;
                $user_id = 0;
                foreach ($chunk_content_users as $key => $content) {
                    $max_user_id = DB::table('users')->max('id');
                    $user = $this->dbConnectionInstance->table('users')->where('id', $content->ion_user_id)->first();
                    // dd($user);
                    // if(!$user){
                    //     dd($content->ion_user_id);
                    //     $max_user_id++;
                    //     $user_id = $max_user_id;
                    //     $user_created_at = date('Y-m-d H:i:s');
                    //     $contentInsert[] = [
                    //         'id' => $user_id,
                    //         'username' => $content->name,
                    //         'active' => 2,
                    //         'status' => 0,
                    //         'created_by' => 1,
                    //         'created_at' => $user_created_at,
                    //     ];
                    // }
                    if($user){
                        $user_id = $user->id;
                        $user_created_at = ($user->created_on != 0 ? date('Y-m-d H:i:s', $user->created_on) : date('Y-m-d H:i:s'));
                        $contentInsert[] = [
                            'id' => $user->id,
                            'ip_address' => $user->ip_address,
                            'username' => $user->username,
                            'email' => $user->email,
                            'phone' => $user->phone,
                            'password' => $user->password,
                            'active' => 1,
                            'last_login' => date('Y-m-d H:i:s', $user->last_login),
                            'status' => $user->active == 1 ? 1 : 0,
                            'created_by' => 1,
                            'created_at' => $user_created_at,
                        ];
                        $name = explode(" ",str_replace("Dr. ", "", $content->name));
                        $doctor_type = match ($content->doctor_type) {
                            1 => 2,
                            2 => 3,
                            3 => 1,
                            4 => 4,
                            default => null,
                        };
                        
                        $contentInsertDoctor[] = [
                            'id' => $content->id,
                            'user_id' => $user_id,
                            'itdose_doctorid' => $content->itdose_doctorid,
                            'first_name' => $name[0] ?? '',
                            'last_name' => implode(' ', array_slice($name, 1)) ?? '',
                            'img_url' => $content->img_url,
                            'secret_key' => $content->secret_key,
                            'doctor_slug' => $content->doctor_slug,
                            'address' => $content->address,
                            'profile' => $content->profile,
                            'registration_no' => $content->registration_no,
                            'docor_mail_verification_frontend' => $content->docor_mail_verification_frontend,
                            'doctor_visit' => null,
                            'visit_price' => $content->visit_price,
                            'degree' => $content->degree,
                            'description' => $content->description,
                            'featured_doctor' => $content->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $content->speciality,
                            'key_procedure_performed' => $content->key_procedure_performed,
                            'status' => $content->status,
                            'doctor_esign' => $content->doctor_esign,
                            'data_source' => $content->data_source,
                            'manage_bill' => $content->manage_bill,
                            'created_by' => 1,
                            'deleted_by' => ($content->is_deleted == 1 ? 1 : null),
                            'created_at' => $user_created_at,
                            'updated_at' => $user_created_at,
                            'deleted_at' => ($content->is_deleted == 1 ? date('Y-m-d H:i:s') : null)
                        ];
                        // role
                        $contentInsertRole[] = [
                            'role_id' => 2,
                            'model_type' => 'Modules\Users\Models\User',
                            'model_id' => $user_id
                        ];
                    }
                    // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                }
                
                if (!empty($contentInsert)) {
                    $this->userService->dataMigrate($contentInsert);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        DB::table('model_has_roles')->upsert(
                            $contentInsertRole,
                            ['model_id'], // Unique columns to check for duplicates
                            ['role_id'] // Columns to update if a duplicate is found
                        );
                    });
                }
                if (!empty($contentInsertDoctor)) {
                    $this->doctorService->dataMigrate($contentInsertDoctor);
                }
                // dd('each loop completed');
            });
            // dd("Loop iteration completed for district table");
        }
        dd("completed migration");
        // return back();
    }
    public function dataMigrateRefferDoctor()
    {
        $all_content_users = $this->dbConnectionInstance->table('doctor')
            ->where('doctor_type', 4)
            ->count();
            
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        // for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = 0;//$i * $chunkSize_users;
            $this->queryBuilder = $this->dbConnectionInstance->table('doctor')
                ->where('doctor_type', 4)
                ->orderBy('id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertDoctor = [];
                // $max_user_id = 0;
                $user_id = 0;
                foreach ($chunk_content_users as $key => $content) {
                    // $max_user_id = DB::table('users')->max('id');
                    $user = $this->dbConnectionInstance->table('users')->where('id', $content->ion_user_id)->first();
                    // dd($user);
                    if(!$user){
                        // dd($content->ion_user_id);
                        // $max_user_id++;
                        $user_created_at = date('Y-m-d H:i:s');
                        $name = explode(" ",str_replace("Dr. ", "", $content->name));
                        $doctor_type = match ($content->doctor_type) {
                            1 => 2,
                            2 => 3,
                            3 => 1,
                            4 => 4,
                            default => null,
                        };
                        
                        $contentInsertDoctor[] = [
                            'id' => $content->id,
                            'user_id' => null,
                            'itdose_doctorid' => $content->itdose_doctorid,
                            'first_name' => $name[0] ?? '',
                            'last_name' => implode(' ', array_slice($name, 1)) ?? '',
                            'img_url' => $content->img_url,
                            'secret_key' => $content->secret_key,
                            'doctor_slug' => $content->doctor_slug,
                            'address' => $content->address,
                            'profile' => $content->profile,
                            'registration_no' => $content->registration_no,
                            'docor_mail_verification_frontend' => $content->docor_mail_verification_frontend,
                            'doctor_visit' => null,
                            'visit_price' => $content->visit_price,
                            'degree' => $content->degree,
                            'description' => $content->description,
                            'featured_doctor' => $content->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $content->speciality,
                            'key_procedure_performed' => $content->key_procedure_performed,
                            'status' => $content->status,
                            'doctor_esign' => $content->doctor_esign,
                            'data_source' => $content->data_source,
                            'manage_bill' => $content->manage_bill,
                            'created_by' => 1,
                            'deleted_by' => ($content->is_deleted == 1 ? 1 : null),
                            'created_at' => $user_created_at,
                            'updated_at' => $user_created_at,
                            'deleted_at' => ($content->is_deleted == 1 ? date('Y-m-d H:i:s') : null)
                        ];
                    }
                    else{
                        $user_id = $user->id;
                        $user_created_at = ($user->created_on != 0 ? date('Y-m-d H:i:s', $user->created_on) : date('Y-m-d H:i:s'));
                        $contentInsert[] = [
                            'id' => $user->id,
                            'ip_address' => $user->ip_address,
                            'username' => $user->username,
                            'email' => $user->email,
                            'phone' => $user->phone,
                            'password' => $user->password,
                            'active' => 2,
                            'last_login' => $user->last_login ? date('Y-m-d H:i:s', $user->last_login) : date('Y-m-d H:i:s'),
                            'status' => 0,
                            'created_by' => 1,
                            'created_at' => $user_created_at,
                        ];
                        $name = explode(" ",str_replace("Dr. ", "", $content->name));
                        $doctor_type = match ($content->doctor_type) {
                            1 => 2,
                            2 => 3,
                            3 => 1,
                            4 => 4,
                            default => null,
                        };
                        
                        $contentInsertDoctor[] = [
                            'id' => $content->id,
                            'user_id' => $user_id,
                            'itdose_doctorid' => $content->itdose_doctorid,
                            'first_name' => $name[0] ?? '',
                            'last_name' => implode(' ', array_slice($name, 1)) ?? '',
                            'img_url' => $content->img_url,
                            'secret_key' => $content->secret_key,
                            'doctor_slug' => $content->doctor_slug,
                            'address' => $content->address,
                            'profile' => $content->profile,
                            'registration_no' => $content->registration_no,
                            'docor_mail_verification_frontend' => $content->docor_mail_verification_frontend,
                            'doctor_visit' => null,
                            'visit_price' => $content->visit_price,
                            'degree' => $content->degree,
                            'description' => $content->description,
                            'featured_doctor' => $content->featured_doctor,
                            'doctor_type' => $doctor_type,
                            'speciality' => $content->speciality,
                            'key_procedure_performed' => $content->key_procedure_performed,
                            'status' => $content->status,
                            'doctor_esign' => $content->doctor_esign,
                            'data_source' => $content->data_source,
                            'manage_bill' => $content->manage_bill,
                            'created_by' => 1,
                            'deleted_by' => ($content->is_deleted == 1 ? 1 : null),
                            'created_at' => $user_created_at,
                            'updated_at' => $user_created_at,
                            'deleted_at' => ($content->is_deleted == 1 ? date('Y-m-d H:i:s') : null)
                        ];
                        // role
                        $contentInsertRole[] = [
                            'role_id' => 2,
                            'model_type' => 'Modules\Users\Models\User',
                            'model_id' => $user_id
                        ];
                    }
                    // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                }
                
                if (!empty($contentInsert)) {
                    $this->userService->dataMigrate($contentInsert);
                }
                if (!empty($contentInsertRole)) {
                    DB::transaction(function () use ($contentInsertRole) {
                        DB::table('model_has_roles')->upsert(
                            $contentInsertRole,
                            ['model_id'], // Unique columns to check for duplicates
                            ['role_id'] // Columns to update if a duplicate is found
                        );
                    });
                }
                if (!empty($contentInsertDoctor)) {
                    $this->doctorService->dataMigrate($contentInsertDoctor);
                }
                // dd('each loop completed');
            });
            // dd("Loop iteration completed for district table");
        // }
        dd("completed migration");
        // return back();
    }
    public function dataMigrateOtherDoctor()
    {
        $all_content_users = $this->dbConnectionInstance->table('doctor')
            ->where('status', 2)
            ->count();
            
        // dd($all_content_users);
        $chunkSize_users = 10;
        $chunkCount_users = ceil($all_content_users / $chunkSize_users);

        // for ($i = 0; $i <= $chunkCount_users; $i++) {
            $startId_users = 0;//$i * $chunkSize_users;
            $this->queryBuilder = $this->dbConnectionInstance->table('doctor')
                ->where('status', 2)
                ->orderBy('id')
                ->offset($startId_users)
                ->limit($chunkSize_users);

            $this->queryBuilder->chunk($chunkSize_users, function ($chunk_content_users) {
                // Initialize arrays for content and content tags
                // dd($chunk_content_users);
                $contentInsert = [];
                $contentInsertRole = [];
                $contentInsertDoctor = [];
                // $max_user_id = 0;
                $user_id = 0;
                foreach ($chunk_content_users as $key => $content) {
                    // $max_user_id = DB::table('users')->max('id');
                    // $user = $this->dbConnectionInstance->table('users')->where('id', $content->ion_user_id)->first();
                    // dd($user);
                    // if(!$user){
                        // dd($content->ion_user_id);
                        // $max_user_id++;
                        $user_created_at = date('Y-m-d H:i:s');
                        // $contentInsertNew = [
                        //     'ip_address' => null,
                        //     'username' => $content->name,
                        //     'email' => null,
                        //     'phone' => null,
                        //     'password' => null,
                        //     'active' => 2,
                        //     'status' => 0,
                        //     'created_by' => 1,
                        //     'created_at' => $user_created_at,
                        // ];
                        // $this->userService->setRequest($contentInsertNew);
                        // $user_new = $this->userService->add();
                        // $user_id = $user_new->id;
                    // }
                    // else{
                    //     $user_id = $user->id;
                    //     $user_created_at = ($user->created_on != 0 ? date('Y-m-d H:i:s', $user->created_on) : date('Y-m-d H:i:s'));
                    //     $contentInsert[] = [
                    //         'id' => $user->id,
                    //         'ip_address' => $user->ip_address,
                    //         'username' => $user->username,
                    //         'email' => $user->email,
                    //         'phone' => $user->phone,
                    //         'password' => $user->password,
                    //         'active' => 2,
                    //         'last_login' => $user->last_login ? date('Y-m-d H:i:s', $user->last_login) : date('Y-m-d H:i:s'),
                    //         'status' => 0,
                    //         'created_by' => 1,
                    //         'created_at' => $user_created_at,
                    //     ];
                        
                    // }
                    $name = explode(" ",str_replace("Dr. ", "", $content->name));
                    $doctor_type = match ($content->doctor_type) {
                        1 => 2,
                        2 => 3,
                        3 => 1,
                        4 => 4,
                        default => null,
                    };
                    
                    $contentInsertDoctor[] = [
                        'id' => $content->id,
                        'user_id' => null,
                        'itdose_doctorid' => $content->itdose_doctorid,
                        'first_name' => $name[0] ?? '',
                        'last_name' => implode(' ', array_slice($name, 1)) ?? '',
                        'img_url' => $content->img_url,
                        'secret_key' => $content->secret_key,
                        'doctor_slug' => $content->doctor_slug,
                        'address' => $content->address,
                        'profile' => $content->profile,
                        'registration_no' => $content->registration_no,
                        'docor_mail_verification_frontend' => $content->docor_mail_verification_frontend,
                        'doctor_visit' => null,
                        'visit_price' => $content->visit_price,
                        'degree' => $content->degree,
                        'description' => $content->description,
                        'featured_doctor' => $content->featured_doctor,
                        'doctor_type' => $doctor_type,
                        'speciality' => $content->speciality,
                        'key_procedure_performed' => $content->key_procedure_performed,
                        'status' => 0,
                        'doctor_esign' => $content->doctor_esign,
                        'data_source' => 3,
                        'manage_bill' => $content->manage_bill,
                        'created_by' => 1,
                        'deleted_by' => ($content->is_deleted == 1 ? 1 : null),
                        'created_at' => $user_created_at,
                        'updated_at' => $user_created_at,
                        'deleted_at' => ($content->is_deleted == 1 ? date('Y-m-d H:i:s') : null)
                    ];
                    // role
                    // $contentInsertRole[] = [
                    //     'role_id' => 2,
                    //     'model_type' => 'Modules\Users\Models\User',
                    //     'model_id' => $user_id
                    // ];
                    // dd($contentInsert,$contentInsertRole,$contentInsertDoctor);
                }
                
                // if (!empty($contentInsert)) {
                //     $this->userService->dataMigrate($contentInsert);
                // }
                // if (!empty($contentInsertRole)) {
                //     DB::transaction(function () use ($contentInsertRole) {
                //         DB::table('model_has_roles')->upsert(
                //             $contentInsertRole,
                //             ['model_id'], // Unique columns to check for duplicates
                //             ['role_id'] // Columns to update if a duplicate is found
                //         );
                //     });
                // }
                if (!empty($contentInsertDoctor)) {
                    $this->doctorService->dataMigrate($contentInsertDoctor);
                }
                // dd('each loop completed');
            });
            // dd("Loop iteration completed for district table");
        // }
        dd("completed migration");
        // return back();
    }
    public function dataMigrateOtherDoctorCreateUser()
    {
        $all_content_users = DB::table('doctors')
            ->select('id','user_id','first_name','last_name')
            ->whereNull('user_id')
            ->get();
            
        // dd($all_content_users);
        foreach ($all_content_users as $key => $content) {
            $user_created_at = date('Y-m-d H:i:s');
            $contentInsertNew = [
                'ip_address' => null,
                'username' => $content->first_name.' '.$content->last_name,
                'email' => null,
                'phone' => null,
                'password' => null,
                'active' => 2,
                'status' => 0,
                'created_by' => 1,
                'created_at' => $user_created_at,
            ];
            $this->userService->setRequest($contentInsertNew);
            $user_new = $this->userService->addUserWithRole('Doctor');
            DB::table('doctors')->where('id', $content->id)->update(['user_id' => $user_new->id]);
            // dd($user_new->id);
        }
        dd("completed migration");
        // return back();
    }
    public function dataMigratePatient()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','patient')->select('count','limit')->first();
        $startId = $increment_migration->limit*$increment_migration->count;
        $all_content = $this->dbConnectionInstance->table('patient')
            ->selectRaw('phone,count(*) as total')
            ->whereNotNull('phone')
            ->groupBy('phone')
            ->offset($startId)
            ->limit($increment_migration->limit)
            ->pluck('total','phone')->toArray();
        // dd($all_content);
        if (count($all_content) == 0) {
            dd("completed migration");
        }
        foreach ($all_content as $phone => $no_patient) {
            $chunkSize = $no_patient;
            $family_id = 'FI'.substr($phone, -4).random_int(1000, 9999);
            $this->queryBuilder = $this->dbConnectionInstance->table('patient')
                ->where('phone',$phone)
                ->orderBy('phone')
                ->orderBy('id');
                // ->offset($startId)
                // ->limit($chunkSize);
            // dd($this->queryBuilder->get());
            $this->queryBuilder->chunk($chunkSize, function ($chunk_content) use ($family_id) {
                // Initialize arrays for content and content tags
                $contentInsert = [];
                $parent_id;
                foreach ($chunk_content as $key =>$content) {
                    $language_id = match ($content->preferred_language) {
                        1 => 2,
                        2 => 1,
                        3 => 3,
                        default => 2,
                    };
                    $add_date = date('Y-m-d',strtotime('03/11/25'));
                    if ($content->birthdate) {
                        $birthdate = $content->birthdate;
                    }
                    else {
                        $age = $content->age ?? 0;
                        $add_date_cal = $add_date ?? date('Y-m-d');
                        $birthdate = $this->calculateDOB($age,$add_date_cal);
                    }
                    
                    $created_at = ($add_date ? date('Y-m-d H:i:s', strtotime($add_date)) : date('Y-m-d H:i:s'));
                    // parent patient
                    if ($key == 0) {
                        $parent_id = $content->id;
                        $contentInsert[] = [
                            'id' => $content->id,
                            'family_id' => $family_id,
                            'language_id' => $language_id,
                            'relationship_id' => null,
                            'img_url' => $content->img_url,
                            'name' => $content->name,
                            'email' => $content->email,
                            'doctor_id' => $content->doctor,
                            'address' => $content->address,
                            'phone' => $content->phone,
                            'sex' => $content->sex,
                            'birthdate' => $birthdate,
                            'pincode' => $content->pincode,
                            'bloodgroup' => $content->bloodgroup,
                            'parent_id' => null,
                            'uhid_no' => $content->uhid_no,
                            'is_employee' => $content->is_employee,
                            'status' => 1,
                            'created_by' => 1,
                            'created_at' => $created_at,
                        ];
                    }
                    else {// member patient
                        $contentInsert[] = [
                            'id' => $content->id,
                            'family_id' => $family_id,
                            'language_id' => $language_id,
                            'relationship_id' => 8,
                            'img_url' => $content->img_url,
                            'name' => $content->name,
                            'email' => $content->email,
                            'doctor_id' => $content->doctor,
                            'address' => $content->address,
                            'phone' => null,
                            'sex' => $content->sex,
                            'birthdate' => $birthdate,
                            'pincode' => $content->pincode,
                            'bloodgroup' => $content->bloodgroup,
                            'parent_id' => $parent_id,
                            'uhid_no' => $content->uhid_no,
                            'is_employee' => $content->is_employee,
                            'status' => 1,
                            'created_by' => 1,
                            'created_at' => $created_at,
                        ];
                    }
                }
                // dd($contentInsert);
                if (!empty($contentInsert)) {
                    $this->patientService->dataMigrate($contentInsert);
                }
                // dd('each loop completed');
            });
            // dd("Loop iteration completed for district table");
        }
        DB::table('temp_increment_migrations')->where('table_name','patient')->update([
            'count' => $increment_migration->count+1,
        ]);
        dd("Loop iteration completed");
        // return back();
    }
    private function calculateDOB($age,$add_date) {
        $age = intval($age);
        if (!is_int($age)) {
            $age = 0;
        }
        $diff = floor((strtotime(date('Y-m-d')) - strtotime($add_date)) / (60 * 60 * 24 * 365));
        $age = $age + $diff;
        $birthdate = date('Y-01-01', strtotime("-$age years"));
        return $birthdate;
    }
    public function dataMigrateMembership()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_registrations')->select('count','limit')->first();

        $all_content = $this->dbConnectionInstance->table('arogya_bandhu_registrations')
            ->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        // dd($chunkSize,$increment_migration->count);
        // for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->count * $chunkSize;
            if ($startId > $all_content) {
                dd("completed migration");
            }
            // dd($startId);
            $this->queryBuilder = $this->dbConnectionInstance->table('arogya_bandhu_registrations')
                ->orderBy('record_id')
                ->offset($startId)
                ->limit($chunkSize);
            // dd($this->queryBuilder->get());
            // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
                // Initialize arrays for content and content tags
                // $contentInsert = [];
                foreach ($this->queryBuilder->get() as $content) {
                    // dd($content->record_id);
                    $all_billing = $this->dbConnectionInstance->table('arogya_bandhu_billing')
                        ->where('registration_id', $content->record_id)
                        // ->where('mode_of_payment', '!=', 'reward_points')
                        ->orderBy('id')
                        ->get();
                    if (count($all_billing) > 0) {
                        $arogya_bandhu_billing = $this->dbConnectionInstance->table('arogya_bandhu_billing')
                            ->where('registration_id', $content->record_id)
                            ->where('mode_of_payment', '!=', 'reward_points')
                            ->orderBy('id')
                            ->get();
                        $reward_points = $this->dbConnectionInstance->table('arogya_bandhu_billing')
                            ->where('registration_id', $content->record_id)
                            ->where('mode_of_payment', 'reward_points')
                            ->select('payment_amount','openning_points','closing_points')
                            ->first();
                        $reward_point_debit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $content->record_id)
                            ->where('type', 3)
                            ->where('credit_debit', 2)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        $reward_point_credit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $content->record_id)
                            ->where('type', 3)
                            ->where('credit_debit', 1)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        $phone = $this->patientService->getParentPhone($content->patient_id);
                        $clinic_id = $this->dbConnectionInstance->table('clinic')
                            ->where('ion_user_id', ($all_billing[0]->clinic_id != 0 ? $all_billing[0]->clinic_id : $all_billing[0]->created_by))
                            ->value('clinic_id');
                        $membership = DB::table('memberships')->where('name', $content->card_type)->select('id','category_id','price')->first();
                        $category_id = $membership->category_id ?? null;
                        $card_type = $membership->id ?? null;
                        $created_by = $content->mem_created_by ? $content->mem_created_by : 1;
                        $created_at = $content->mem_created_at ? date('Y-m-d H:i:s', strtotime($content->mem_created_at)) : date('Y-m-d H:i:s', strtotime($content->registration_date));
                        
                        // dd($arogya_bandhu_billing,$reward_points);
                        // membership_registrations
                        $contentInsert = [
                            'id' => $content->record_id,
                            'patient_id' => $content->patient_id,
                            'phone' => $phone ?? null,
                            'clinic_id' => $clinic_id ?? null,
                            'start_date' => $content->start_date,
                            'end_date' => $content->end_date,
                            'registration_no' => $content->registration_no,
                            'category_id' => $category_id,
                            'card_type' => $card_type,
                            'registration_date' => $content->registration_date,
                            'smart_card' => $content->smart_card,
                            'data_source' => $content->data_source,
                            'source' => $content->source,
                            'pincode' => $content->pincode,
                            'remarks' => $content->remarks,
                            'is_renewal' => $content->is_renewal,
                            'is_complementory_availed' => $content->is_complementory_availed,
                            'complementory_diagnostic_test' => $content->complementory_diagnostic_test,
                            'complementory_diagnostic_no_of_occurrence' => $content->complementory_diagnostic_no_of_occurrence,
                            'homecollection_no_of_occurrence' => $content->homecollection_no_of_occurrence,
                            'status' => $content->status,
                            'created_by' => $created_by,
                            'created_at' => $created_at,
                            'updated_at' => $created_at
                        ];
                        $this->billingService->dataMigrate($contentInsert);
                        // payment bill create
                        $paymentData = [
                            'total_amount' => $arogya_bandhu_billing->sum('payment_amount'),
                            'paid_amount' => $arogya_bandhu_billing->sum('payment_amount'),
                            'created_by' => $created_by,
                            'created_at' => $created_at,
                            'updated_at' => $created_at,
                            'redeem_points' => $reward_points->payment_amount ?? null,
                            'openning_points' => $reward_points->openning_points ?? null,
                            'closing_points' => $reward_points->closing_points ?? null,
                            'payment_modes' => $arogya_bandhu_billing->pluck('mode_of_payment')->toArray(),
                            'payment_details' => $arogya_bandhu_billing->toArray(),
                            'reward_point_debit' => $reward_point_debit->toArray(),
                            'reward_point_credit' => $reward_point_credit->toArray()
                        ];

                        $pay_type = config('billing.types.0');
                        $bill_show_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $content->record_id])->value('bill_show_id');
                        // dd($paymentData['reward_point_debit'][0]->point);
                        if ($bill_show_id) {
                            $bill_id = $bill_show_id;
                        }
                        else{
                            $bill_id = isset($clinic_id) ? $this->paymentBillService->billingIncrementId($clinic_id,'MB',8) : null;
                        }
                        
                        $bill_amount = $membership->price ?? null;//$arogya_bandhu_billing->sum('payment_amount');
                        // dd($bill_amount);
                        // payment here
                        $this->paymentWithReward($paymentData,$bill_id,$pay_type,$bill_amount,0,$content->record_id,$content->patient_id,$phone);
                    }
                }
                // dd($contentInsert);
                // if (!empty($contentInsert)) {
                //     $this->billingService->dataMigrate($contentInsert);
                // }
                // dd('each loop completed');
            // });
            
        // }
        DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_registrations')->update([
            'count' => $increment_migration->count+1,
        ]);
        dd("Loop iteration completed");
        // return back();
    }
    private function paymentWithReward($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    {
        // dd($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone);
        $data = [
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'discount' => $discount,
            'total_amount' => $paymentData['total_amount'],
            'paid_amount' => $paymentData['paid_amount'],
            'due_amount' => 0,
            'created_by' => $paymentData['created_by'],
            'created_at' => $paymentData['created_at'],
            'updated_at' => $paymentData['created_at']
        ];
        // dd($service->paymentBill);
        $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
        $this->paymentBillService->setRequest($data);
        if($payment_bill_id){
            $this->paymentBillService->findById($payment_bill_id);
            $paymentBill = $this->paymentBillService->update();
        }
        else {
            $paymentBill = $this->paymentBillService->add();
        }
        // dd($paymentBill);
        if($paymentData['redeem_points'] > 0 || isset($paymentData['payment_modes'])){
            // payment create
            $data_payment = [
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d',strtotime($paymentData['created_at'])),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','MB-receipt',8),
                'status' => 'Paid',
                'amount' => $paymentData['paid_amount'],
                'redeem_points' => $paymentData['redeem_points'],
                'openning_points' => $paymentData['openning_points'],
                'closing_points' => $paymentData['closing_points'],
                'created_by' => $paymentData['created_by'],
                'created_at' => $paymentData['created_at'],
                'updated_at' => $paymentData['created_at']
            ];
            $payment_id = DB::table('payments')->where(['bill_id' => $paymentBill->id])->value('id');
            $this->paymentService->setRequest($data_payment);
            if($payment_id){
                $this->paymentService->findById($payment_id);
                $payment = $this->paymentService->update();
            }
            else {
                $payment = $this->paymentService->add();
            }
            // dd($paymentData['payment_details']);
            // payment details
            $total_amount = 0;
            $total_discount = 0;
            if (isset($paymentData['payment_modes'])) {
                DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
                foreach ($paymentData['payment_details'] as $key => $row) {
                    $data_payment_dtl = [
                        'created_by' => $paymentData['created_by'],
                        'created_at' => $paymentData['created_at'],
                        'updated_at' => $paymentData['created_at']
                    ];
                    $data_payment_dtl['payment_id'] = $payment->id;
                    $data_payment_dtl['payment_mode'] = $row->mode_of_payment;
                    $data_payment_dtl['amount'] = $row->payment_amount;
                    $data_payment_dtl['payment_details'] = $row->payment_details;
                    $total_amount += $row->payment_amount;
                    // dd($data_payment_dtl);
                    $this->paymentDetailService->setRequest($data_payment_dtl);
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }            
            // // reward points debit
            // if(!empty($paymentData['reward_point_debit'])){
            //     foreach ($paymentData['reward_point_debit'] as $debit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => ($debit->point)*-1,
            //             'credit_debit' => 2,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
                
            // }
            // // reward points credit
            // // $percentage = $this->rewardService->getPercentage(2);
            // // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
            // if(!empty($paymentData['reward_point_credit'])){
            //     foreach ($paymentData['reward_point_credit'] as $credit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => $credit->point,
            //             'credit_debit' => 1,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id,
            //             // 'expiry' => $credit->expiry,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
            // }
        }
        // dd('payment done');
        return true;
    }
    public function dataMigrateMembershipCard()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_smart_cards')->select('count','limit')->first();

        $all_content = $this->dbConnectionInstance->table('arogya_bandhu_registrations')
            ->where('smart_card',2)
            ->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        // dd($chunkSize,$increment_migration->count);
        // for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->count * $chunkSize;
            if ($startId > $all_content) {
                dd("completed migration");
            }
            // dd($startId);
            $this->queryBuilder = $this->dbConnectionInstance->table('arogya_bandhu_registrations')
                ->where('smart_card',2)
                // ->where('record_id',14418)
                ->orderBy('record_id')
                ->offset($startId)
                ->limit($chunkSize);
            // dd($this->queryBuilder->get());
            // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
                // Initialize arrays for content and content tags
                // $contentInsert = [];
                foreach ($this->queryBuilder->get() as $content) {
                    // dd($content->record_id);
                    $record_id = $content->record_id;
                    $payment = $this->dbConnectionInstance->table('payment')
                        ->select(
                            'id','date','amount','gross_total','remarks','payment_mode','breakup_payment_amount',
                            'payment_details','created_at','created_by','redeem_points','openning_points','closing_points'
                        )
                        ->where('absmartcard_membershipid', $record_id)
                        ->orderBy('id')
                        ->get();
                    // dd($payment);
                    if (count($payment) > 0) {
                        $reward_point_debit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $record_id)
                            ->where('type', 4)
                            ->where('credit_debit', 2)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        $reward_point_credit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $record_id)
                            ->where('type', 4)
                            ->where('credit_debit', 1)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        // dd($reward_point_debit,$reward_point_credit);
                        $phone = $this->patientService->getParentPhone($content->patient_id);
                        // dd($phone);
                        
                        $created_by = $payment[0]->created_by ? $payment[0]->created_by : 1;
                        $created_at = $payment[0]->created_at ? date('Y-m-d H:i:s', strtotime($payment[0]->created_at)) : date('Y-m-d H:i:s', strtotime($payment[0]->date));
                        
                        // dd($payment,$reward_points);
                        // payment bill create
                        $paymentCardData = [
                            'total_amount' => $payment->sum('gross_total'),
                            'paid_amount' => $payment->sum('gross_total'),
                            'created_by' => $created_by,
                            'created_at' => $created_at,
                            'updated_at' => $created_at,
                            'redeem_points' => $payment[0]->redeem_points ?? null,
                            'openning_points' => $payment[0]->openning_points ?? null,
                            'closing_points' => $payment[0]->closing_points ?? null,
                            'payment_modes' => $payment[0]->payment_mode,
                            'payment_details' => $payment->toArray(),
                            'reward_point_debit' => $reward_point_debit->toArray(),
                            'reward_point_credit' => $reward_point_credit->toArray()
                        ];
                        
                        $bill_id = DB::table('payment_bill_master')
                            ->where(['service_id'=>$content->record_id,'type'=>'MB'])
                            ->value('bill_show_id');
                        $pay_type = config('billing.types.3');
                        $bill_amount = 30;
                        // dd($bill_id,$pay_type,$bill_amount);
                        // payment here
                        $this->paymentCardWithReward($paymentCardData,$bill_id,$pay_type,$bill_amount,0,$content->record_id,$content->patient_id,$phone);
                    }
                }
                // dd($contentInsert);
                // if (!empty($contentInsert)) {
                //     $this->billingService->dataMigrate($contentInsert);
                // }
                // dd('each loop completed');
            // });
            
        // }
        DB::table('temp_increment_migrations')->where('table_name','arogya_bandhu_smart_cards')->update([
            'count' => $increment_migration->count+1,
        ]);
        dd("Loop iteration completed");
        // return back();
    }
    private function paymentCardWithReward($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    {
        // dd($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone);
        $data = [
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'discount' => $discount,
            'total_amount' => $paymentData['total_amount'],
            'paid_amount' => $paymentData['paid_amount'],
            'due_amount' => 0,
            'created_by' => $paymentData['created_by'],
            'created_at' => $paymentData['created_at'],
            'updated_at' => $paymentData['created_at']
        ];
        // dd($service_id);
        $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
        $this->paymentBillService->setRequest($data);
        if($payment_bill_id){
            $this->paymentBillService->findById($payment_bill_id);
            $paymentBill = $this->paymentBillService->update();
        }
        else {
            $paymentBill = $this->paymentBillService->add();
        }
        // dd($paymentData['redeem_points']);
        if($paymentData['redeem_points'] > 0 || isset($paymentData['payment_modes'])){
            // payment create
            $data_payment = [
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d',strtotime($paymentData['created_at'])),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','MB-receipt',8),
                'status' => 'Paid',
                'amount' => $paymentData['paid_amount'],
                'redeem_points' => $paymentData['redeem_points'],
                'openning_points' => $paymentData['openning_points'],
                'closing_points' => $paymentData['closing_points'],
                'created_by' => $paymentData['created_by'],
                'created_at' => $paymentData['created_at'],
                'updated_at' => $paymentData['created_at']
            ];
            $payment_id = DB::table('payments')->where(['bill_id' => $paymentBill->id])->value('id');
            $this->paymentService->setRequest($data_payment);
            if($payment_id){
                $this->paymentService->findById($payment_id);
                $payment = $this->paymentService->update();
            }
            else {
                $payment = $this->paymentService->add();
            }
            // payment details
            $total_amount = 0;
            $total_discount = 0;
            // dd($paymentData);
            if ($paymentData['payment_modes'] != '') {
                DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
                $payment_modes = explode(',',$paymentData['payment_details'][0]->payment_mode);
                $amounts = explode(',',$paymentData['payment_details'][0]->breakup_payment_amount);
                $payment_details_arr = explode(',',$paymentData['payment_details'][0]->payment_details);
                foreach ($payment_modes as $key => $row) {
                    switch ($row) {
                        case 'credit_card':
                            $payment_details = $payment_details_arr[$key-1] ?? '';
                            break;
                        case 'debit_card':
                            $payment_details = $payment_details_arr[$key-1] ?? '';
                            break;
                        case 'upi':
                            $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
                            break;
                        default:
                            $payment_details = null;
                            break;
                    }
                    $data_payment_dtl = [
                        'created_by' => $paymentData['created_by'],
                        'created_at' => $paymentData['created_at'],
                        'updated_at' => $paymentData['created_at']
                    ];
                    $data_payment_dtl['payment_id'] = $payment->id;
                    $data_payment_dtl['payment_mode'] = $row;
                    $data_payment_dtl['amount'] = $amounts[$key] ?? 0;
                    $data_payment_dtl['payment_details'] = $payment_details;
                    $total_amount += $amounts[$key] ?? 0;
                    // dd($data_payment_dtl);
                    $this->paymentDetailService->setRequest($data_payment_dtl);
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }
            // // reward points debit
            // if(!empty($paymentData['reward_point_debit'])){
            //     foreach ($paymentData['reward_point_debit'] as $debit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => ($debit->point)*-1,
            //             'credit_debit' => 2,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
                
            // }
            // // reward points credit
            // // $percentage = $this->rewardService->getPercentage(2);
            // // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
            // if(!empty($paymentData['reward_point_credit'])){
            //     foreach ($paymentData['reward_point_credit'] as $credit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => $credit->point,
            //             'credit_debit' => 1,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id,
            //             // 'expiry' => $credit->expiry,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
            // }
        }
        // dd('payment done');
        return true;
    }
    public function dataMigrateAppoinment()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointments')->select('count','limit')->first();
        $all_content = $this->dbConnectionInstance->table('appointment')->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);

        $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
        if ($startId > $all_content) {
            dd("completed migration");
        }
        $this->queryBuilder = $this->dbConnectionInstance->table('appointment')
            ->orderBy('id')
            ->offset($startId)
            ->limit($all_content)->get();
        $chunk = $this->queryBuilder->chunk($chunkSize);
        // dd($chunk);
        foreach ($chunk as $chunk_contents) {
            $contentInsert = [];
            foreach ($chunk_contents as $content) {
                // dd(date('Y-m-d',$content->date));
                $timestamp = $content->created_at != '' ? $content->created_at : null;
                $phone = $this->patientService->getParentPhone($content->patient);
                $doctor_id = $this->dbConnectionInstance->table('doctor')->where('id', $content->doctor)->value('ion_user_id');
                $schedule_id = DB::table('time_schedules')->where([
                    'doctor_id' => $doctor_id,
                    'clinic_id' => $content->clinic_id,
                    'date' => date('Y-m-d',$content->date),
                    's_time' => $content->s_time,
                    'e_time' => $content->e_time,
                ])->value('id');
                // dd($schedule_id);
                $contentInsert[] = [
                    'id' => $content->id,
                    'patient_id' => $content->patient,
                    'patient_phone' => $phone,
                    'doctor_id' => $doctor_id,
                    'clinic_id' => $content->clinic_id,
                    'date' => date('Y-m-d',$content->date),
                    'time_slot' => $content->time_slot,
                    'schedule_id' => $schedule_id ?? null,
                    'remarks' => $content->remarks,
                    'payment_status' => $content->payment_status,
                    // 'unique_bill_id' => $content->unique_bill_id,
                    'unique_queue_number' => $content->unique_queue_number,
                    'cancel_reason' => $content->cancel_reason,
                    'appointment_type' => $content->appointment_type,
                    'hand_write_prescription' => $content->hand_write_prescription,
                    'e_prescription_upload_fairbase' => $content->e_prescription_upload_fairbase,
                    'data_source' => $content->data_source,
                    'is_exist' => $content->is_exist,
                    'executive_name' => $content->executive_name,
                    'executive_name_remarks' => $content->executive_name_remarks,
                    // 'meeting_id' => $content->meeting_id,
                    'status' => $content->status,
                    'created_by' => $content->created_by ? $content->created_by : 1,
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp
                ];
            }
            if (!empty($contentInsert)) {
                $this->appointmentService->dataMigrate($contentInsert);
            }
        }
        
        dd("Loop iteration completed");
    }
    public function dataMigrateAppoinmentBill()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointment_bill')->select('count','limit')->first();

        $all_content = $this->dbConnectionInstance->table('appointment as a')
            ->join('payment as p', 'p.appointment_id', '=', 'a.id')
            ->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        // dd($chunkSize,$increment_migration->count);
        // for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->count * $chunkSize;
            if ($startId > $all_content) {
                dd("completed migration");
            }
            // dd($startId);
            $this->queryBuilder = $this->dbConnectionInstance->table('appointment as a')
                ->join('payment as p', 'p.appointment_id', '=', 'a.id')
                ->select('a.unique_bill_id','a.patient as patient_id', 'p.*')
                ->orderBy('p.id')
                ->offset($startId)
                ->limit($chunkSize);
            // dd($this->queryBuilder->get());
            // $this->queryBuilder->chunk($chunkSize, function ($chunk_content) {
                // Initialize arrays for content and content tags
                // $contentInsert = [];
                foreach ($this->queryBuilder->get() as $content) {
                    $exciting_cnt = DB::table('payment_bill_master')
                            ->where(['service_id'=>$content->appointment_id,'type'=>'OPD'])
                            ->count();
                    // dd($exciting_cnt);
                    if ($exciting_cnt == 0) {
                        $service_id = $content->appointment_id;
                        $reward_point_debit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $service_id)
                            ->where('type', 1)
                            ->where('credit_debit', 2)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        $reward_point_credit = $this->dbConnectionInstance->table('reward_points')
                            ->where('bill_id', $service_id)
                            ->where('type', 1)
                            ->where('credit_debit', 1)
                            ->select('bill_id','point','credit_debit','is_redeem')
                            ->get();
                        // dd($reward_point_debit,$reward_point_credit);
                        $phone = $this->patientService->getParentPhone($content->patient_id);
                        
                        $created_by = $content->created_by ? $content->created_by : 1;
                        $created_at = $content->created_at ? date('Y-m-d H:i:s', strtotime($content->created_at)) : date('Y-m-d H:i:s', strtotime($content->date));
                        
                        // dd($created_by,$created_at);
                        // payment bill create
                        $paymentCardData = [
                            'total_amount' => $content->gross_total,
                            'paid_amount' => $content->gross_total,
                            'created_by' => $created_by,
                            'created_at' => $created_at,
                            'updated_at' => $created_at,
                            'redeem_points' => $content->redeem_points ?? null,
                            'openning_points' => $content->openning_points ?? null,
                            'closing_points' => $content->closing_points ?? null,
                            'payment_modes' => $content->payment_mode,
                            'payment_details' => $content,
                            'reward_point_debit' => $reward_point_debit->toArray(),
                            'reward_point_credit' => $reward_point_credit->toArray()
                        ];
                        // dd($paymentCardData);
                        $bill_id = $content->unique_bill_id ?? null;
                        $pay_type = config('billing.types.1');
                        $bill_amount = $content->amount;
                        $discount = $content->discount != '' ? $content->discount : 0;
                        // dd($bill_id,$pay_type,$bill_amount);
                        // payment here
                        $this->paymentAppointmentWithReward($paymentCardData,$bill_id,$pay_type,$bill_amount,$discount,$service_id,$content->patient_id,$phone);
                        
                        $data_appointment = [
                            'payment_status' => 'paid',
                            'modified_by' => $created_by,
                            'updated_at' => $created_at
                        ];
                        $this->appointmentService->findById($service_id);
                        $this->appointmentService->setRequest($data_appointment);
                        $this->appointmentService->update();
                    }
                }
                // dd($contentInsert);
                // if (!empty($contentInsert)) {
                //     $this->billingService->dataMigrate($contentInsert);
                // }
                // dd('each loop completed');
            // });
            
        // }
        DB::table('temp_increment_migrations')->where('table_name','appointment_bill')->update([
            'count' => $increment_migration->count+1,
        ]);
        dd("Loop iteration completed");
        // return back();
    }
    private function paymentAppointmentWithReward($paymentData,$bill_id,$pay_type,$bill_amount,$discount = 0,$service_id,$patient_id,$phone)
    {
        // dd($paymentData['payment_modes']);
        $data = [
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'discount' => $discount,
            'total_amount' => $paymentData['total_amount'],
            'paid_amount' => $paymentData['paid_amount'],
            'due_amount' => 0,
            'membership_registration_no' => $paymentData['payment_details']->arrogya_membership_no ?? null,
            'created_by' => $paymentData['created_by'],
            'created_at' => $paymentData['created_at'],
            'updated_at' => $paymentData['created_at']
        ];
        // dd($service_id);
        $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
        $this->paymentBillService->setRequest($data);
        if($payment_bill_id){
            $this->paymentBillService->findById($payment_bill_id);
            $paymentBill = $this->paymentBillService->update();
        }
        else {
            $paymentBill = $this->paymentBillService->add();
        }
        // dd($paymentBill);
        if($paymentData['redeem_points'] > 0 || isset($paymentData['payment_modes'])){
            // payment create
            $data_payment = [
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d',strtotime($paymentData['created_at'])),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','OPD-receipt',8),
                'status' => 'Paid',
                'amount' => $paymentData['paid_amount'],
                'redeem_points' => $paymentData['redeem_points'],
                'openning_points' => $paymentData['openning_points'],
                'closing_points' => $paymentData['closing_points'],
                'created_by' => $paymentData['created_by'],
                'created_at' => $paymentData['created_at'],
                'updated_at' => $paymentData['created_at']
            ];
            $payment_id = DB::table('payments')->where(['bill_id' => $paymentBill->id])->value('id');
            $this->paymentService->setRequest($data_payment);
            if($payment_id){
                $this->paymentService->findById($payment_id);
                $payment = $this->paymentService->update();
            }
            else {
                $payment = $this->paymentService->add();
            }
            // payment details
            $total_amount = 0;
            $total_discount = 0;
            // dd($payment);
            if ($paymentData['payment_modes'] != '' && $paymentData['total_amount'] > 0) {
                DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
                $payment_modes = explode(',',$paymentData['payment_details']->payment_mode);
                $amounts = explode(',',$paymentData['payment_details']->breakup_payment_amount);
                $payment_details_arr = explode(',',$paymentData['payment_details']->payment_details);
                // dd($payment_modes,$amounts,$payment_details_arr);
                foreach ($payment_modes as $key => $row) {
                    switch ($row) {
                        case 'credit_card':
                            $payment_details = $payment_details_arr[$key-1] ?? '';
                            break;
                        case 'debit_card':
                            $payment_details = $payment_details_arr[$key-1] ?? '';
                            break;
                        case 'upi':
                            $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
                            break;
                        default:
                            $payment_details = null;
                            break;
                    }
                    $data_payment_dtl = [
                        'created_by' => $paymentData['created_by'],
                        'created_at' => $paymentData['created_at'],
                        'updated_at' => $paymentData['created_at']
                    ];
                    $data_payment_dtl['payment_id'] = $payment->id;
                    $data_payment_dtl['payment_mode'] = $row;
                    $data_payment_dtl['amount'] = $amounts[$key] ?? 0;
                    $data_payment_dtl['payment_details'] = $payment_details;
                    $total_amount += $amounts[$key] ?? 0;
                    // dd($data_payment_dtl);
                    $this->paymentDetailService->setRequest($data_payment_dtl);
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }
            // // reward points debit
            // if(!empty($paymentData['reward_point_debit'])){
            //     foreach ($paymentData['reward_point_debit'] as $debit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => ($debit->point)*-1,
            //             'credit_debit' => 2,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $debit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
                
            // }
            // // reward points credit
            // // $percentage = $this->rewardService->getPercentage(2);
            // // $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
            // if(!empty($paymentData['reward_point_credit'])){
            //     foreach ($paymentData['reward_point_credit'] as $credit) {
            //         $reward = [
            //             'phone_no' => $phone,
            //             'date' => $paymentData['created_at'],
            //             'type' => $pay_type,
            //             'point' => $credit->point,
            //             'credit_debit' => 1,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id,
            //             // 'expiry' => $credit->expiry,
            //             'created_by' => $paymentData['created_by'],
            //             'created_at' => $paymentData['created_at'],
            //             'updated_at' => $paymentData['created_at']
            //         ];
            //         $reward_id = DB::table('reward_points')->where([
            //             'phone_no' => $phone,
            //             'type' => $pay_type,
            //             'is_redeem' => $credit->is_redeem,
            //             'bill_id' => $bill_id
            //         ])->value('id');
            //         $this->rewardService->setRequest($reward);
            //         if($reward_id){
            //             $this->rewardService->findById($reward_id);
            //             $this->rewardService->update();
            //         }
            //         else {
            //             $this->rewardService->add();
            //         }
            //     }
            // }
        }
        // dd('payment done');
        return true;
    }
    public function dataMigrateAppoinmentVital()
    {
        try {
            Log::info('Migration started for appointment_vitals.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','appointment_vitals')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('appointment as a')
            ->join('patient_vitals as p', 'p.appointment_id', '=', 'a.id')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('appointment as a')
                ->join('patient_vitals as p', 'p.appointment_id', '=', 'a.id')
                ->select('p.*')
                ->orderBy('p.vitals_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd(date('Y-m-d',$content->date));
                    $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->vitals_id,
                        'patient_id' => $content->patient_id,
                        'appointment_id' => $content->appointment_id,
                        'blood_pressure' => $content->blood_pressure,
                        'heart_rate' => $content->heart_rate,
                        'temperature' => $content->temperature,
                        'spo2' => $content->spo2,
                        'height' => $content->height,
                        'weight' => $content->weight,
                        'bmi' => $content->bmi,
                        'blood_group' => $content->blood_group,
                        'head_circumference' => $content->head_circumference,
                        'status' => 1,
                        'created_by' => 1,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                }
                if (!empty($contentInsert)) {
                    $this->vitalsService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateAppoinmentPrescription()
    {
        try {
            Log::info('Migration started for prescription_template.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','prescriptions')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('appointment as a')
            ->join('prescription as p', 'p.appointment_id', '=', 'a.id')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('appointment as a')
                ->join('prescription as p', 'p.appointment_id', '=', 'a.id')
                ->select('p.*')
                ->orderBy('p.id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $medicine_total = explode('###',$content->medicine);
                    if ($medicine_total[count($medicine_total)-1] == '') {
                        array_pop($medicine_total);
                    }
                    $medicines = json_decode($content->med_nm_id);
                    $tests = [];
                    if ($content->labtest_id) {
                        $tests = json_decode($content->labtest_id);
                        if (!$tests) {
                            $tests = explode(',',$content->labtest_id);
                        }
                        elseif (!is_array($tests)) {
                            $tests = [$content->labtest_id];
                        }
                    }
                    // dd($tests);
                    $doctor_id = $this->dbConnectionInstance->table('doctor')->where('id', $content->doctor)->value('ion_user_id');
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->date));
                    $contentInsert[] = [
                        'id' => $content->id,
                        'appointment_id' => $content->appointment_id,
                        'template_id' => $content->template_id,
                        'doctor_id' => $doctor_id,
                        'patient_id' => $content->patient,
                        'symptom' => $content->symptom,
                        'observation' => $content->observation,
                        'advice' => $content->advice,
                        'diagnosis_examination' => $content->diagnosis_examination,
                        'chief_complaints' => $content->note,
                        'next_followup_date' => $content->next_followup_date,
                        'date' => $content->date,
                        'status' => 1,
                        'created_by' => $doctor_id,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    if (is_iterable($medicine_total) && !empty($medicine_total)) {
                        foreach ($medicine_total as $key => $medicine_row) {
                            $medicine_col = explode('***',$medicine_row);
                            $name = explode('*',$medicine_col[0]);
                            $type_id = isset($medicines[$key]) ? ($medicines[$key] != 'new' ? $medicines[$key] : null) : (count($name) > 1 ? $name[0] : null);
                            $contentInsertChild[] = [
                                'prescription_id' => $content->id,
                                'type' => 'M',
                                'type_id' => $type_id,
                                'name' => count($name) > 1 ? $name[1] : $medicine_col[0],
                                'frequency' => $medicine_col[1] ?? null,
                                'days' => $medicine_col[2] ?? null,
                                'instruction' => $medicine_col[3] ?? null,
                                'notes' => $medicine_col[4] ?? null,
                                'status' => 1,
                                'created_by' => $doctor_id,
                                'created_at' => $timestamp,
                                'updated_at' => $timestamp
                            ];
                        }
                    }
                    if (!empty($tests)) {
                        foreach ($tests as $key => $test_row) {
                            $name = DB::table('tests')->where('id',$test_row)->value('test_name');
                            $contentInsertChild[] = [
                                'prescription_id' => $content->id,
                                'type' => 'T',
                                'type_id' => $test_row ?? null,
                                'name' => $name ?? null,
                                'frequency' => null,
                                'days' => null,
                                'instruction' => null,
                                'notes' => null,
                                'status' => 1,
                                'created_by' => $doctor_id,
                                'created_at' => $timestamp,
                                'updated_at' => $timestamp
                            ];
                        }
                    }
                }
                if (!empty($contentInsert)) {
                    $this->prescriptionService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for prescription.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertChild)) {
                    $this->prescriptionChildService->dataMigrate($contentInsertChild);
                    Log::info('Chunk migrated successfully for prescription child.', ['count' => count($contentInsertChild)]);
                }
                // dd('hiii');
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureBrands()
    {
        try {
            Log::info('Migration started for master_brands.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_brands')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('master_brand')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('master_brand')
                ->orderBy('brand_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'brand_id' => $content->brand_id,
                        'c_brand_code' => $content->c_brand_code,
                        'c_brand_name' => $content->c_brand_name,
                        'added_on' => $content->added_on,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('master_brands')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_brands')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for master_brands.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureHsns()
    {
        try {
            Log::info('Migration started for master_hsns.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_hsns')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('master_hsn')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('master_hsn')
                ->orderBy('hsn_code')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'hsn_code' => $content->hsn_code,
                        'hsn_sac_code' => $content->hsn_sac_code,
                        'hsn_sac_name' => $content->hsn_sac_name,
                        'hsn_sac_flag' => $content->hsn_sac_flag,
                        'added_on' => $content->added_on,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('master_hsns')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_hsns')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for master_hsns.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureSuppliers()
    {
        try {
            Log::info('Migration started for master_suppliers.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','master_suppliers')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('master_supplier')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('master_supplier')
                ->orderBy('supplier_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->added_on));
                    $contentInsert[] = [
                        'supplier_id' => $content->supplier_id,
                        'c_supplier_code' => $content->c_supplier_code,
                        'supplier_name' => $content->supplier_name,
                        'added_on' => $content->added_on,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('master_suppliers')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'master_suppliers')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for master_suppliers.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqureMaster()
    {
        try {
            Log::info('Migration started for Csqure Master.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','pharmacy_sales_reg_line_masters')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('cron_executed_rows')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('cron_executed_rows')
                ->orderBy('record_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $date = $content->date;
                    $year = '20' . substr($date, 0, 2);
                    $month = substr($date, 2, 2);
                    $day = substr($date, 4, 2);
                    $formattedDate = "$year-$month-$day";
                    if (checkdate($month, $day, $year)) {
                        $timestamp = date('Y-m-d H:i:s',strtotime($formattedDate));
                    }
                    else {
                        $formattedDate = $content->date;
                        $timestamp = date('Y-m-d H:i:s',strtotime($content->date));
                    }
                    // dd($formattedDate);
                    $contentInsert[] = [
                        'id' => $content->record_id,
                        'upload_date' => $formattedDate,
                        'created_by' => 1,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                }
                if (!empty($contentInsert)) {
                    $this->uploadCsquareService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for prescription.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCsqure()
    {
        try {
            Log::info('Migration started for Csqure.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','pharmacy_sales_reg_line_details')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('master_reporting')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('master_reporting')
                ->orderBy('reporting_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                foreach ($chunk_contents as $content) {
                    $master_id = DB::table('pharmacy_sales_reg_line_masters')->where('upload_date', $content->csv_upload_date)->first()->id;
                    // dd($master_id);
                    $timestamp = date('Y-m-d H:i:s',strtotime($content->csv_upload_date));
                    $contentInsert[] = [
                        'id' => $content->reporting_id,
                        'master_id' => $master_id ?? null,
                        'batch_no' => $content->batch_no,
                        'invoice_no' => $content->invoice_no,
                        'lc_code' => $content->lc_code,
                        'payment_mode' => $content->payment_mode,
                        'customer_registration_no' => $content->customer_registration_no,
                        'item_code' => $content->item_code,
                        'category_class' => $content->category_class,
                        'branch_code' => $content->branch_code,
                        'invoice_type' => $content->invoice_type,
                        'expiry' => $content->expiry,
                        'mf' => $content->mf,
                        'pk_qty' => $content->pk_qty,
                        'pur_rate' => $content->pur_rate,
                        'loose_eff_pur_rate' => $content->loose_eff_pur_rate,
                        'pack_eff_pur_rate' => $content->pack_eff_pur_rate,
                        'sale_rate' => $content->sale_rate,
                        'mrp' => $content->mrp,
                        'mrp_val' => $content->mrp_val,
                        'item_profit' => $content->item_profit,
                        'st_disc' => $content->st_disc,
                        'disc' => $content->disc,
                        'disc_amt' => $content->disc_amt,
                        'tax_percentage' => $content->tax_percentage,
                        'tax' => $content->tax,
                        'amount' => $content->amount,
                        'cust_code' => $content->cust_code,
                        'customer_name' => $content->customer_name,
                        'customer_contact' => $content->customer_contact,
                        'supplier_code' => $content->supplier_code,
                        'supplier_bill_no' => $content->supplier_bill_no,
                        'supplier_bill_date' => $content->supplier_bill_date,
                        'invoice_date' => $content->invoice_date,
                        'invoice_time' => $content->invoice_time,
                        'ref_invoice_no' => $content->ref_invoice_no,
                        'csv_upload_date' => $content->csv_upload_date,
                        'branch_sh_name' => $content->branch_sh_name,
                        'cgst_amount' => $content->cgst_amount,
                        'sgst_amount' => $content->sgst_amount,
                        'card_number' => $content->card_number,
                        'category_type' => $content->category_type,
                        'medicine_name' => $content->medicine_name,
                        'sms_coupon_flag' => $content->sms_coupon_flag,
                        'status' => 1,
                        'created_by' => 1,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                    if ($content->category_class != 'OTC' && $content->category_type != 'OTC' && $content->customer_contact != '' && $content->amount != '') {
                        $phone_no = $content->customer_contact;
                        $point = round((abs($content->amount)*2)/100, 2);
                        // // reward points debit
                        // if($content->invoice_type == 'L'){
                        //     $reward_cnt = DB::table('reward_points')->where([
                        //         'phone_no' => $phone_no,
                        //         'type' => 'csqarepharmacyreport',
                        //         'credit_debit' => 2,
                        //         'is_redeem' => 1,
                        //         'csqare_invNo' => $content->invoice_no
                        //     ])->count();
                        //     if ($reward_cnt == 0) {
                        //         $contentInsertChild[] = [
                        //             'phone_no' => $phone_no,
                        //             'date' => $timestamp,
                        //             'type' => 'csqarepharmacyreport',
                        //             'point' => $point*-1,
                        //             'credit_debit' => 2,
                        //             'is_redeem' => 1,
                        //             'csqare_invNo' => $content->invoice_no,
                        //             'status' => 1,
                        //             'created_by' => 1,
                        //             'created_at' => $timestamp,
                        //             'updated_at' => $timestamp
                        //         ];
                        //     }
                        // }
                        // // reward points credit
                        // elseif ($content->invoice_type == 'S'){
                        //     $reward_cnt = DB::table('reward_points')->where([
                        //         'phone_no' => $phone_no,
                        //         'type' => 'csqarepharmacyreport',
                        //         'credit_debit' => 1,
                        //         'is_redeem' => 1,
                        //         'csqare_invNo' => $content->invoice_no
                        //     ])->count();
                        //     if ($reward_cnt == 0) {
                        //         $contentInsertChild[] = [
                        //             'phone_no' => $phone_no,
                        //             'date' => $timestamp,
                        //             'type' => 'csqarepharmacyreport',
                        //             'point' => $point,
                        //             'credit_debit' => 1,
                        //             'is_redeem' => 1,
                        //             'csqare_invNo' => $content->invoice_no,
                        //             'status' => 1,
                        //             'created_by' => 1,
                        //             'created_at' => $timestamp,
                        //             'updated_at' => $timestamp
                        //         ];
                        //     }
                        // }
                    }
                    // dd($contentInsertChild);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        PharmacySalesRegLineDetail::upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'pharmacy_sales_reg_line_details')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for pharmacy_sales_reg_line_details.', ['count' => count($contentInsert)]);
                }
                // if (!empty($contentInsertChild)) {
                //     $this->rewardService->dataMigrate($contentInsertChild);
                //     Log::info('Chunk migrated successfully for pharmacy_sales_reg_line_details reward point.', ['count' => count($contentInsertChild)]);
                // }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateOrderMedicine()
    {
        try {
            Log::info('Migration started for order medicine.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','order_medicines')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('order_medicine')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 10000){
                $all_content = 10000;
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('order_medicine')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                $contentInsertReward = [];
                foreach ($chunk_contents as $content) {
                    $patient_phone = $this->patientService->getParentPhone($content->patient);
                    // dd($master_id);
                    if (in_array($content->data_source,[4,6])) {
                        $data_source = 1;
                    }
                    elseif (in_array($content->data_source,[2])) {
                        $data_source = 2;
                    }
                    else {
                        $data_source = 3;
                    }
                    // dd($data_source);
                    $timestamp = $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient_id' => $content->patient,
                        'patient_phone' => $patient_phone ?? null,
                        'type_of_collection' => $content->type_of_collection,
                        'clinic_id' => $content->clinic,
                        'building_no' => $content->building_no,
                        'full_address' => $content->full_address,
                        'landmark' => $content->landmark,
                        'city' => $content->city,
                        'pincode' => $content->pincode,
                        'prescription_upload' => $content->prescription_upload,
                        'data_source' => $data_source,
                        'source' => $content->source,
                        'remarks' => $content->remarks,
                        'latitude' => $content->latitude,
                        'longitude' => $content->longitude,
                        'total_amount' => $content->totalAmt,
                        'status' => $content->status,
                        'reject_remarks' => $content->reject_remarks,
                        'is_accept' => $content->is_accept,
                        'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                    if ($content->medicine_id) {
                        $medicine_ids = explode(',',$content->medicine_id);
                        $quantitys = $content->medicine_quantity != 0 ?explode(',',$content->medicine_quantity) : [];
                        foreach ($medicine_ids as $key => $medicine) {
                            $contentInsertChild[] = [
                                'order_id' => $content->id,
                                'medicine_id' => $medicine,
                                'quantity' => $quantitys[$key] ?? 1,
                                'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                                'created_at' => $timestamp,
                                'updated_at' => $timestamp
                            ];
                        }
                        // $check_reward = DB::table('reward_points')->where(['phone_no'=>$patient_phone,'type' => 'backendpharmacy'])->get();
                        // // reward points credit
                        // if ($patient_phone && strtotime($content->created_at) >= strtotime('2024-11-02') && count($check_reward) == 0) {
                        //     $contentInsertReward[] = [
                        //         'phone_no' => $patient_phone,
                        //         'date' => $timestamp,
                        //         'type' => 'backendpharmacy',
                        //         'point' => 30,
                        //         'credit_debit' => 1,
                        //         'is_redeem' => 1,
                        //         'bill_id' => $content->id,
                        //         'status' => 1,
                        //         'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                        //         'created_at' => $timestamp,
                        //         'updated_at' => $timestamp
                        //     ];
                        // }
                    }
                    // dd($contentInsertReward);
                }
                if (!empty($contentInsert)) {
                    $this->pharmacyService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for order medicine.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertChild)) {
                    $this->pharmacyChildService->dataMigrate($contentInsertChild);
                    Log::info('Chunk migrated successfully for order medicine child.', ['count' => count($contentInsert)]);
                }
                // if (!empty($contentInsertReward)) {
                //     $this->rewardService->dataMigrate($contentInsertReward);
                //     Log::info('Chunk migrated successfully for order medicine reward point.', ['count' => count($contentInsertReward)]);
                // }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBatchGenerator()
    {
        try {
            Log::info('Migration started for Diagnastic Batch Generator.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_batch_generators')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_batchGenerator')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_batchGenerator')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    if ($content->clinic_slug != '') {
                        $clinic_id = DB::table('clinics')
                            ->whereRaw('UPPER(SUBSTRING(clinic_name, 1, 4)) = ?', [strtoupper(substr($content->clinic_slug, 0, 4))])
                            ->value('id');
                        // dd($clinic_id);
                        $timestamp = date('Y-m-d H:i:s');
                        $contentInsert[] = [
                            'id' => $content->id,
                            'clinic_id' => $clinic_id ?? null,
                            'clinic_slug' => $content->clinic_slug,
                            'incrementor' => $content->incrementor,
                            'created_at' => $timestamp,
                            'updated_at' => $timestamp
                        ];
                    }
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('sample_batch_generators')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_batch_generators')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Batch Generator.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticHomecollectionCharge()
    {
        try {
            Log::info('Migration started for Diagnastic Home Collection Charge.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_homecollection_charges')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_homecollection_charges')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_homecollection_charges')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                        // dd($clinic_id);
                    $timestamp = date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'area_range' => $content->area_range,
                        'max_distance' => $content->max_distance,
                        'charges' => $content->charges,
                        'status' => $content->status,
                        'created_by' => 1,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        SampleHomecollectionCharge::upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_homecollection_charges')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Home Collection Charge.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticSampleType()
    {
        try {
            Log::info('Migration started for Diagnastic sample type.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_types')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_type')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_type')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                        // dd($clinic_id);
                    $timestamp = $content->created_at ? $content->created_at : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_name' => $content->sample_name,
                        'container_name' => $content->container_name,
                        'color' => $content->color,
                        'colorname' => $content->colorname,
                        'is_active' => $content->is_active,
                        'created_by' => $content->created_by,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('sample_types')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_types')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic sample type.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnastic()
    {
        try {
            Log::info('Migration started for Diagnastic.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collections')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $patient_phone = $this->patientService->getParentPhone($content->patient);
                    $doctor_dtl = $this->dbConnectionInstance->table('doctor')->where('id', $content->doctor_id)->select('ion_user_id','status')->first();
                    $ion_user_id = $doctor_dtl->ion_user_id ?? null;
                    if ($ion_user_id == 0) {
                        $ion_user_id = DB::table('doctors')->where('id', $content->doctor_id)->value('user_id') ?? null;
                    }
                    // dd($content->id,$ion_user_id);
                    $doctortype = $doctor_dtl->status ?? 0;
                    $phlebo_user_id = $this->dbConnectionInstance->table('phlebotomist')->where('id', $content->phlebo_id)->value('ion_user_id') ?? null;
                    $timestamp = $content->date_time_of_entry ? $content->date_time_of_entry : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient_id' => $content->patient,
                        'patient_phone' => $patient_phone,
                        'test_id' => $content->test_id,
                        'date_of_collection' => $content->date_of_collection,
                        'type_of_collection' => $content->type_of_collection,
                        'appointment_type' => $content->appointment_type,
                        'clinic_id' => $content->clinic,
                        'clinic_assign' => $content->clinic_assign,
                        'building_no' => $content->building_no,
                        'full_address' => $content->full_address,
                        'landmark' => $content->landmark,
                        'city' => $content->city,
                        'pincode' => $content->pincode,
                        'phlebo_assign_status' => $content->phlebo_assign_status,
                        'prev_assignment_history' => $content->prev_assignment_history,
                        'phlebo_id' => $phlebo_user_id,
                        'prescription_upload' => $content->prescription_upload,
                        'unique_bill_id' => $content->unique_bill_id,
                        'unique_queue_number' => $content->unique_queue_number,
                        'data_source' => $content->data_source,
                        'source' => $content->source,
                        'coupon_code' => $content->coupon_code,
                        'coupon_status' => $content->coupon_status,
                        'date_of_extends' => $content->date_of_extends,
                        'unique_id' => $content->unique_id,
                        'doctortype' => $doctortype == 2 ? 2 : 1,
                        'doctor_id' => $ion_user_id,
                        'doctor_name' => $content->doctor_name,
                        'remarks' => $content->remarks,
                        'latitude' => $content->latitude,
                        'longitude' => $content->longitude,
                        'offered_type' => $content->offered_type,
                        'offered_id' => $content->offered_id,
                        'is_homecollection' => $content->is_homecollection,
                        'hc_quantity_arearange' => $content->hc_quantity_arearange,
                        'patient_esign' => $content->patient_esign,
                        'fully_paid' => $content->fully_paid,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'modified_by' => $content->created_by,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic.', ['count' => count($contentInsert)]);
                }
                // private $diagnosticService;
                // private $diagnosticBreakupTestService;
                // private $diagnosticBreakupBillService;
                // private $diagnosticPhleboAssignmentService;
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupTest()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Test.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_tests')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_breakup_test')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_breakup_test')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_collection_id' => $content->sample_collection_id,
                        'unique_id' => $content->unique_id,
                        'package_id' => $content->package_id,
                        'test_id' => $content->test_id,
                        'itdose_testid' => $content->itdose_testid,
                        'test_code' => $content->test_code,
                        'sample_type' => $content->sample_type,
                        'sin_no' => $content->sin_no,
                        'vial_qty' => $content->vial_qty,
                        'transferred_to' => $content->transferred_to,
                        'is_urgent' => $content->is_urgent,
                        'fieldboy' => $content->fieldboy,
                        'courier_details' => $content->courier_details,
                        'docketno' => $content->docketno,
                        'batchno' => $content->batchno,
                        'sin_created_by' => $content->sin_created_by,
                        'sin_created_on' => $content->sin_created_on,
                        'is_centrifuge' => $content->is_centrifuge,
                        'segregation_created_by' => $content->segregation_created_by,
                        'segregation_created_on' => $content->segregation_created_on,
                        'transfer_by' => $content->transfer_by,
                        'transfer_on' => $content->transfer_on,
                        'sample_reject_status' => $content->sample_reject_status,
                        // 'test_delete_status' => $content->test_delete_status,
                        'report_delivery_date' => $content->report_delivery_date,
                        'reportGenerated' => $content->reportGenerated,
                        'itdose_test_status' => $content->itdose_test_status,
                        'status' => $content->sample_status,
                        'created_by' => $content->created_by,
                        'modified_by' => $content->modified_by,
                        'deleted_by' => $content->test_delete_status == 2 ? 1 : null,
                        'created_at' => $content->created_on ? $content->created_on : date('Y-m-d H:i:s'),
                        'updated_at' => $content->modified_on ? $content->modified_on : date('Y-m-d H:i:s'),
                        'deleted_at' => $content->test_delete_status == 2 ? date('Y-m-d H:i:s') : null
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticBreakupTestService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Test.', ['count' => count($contentInsert)]);
                }
                // private $diagnosticService;
                // private $diagnosticBreakupTestService;
                // private $diagnosticBreakupBillService;
                // private $diagnosticPhleboAssignmentService;
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupBill()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Bill.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_bills')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_breakup_bill')
            ->select('collection_id')
            ->groupBy('collection_id')
            ->get();
            $all_content = count($all_content);
            // dd(count($all_content),$all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_breakup_bill')
                ->select('collection_id')
                ->groupBy('collection_id')
                ->orderBy('collection_id')
                ->offset($startId)
                ->limit( $all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($content);
                    $bill_detals = $this->dbConnectionInstance->table('sample_collection_breakup_bill')
                        ->join('sample_collection', 'sample_collection.id', '=', 'sample_collection_breakup_bill.collection_id')
                        ->select('sample_collection_breakup_bill.*','sample_collection.date_time_of_entry','sample_collection.created_by')
                        ->where('sample_collection_breakup_bill.collection_id', $content->collection_id)
                        ->orderBy('sample_collection_breakup_bill.id')
                        ->get();
                    $home_collection_bill = $this->dbConnectionInstance->table('payment')
                        ->join('sample_collection', 'sample_collection.id', '=', 'payment.sample_collection_id')
                        ->where('sample_collection_id', $content->collection_id)
                        ->select(
                            'sample_collection.is_homecollection','sample_collection.hc_quantity_arearange',
                            DB::raw("SUBSTRING_INDEX(sample_collection.hc_quantity_arearange, ',', 1) as km_range"),
                            DB::raw("SUBSTRING_INDEX(sample_collection.hc_quantity_arearange, ',', -1) as km_quantity"),
                            'payment.home_collection_charges','sample_collection.created_by','sample_collection.date_time_of_entry'
                        )
                        ->first();
                        // dd($bill_detals,$home_collection_bill);
                    foreach ($bill_detals as $bill) {
                        $contentInsert[] = [
                            'sample_collection_id' => $content->collection_id,
                            'item_id' => $bill->item_id,
                            'home_collection_id' => null,
                            'amount' => $bill->amount,
                            'discount' => $bill->discount,
                            'net_amount' => $bill->net_amount,
                            'status' => 0,
                            'created_by' => $bill->created_by,
                            'deleted_by' => $bill->test_delete_status == 2 ? 1 : null,
                            'created_at' => $bill->date_time_of_entry ? $bill->date_time_of_entry : date('Y-m-d H:i:s'),
                            'updated_at' => $bill->date_time_of_entry ? $bill->date_time_of_entry : date('Y-m-d H:i:s'),
                            'deleted_at' => $bill->test_delete_status == 2 ? date('Y-m-d H:i:s') : null
                        ];
                    }
                    if ($home_collection_bill && $home_collection_bill->is_homecollection == 2) {
                        $home_collection = DB::table('sample_homecollection_charges')->where('area_range',$home_collection_bill->km_range)->select('id','charges')->first();
                        $amount = ($home_collection->charges ?? 0) * intval($home_collection_bill->km_quantity);
                        $discount = $amount > 0 ? $amount - $home_collection_bill->home_collection_charges : 0;
                        $contentInsert[] = [
                            'sample_collection_id' => $content->collection_id,
                            'item_id' => null,
                            'home_collection_id' => $home_collection->id ?? null,
                            'amount' => $amount,
                            'discount' => $discount,
                            'net_amount' => $home_collection_bill->home_collection_charges,
                            'status' => 0,
                            'created_by' => $home_collection_bill->created_by,
                            'deleted_by' => null,
                            'created_at' => $home_collection_bill->date_time_of_entry ? $home_collection_bill->date_time_of_entry : date('Y-m-d H:i:s'),
                            'updated_at' => $home_collection_bill->date_time_of_entry ? $home_collection_bill->date_time_of_entry : date('Y-m-d H:i:s'),
                            'deleted_at' => null
                        ];
                    }
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticBreakupBillService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Bill.', ['count' => count($contentInsert)]);
                }
                // private $diagnosticService;
                // private $diagnosticBreakupTestService;
                // private $diagnosticBreakupBillService;
                // private $diagnosticPhleboAssignmentService;
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticPayment()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','diagnastic_payment')->select('count','limit')->first();
        //SELECT * FROM payment WHERE sample_collection_id ='45957' 
        // AND (category_name != 'doctor_consultanat' OR category_name IS NULL) AND STATUS = 'refund'
        $all_content = $this->dbConnectionInstance->table('payment as p')
            ->join('sample_collection as s', 'p.sample_collection_id', '=', 's.id')
            // ->select('p.sample_collection_id')
            ->where(function ($query) {
                $query->where('category_name', '!=', 'doctor_consultanat')
                    ->orWhereNull('category_name');
            })
            ->orderBy('p.sample_collection_id')
            ->count();
        // $all_content = count($all_content);
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);
        // dd($chunkSize,$increment_migration->count);
        // for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->count * $chunkSize;
            if ($startId > $all_content) {
                dd("completed migration");
            }
            // dd($startId);
            $this->queryBuilder = $this->dbConnectionInstance->table('payment as p')
                ->join('sample_collection as s', 'p.sample_collection_id', '=', 's.id')
                ->select('s.id as sample_id','s.unique_bill_id','s.patient as patient_id','s.unique_id as workorder_id', 'p.*')
                // ->whereNotNull('s.unique_id')
                ->where(function ($query) {
                    $query->where('category_name', '!=', 'doctor_consultanat')
                        ->orWhereNull('category_name');
                })
                ->orderBy('p.sample_collection_id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                foreach ($chunk_contents as $content) {
                    // dd($content);
                    $service_id = $content->sample_id;
                    $phone = $this->patientService->getParentPhone($content->patient_id);
                    // payment bill create
                    $paymentData = [
                        'payment_dtl' => $content,
                    ];
                    // dd($paymentData);
                    $bill_id = $content->unique_bill_id ?? null;
                    $pay_type = config('billing.types.2');
                    // payment here
                    $this->paymentDiagnasticWithReward($paymentData,$bill_id,$pay_type,$service_id,$content->patient_id,$phone);
                }
                DB::table('temp_increment_migrations')
                    ->where('table_name', 'diagnastic_payment')
                    ->increment('count');
            }
        // }
        
        dd("Loop iteration completed");
        // return back();
    }
    private function paymentDiagnasticWithReward($paymentData,$bill_id,$pay_type,$service_id,$patient_id,$phone)
    {
        $breakup_bill = $this->dbConnectionInstance->table('sample_collection_breakup_bill')
            ->where('collection_id', $service_id)
            ->where('test_delete_status',1)
            ->get();
        // dd($breakup_bill,$service_id);
        $reward_redeem = $paymentData['payment_dtl']->redeem_points;
        $bill_amount = $breakup_bill->sum('amount') ?? 0;
        $discount = $breakup_bill->sum('discount') ?? 0;
        $paid_amount = $breakup_bill->sum('net_amount') ?? 0;
        $due_amount = $bill_amount - $discount - $paid_amount;
        $due_amount = $due_amount < 0 ? 0 : $due_amount;
        // dd($paid_amount,$due_amount);
        $data = [
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'discount' => $discount,
            'total_amount' => $paid_amount,
            'paid_amount' => intval($paid_amount),
            'due_amount' => intval($due_amount),
            'membership_registration_no' => $paymentData['payment_dtl']->arrogya_membership_no ?? null,
            'created_by' => $paymentData['payment_dtl']->created_by,
            'created_at' => $paymentData['payment_dtl']->created_at,
            'updated_at' => $paymentData['payment_dtl']->created_at
        ];
        // dd($paymentData,$breakup_bill,$data);
        $payment_bill_id = DB::table('payment_bill_master')->where(['type' => $pay_type,'service_id' => $service_id])->value('id');
        $this->paymentBillService->setRequest($data);
        if($payment_bill_id){
            $this->paymentBillService->findById($payment_bill_id);
            $paymentBill = $this->paymentBillService->update();
        }
        else {
            $paymentBill = $this->paymentBillService->add();
        }
        if($paymentData['payment_dtl']->workorder_id == null){
            $amount = array_sum(explode(',',$paymentData['payment_dtl']->breakup_payment_amount));
        }
        else{
            $amount = $paymentData['payment_dtl']->breakuptotalamount;
        }
        // dd($amount);
        $data_payment = [
            'bill_id' => $paymentBill->id,
            'date' => date('Y-m-d',strtotime($paymentData['payment_dtl']->created_at)),
            'recpit_no' => $this->paymentService->recpitIncrementId('myMD','DG-receipt',8),
            'status' => $paymentData['payment_dtl']->status == 'refund' ? 'Refund' : 'Paid',
            'amount' => $amount,
            'redeem_points' => $paymentData['payment_dtl']->redeem_points,
            'openning_points' => $paymentData['payment_dtl']->openning_points,
            'closing_points' => $paymentData['payment_dtl']->closing_points,
            'created_by' => $paymentData['payment_dtl']->created_by,
            'created_at' => $paymentData['payment_dtl']->created_at,
            'updated_at' => $paymentData['payment_dtl']->created_at
        ];
        $payment_id = DB::table('payments')->where(Arr::except($data_payment, ['recpit_no','openning_points','closing_points','updated_at']))->value('id');
        // dd($payment_id,Arr::except($data_payment, ['recpit_no','created_by','created_at','updated_at']));
        $this->paymentService->setRequest($data_payment);
        if($payment_id){
            $this->paymentService->findById($payment_id);
            $payment = $this->paymentService->update();
            DB::table('payment_details')->where(['payment_id' => $payment_id])->delete();
        }
        else {
            $payment = $this->paymentService->add();
        }
        $total_amount = 0;
        $total_discount = 0;
        // dd($payment);
        if ($paymentData['payment_dtl']->payment_mode != '') {
            // DB::table('payment_details')->where(['payment_id' => $payment->id])->delete();
            
            $payment_modes = explode(',',$paymentData['payment_dtl']->payment_mode);
            $amounts = explode(',',$paymentData['payment_dtl']->breakup_payment_amount);
            $payment_details_arr = explode(',',$paymentData['payment_dtl']->payment_details);
            // dd($payment_modes,$amounts,$payment_details_arr);
            foreach ($payment_modes as $key => $row_mode) {
                switch ($row_mode) {
                    case 'credit_card':
                        $payment_details = $payment_details_arr[$key-1] ?? '';
                        break;
                    case 'debit_card':
                        $payment_details = $payment_details_arr[$key-1] ?? '';
                        break;
                    case 'upi':
                        $payment_details = ($payment_details_arr[$key-1] ?? '').' '.($payment_details_arr[$key] ?? '');
                        break;
                    default:
                        $payment_details = null;
                        break;
                }
                $data_payment_dtl = [
                    'created_by' => $paymentData['payment_dtl']->created_by,
                    'created_at' => $paymentData['payment_dtl']->created_at,
                    'updated_at' => $paymentData['payment_dtl']->created_at
                ];
                $data_payment_dtl['payment_id'] = $payment->id;
                $data_payment_dtl['payment_mode'] = $row_mode == 'reward_points' ? 'refund_reward_points' : $row_mode;
                $data_payment_dtl['amount'] = intval($amounts[$key] ?? 0);
                $data_payment_dtl['payment_details'] = $payment_details;
                $total_amount +=intval($amounts[$key] ?? 0);
                // dd($data_payment_dtl);
                $this->paymentDetailService->setRequest($data_payment_dtl);
                $this->paymentDetailService->add();
                // dd($this->paymentDetailService);
            }
        }
        // dd($paymentData,$breakup_bill,$payment);
        return true;
    }
    public function dataMigrateDiagnasticPhleboAssignment()
    {
        try {
            Log::info('Migration started for Diagnastic Phlebo Assignment.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','phlebo_assignments')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('phlebo_assignment')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('phlebo_assignment')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $role_id = null;
                    $user_id = null;
                    if($content->role == 1){
                        $role_id = 7;
                        $user_id = $this->dbConnectionInstance->table('phlebotomist')->where('id', $content->phlebo_id)->value('ion_user_id') ?? null;
                    }
                    elseif ($content->role == 2) {
                        $role_id = 6;
                        $user_id = $this->dbConnectionInstance->table('nurse')->where('id', $content->phlebo_id)->value('ion_user_id') ?? null;
                    }
                    
                    // dd($clinic_id);
                    $timestamp = $content->assign_date_time ? $content->assign_date_time : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'sample_id' => $content->sample_id,
                        'role_id' => $role_id,
                        'phlebo_id' => $user_id,
                        'schedule_time' => $content->schedule_time,
                        'remarks' => $content->remarks,
                        'test_count' => $content->test_count,
                        'collected_testcount' => $content->collected_testcount,
                        'handover_testcount' => $content->handover_testcount,
                        'actual_date_time_of_collection' => $content->actual_date_time_of_collection,
                        'temp_of_bag_at_collection' => $content->temp_of_bag_at_collection,
                        'payment_status' => $content->payment_status,
                        'amount' => $content->amount,
                        'mode_of_payment' => $content->mode_of_payment,
                        'collection_status' => $content->collection_status,
                        'date_time_at_handover' => $content->date_time_at_handover,
                        'temp_of_bag_at_handover' => $content->temp_of_bag_at_handover,
                        'amount_at_handover' => $content->amount_at_handover,
                        'handover_status' => empty($content->handover_status) ? '4' : $content->handover_status,
                        'collected_by' => $content->collected_by,
                        'created_by' => $content->assign_by,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticPhleboAssignmentService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Phlebo Assignment.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticApiLog()
    {
        try {
            Log::info('Migration started for Diagnastic Api Log.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_api_logs')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_api_error_log')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_api_error_log')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $timestamp = $content->created_at ? $content->created_at : date('Y-m-d H:i:s');
                    $contentInsert[] = [
                        'id' => $content->id,
                        'request_id' => $content->request_id,
                        'work_order_id' => $content->workorderid,
                        'api_link' => $content->api_link,
                        'playload_json' => $content->jsondata,
                        'response_json' => $content->return_jsondata,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticApiService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Api Log.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticRecollection()
    {
        try {
            Log::info('Migration started for Diagnastic Recollection.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_barcode_resubmissions')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_barcode_resubmission')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_barcode_resubmission')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'work_order_id' => $content->workorderid,
                        'breakup_test_id' => $content->breakuptestid,
                        'test_id' => $content->test_id,
                        'itdose_test_id' => $content->itdose_testid,
                        'sample_type' => $content->sample_type,
                        'sin_no' => $content->sin_no,
                        'status' => $content->status,
                        'created_by' => $content->request_by,
                        'modified_by' => $content->updated_by,
                        'created_at' => $content->request_at ? $content->request_at : date('Y-m-d H:i:s'),
                        'updated_at' => $content->updated_at ? $content->updated_at : date('Y-m-d H:i:s')
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->diagnosticBarcodeResubmissionService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Recollection.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticBreakupTestItdose()
    {
        try {
            Log::info('Migration started for Diagnastic Breakup Test Itdose.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_breakup_test_itdoses')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_api_status')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_api_status')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    $breakup_test_id = DB::table('sample_collection_breakup_tests')
                        ->where('unique_id',$content->workorder_id)
                        ->where('itdose_testid',$content->itdose_test_id)
                        ->where('test_code',$content->test_code)
                        ->first()->id ?? null;
                    // dd($breakup_test_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'breakup_test_id' => $breakup_test_id,
                        'workorder_id' => $content->workorder_id,
                        'itdose_test_id' => $content->itdose_test_id,
                        'test_code' => $content->test_code,
                        'status' => $content->status,
                        'created_by' => 1,
                        'created_at' => $content->created_on ? $content->created_on : date('Y-m-d H:i:s'),
                        'updated_at' => $content->created_on ? $content->created_on : date('Y-m-d H:i:s')
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        SampleCollectionBreakupTestItdose::upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'sample_collection_breakup_test_itdoses')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for Diagnastic Breakup Test Itdose.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateDiagnasticEstimation()
    {
        try {
            Log::info('Migration started for Diagnastic Estimation.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','sample_collection_estimations')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('sample_collection_estimation')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('sample_collection_estimation')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // dd($clinic_id);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'patient_id' => $content->patient_id,
                        'full_address' => $content->full_address,
                        'land_mark' => $content->land_mark,
                        'city' => $content->city,
                        'pincode' => $content->pincode,
                        'test_id' => $content->test_id,
                        'collection_id' => $content->collection_id,
                        'clinic_id' => $content->clinic_id,
                        'sub_total' => $content->subtotal,
                        'discount' => $content->discount > 0 ? $content->discount : null,
                        'gross_total' => $content->grosstotal,
                        'status' => $content->status,
                        'created_by' => $content->created_by,
                        'created_at' => $content->created_on ? $content->created_on : date('Y-m-d H:i:s'),
                        'updated_at' => $content->created_on ? $content->created_on : date('Y-m-d H:i:s')
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->estimationService->dataMigrate($contentInsert);
                    Log::info('Chunk migrated successfully for Diagnastic Estimation.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateCampaign()
    {
        try {
            Log::info('Migration started for order medicine.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','campaign_masters')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('campaign_master')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('campaign_master')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            if (count($chunk) == 0) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                $contentInsertChild = [];
                // dd($chunk_contents);
                foreach ($chunk_contents as $content) {
                    //c "Diagnosis Campaign"=>1, "Medicine Campaign"=>2, "none"=>3
                    //l "Diagnosis Campaign"=>1, "Medicine Campaign"=>2, "Diagnostic & Medicine (Both)"=>3, "OTC Product Campaign"=> 4
                    //c "default"=>0 ,"Self Care Product"=>1, "OTC Product"=>2, "Medicine"=>3
                    //l "default"=>0, "Self Care Product"=>1, "OTC Food Product"=>2
                    if ($content->status == 2) {//0:inactive,1:active   active=>1, inactive=>2
                        $status = 0;
                    }
                    else {
                        $status = 1;
                    }
                    $campaign_type = 0;
                    $medicine_type = 0;
                    $medicines = null;
                    $medicine_discount_type = 0;
                    $medicine_discount = 0;
                    $medicine_discount_for = 0;
                    $self_otc_products = null;
                    $self_otc_discount_type = 0;
                    $self_otc_discount = 0;
                    $self_otc_discount_for = 0;
                    $food_otc_products = null;
                    $food_otc_discount_type = 0;
                    $food_otc_discount = 0;
                    $food_otc_discount_for = 0;
                    switch ($content->campaign_type) {
                        case 1:
                            $campaign_type = 1;
                            if($content->diagnostic_uses == 2 || $content->diagnostic_uses == 0){//only test
                                $test_ids = $content->diagnostic_test == null ? '["all"]' : $content->diagnostic_test;
                                $child_id = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',1)->where('test_ids',$test_ids)->value('id') ?? null;
                                $contentInsertChild[] = [
                                    'id' => $child_id,
                                    'campaign_id' => $content->id,
                                    'test_type' => 1,
                                    'test_ids' => $test_ids,
                                    'discount_type' => $content->discount_type,
                                    'discount' => $content->discount,
                                    'discount_for' => $content->test_individual,
                                    'status' => $status,
                                    'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                                    'modified_by' => $content->updated_by != 0 ? $content->updated_by : 1,
                                    'created_at' => $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s'),
                                    'updated_at' => $content->updated_at ? date('Y-m-d H:i:s',strtotime($content->updated_at)) : date('Y-m-d H:i:s')
                                ];
                            }
                            elseif ($content->diagnostic_uses == 3) {//only package
                                $test_ids = $content->diagnostic_package == null ? '["all"]' : $content->diagnostic_package;
                                $child_id = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',2)->where('test_ids',$test_ids)->value('id') ?? null;
                                $contentInsertChild[] = [
                                    'id' => $child_id,
                                    'campaign_id' => $content->id,
                                    'test_type' => 2,
                                    'test_ids' => $test_ids,
                                    'discount_type' => $content->package_discount_type,
                                    'discount' => $content->package_discount,
                                    'discount_for' => $content->package_individual,
                                    'status' => $status,
                                    'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                                    'modified_by' => $content->updated_by != 0 ? $content->updated_by : 1,
                                    'created_at' => $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s'),
                                    'updated_at' => $content->updated_at ? date('Y-m-d H:i:s',strtotime($content->updated_at)) : date('Y-m-d H:i:s')
                                ];
                            }
                            elseif ($content->diagnostic_uses == 1) {//test & package
                                $test_ids = $content->diagnostic_test == null ? '["all"]' : $content->diagnostic_test;
                                $child_id = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',1)->where('test_ids',$test_ids)->value('id') ?? null;
                                $contentInsertChild[] = [
                                    'id' => $child_id,
                                    'campaign_id' => $content->id,
                                    'test_type' => 1,
                                    'test_ids' => $test_ids,
                                    'discount_type' => $content->discount_type,
                                    'discount' => $content->discount,
                                    'discount_for' => $content->test_individual,
                                    'status' => $status,
                                    'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                                    'modified_by' => $content->updated_by != 0 ? $content->updated_by : 1,
                                    'created_at' => $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s'),
                                    'updated_at' => $content->updated_at ? date('Y-m-d H:i:s',strtotime($content->updated_at)) : date('Y-m-d H:i:s')
                                ];
                                $test_ids = $content->diagnostic_package == null ? '["all"]' : $content->diagnostic_package;
                                $child_id = DB::table('campaign_test_details')->where('campaign_id',$content->id)->where('test_type',2)->where('test_ids',$test_ids)->value('id') ?? null;
                                $contentInsertChild[] = [
                                    'id' => $child_id,
                                    'campaign_id' => $content->id,
                                    'test_type' => 2,
                                    'test_ids' => $test_ids,
                                    'discount_type' => $content->package_discount_type,
                                    'discount' => $content->package_discount,
                                    'discount_for' => $content->package_individual,
                                    'status' => $status,
                                    'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                                    'modified_by' => $content->updated_by != 0 ? $content->updated_by : 1,
                                    'created_at' => $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s'),
                                    'updated_at' => $content->updated_at ? date('Y-m-d H:i:s',strtotime($content->updated_at)) : date('Y-m-d H:i:s')
                                ];
                            }
                            // test
                            break;
                        case 2:
                            $campaign_type = 2;
                            break;
                        default:
                            $campaign_type = 0;
                            break;
                    }
                    switch ($content->medicine_type) {
                        case 3:
                            $medicine_type = 0;
                            $medicines = '["all"]';
                            $medicine_discount_type = $content->discount_type;
                            $medicine_discount = $content->discount;
                            $medicine_discount_for = 1;
                            break;
                        case 1:
                            $campaign_type = 4;
                            $medicine_type = 1;
                            $self_otc_products = '["all"]';
                            $self_otc_discount_type = $content->discount_type;
                            $self_otc_discount = $content->discount;
                            $self_otc_discount_for = 1;
                            break;
                        case 2:
                            $campaign_type = 4;
                            $medicine_type = 2;
                            $food_otc_products = '["all"]';
                            $food_otc_discount_type = $content->discount_type;
                            $food_otc_discount = $content->discount;
                            $food_otc_discount_for = 1;
                            break;
                        default:
                            $medicine_type = $content->medicine_type;
                            break;
                    }
                    
                    // dd($content);
                    $contentInsert[] = [
                        'id' => $content->id,
                        'campaign_name' => $content->campaign_name,
                        'campaign_type' => $campaign_type,
                        'url_slag' => $content->url_slag,
                        'image_path' => $content->image_path,
                        'applicable_for' => $content->applicable_for,
                        'start_date' => empty($content->start_date) ? $content->date_of_expery : $content->start_date,
                        'end_date' => $content->date_of_expery,
                        'is_for_coupon' => $content->is_for_coupon,
                        'coupon_expeiry_date' => $content->coupon_expeiry_date,
                        'clinic' => $content->clinic == null ? '["all"]' : $content->clinic,
                        'medicine_type' => $medicine_type,
                        'medicines' => $medicines,
                        'medicine_discount_type' => $medicine_discount_type,
                        'medicine_discount' => $medicine_discount,
                        'medicine_discount_for' => $medicine_discount_for,
                        'self_otc_products' => $self_otc_products,
                        'self_otc_discount_type' => $self_otc_discount_type,
                        'self_otc_discount' => $self_otc_discount,
                        'self_otc_discount_for' => $self_otc_discount_for,
                        'food_otc_products' => $food_otc_products,
                        'food_otc_discount_type' => $food_otc_discount_type,
                        'food_otc_discount' => $food_otc_discount,
                        'food_otc_discount_for' => $food_otc_discount_for,
                        'is_hc_free' => $content->is_hc_free,
                        'min_limit' => $content->min_limit,
                        'status' => $status,
                        'created_by' => $content->created_by != 0 ? $content->created_by : 1,
                        'modified_by' => $content->updated_by != 0 ? $content->updated_by : 1,
                        'created_at' => $content->created_at ? date('Y-m-d H:i:s',strtotime($content->created_at)) : date('Y-m-d H:i:s'),
                        'updated_at' => $content->updated_at ? date('Y-m-d H:i:s',strtotime($content->updated_at)) : date('Y-m-d H:i:s')
                    ];
                    // dd($contentInsert,$contentInsertChild);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('campaign_masters')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'campaign_masters')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for campaign_masters.', ['count' => count($contentInsert)]);
                }
                if (!empty($contentInsertChild)) {
                    DB::transaction(function () use ($contentInsertChild) {
                        $columnsToUpdate = array_diff(array_keys($contentInsertChild[0]), ['id']);
                        DB::table('campaign_test_details')->upsert(
                            $contentInsertChild,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                    });
                    Log::info('Chunk migrated successfully for campaign_test_details.', ['count' => count($contentInsertChild)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
    public function dataMigrateRewardPoints()
    {
        try {
            Log::info('Migration started for reward points.');
            $increment_migration = DB::table('temp_increment_migrations')->where('table_name','reward_points')->select('count','limit')->first();
            $all_content = $this->dbConnectionInstance->table('reward_points')
            ->count();
            // dd($all_content);
            $chunkSize = $increment_migration->limit;
            $chunkCount = ceil($all_content / $chunkSize);

            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            // dd($startId,$all_content);
            if ($startId > $all_content) {
                // dd("completed migration");
                Log::info('completed migration');
                dd("completed migration");
            }
            if(($all_content - $startId) > 30000){
                $all_content = 30000;
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('reward_points')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            // dd($chunk);
            foreach ($chunk as $chunk_contents) {
                $contentInsert = [];
                foreach ($chunk_contents as $content) {
                    // "Default"=>0,
                    // 1:opd,2:diagnostic,3:membership,4:ABsmartcard,5:diagnostic_it_dose,6:diagnostic_manual_entry,7:csqarepharmacyreport,
                    // "Backend Pharmacy"=>8,9=>diagnostic dispute test,10=>froentend diagnostic
                    switch($content->type){
                        case 1:
                            $type = 'OPD';
                            break;
                        case 2:
                            $type = 'DG';
                            break;
                        case 3:
                            $type = 'MB';
                            break;
                        case 4:
                            $type = 'MB-CARD';
                            break;
                        case 5:
                            $type = 'diagnostic_it_dose';
                            break;
                        case 6:
                            $type = 'diagnostic_manual_entry';
                            break;
                        case 7:
                            $type = 'csqarepharmacyreport';
                            break;
                        case 8:
                            $type = 'backendpharmacy';
                            break;
                        case 9:
                            $type = 'diagnostic_dispute_test';
                            break;
                        case 10:
                            $type = 'frontend_diagnostic';
                            break;
                        default:
                            $type = null;
                            break;
                    }
                    $contentInsert[] = [
                        'id' => $content->id,
                        'phone_no' => $content->phone_no,
                        'date' => $content->date,
                        'type' => $type,
                        'point' => $content->point,
                        'credit_debit' => $content->credit_debit,
                        'is_redeem' => $content->is_redeem,
                        'bill_id' => $content->bill_id,
                        'it_dose_transtaction_id' => $content->it_dose_transtaction_id,
                        'csqare_invNo' => $content->csqare_invNo,
                        // 'expiry' => $content->expiry,
                        'status' => $content->status,//1:perfect,2:dispute,3:expired
                        'created_by' => 1,
                        'created_at' => date('Y-m-d H:i:s', strtotime($content->date)),
                        'updated_at' => date('Y-m-d H:i:s', strtotime($content->date))
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    DB::transaction(function () use ($contentInsert) {
                        $columnsToUpdate = array_diff(array_keys($contentInsert[0]), ['id']);
                        DB::table('reward_points')->upsert(
                            $contentInsert,
                            ['id'], // Unique columns to check for duplicates
                            $columnsToUpdate // Columns to update if a duplicate is found
                        );
                        DB::table('temp_increment_migrations')
                            ->where('table_name', 'reward_points')
                            ->increment('count');
                    });
                    Log::info('Chunk migrated successfully for reward points.', ['count' => count($contentInsert)]);
                }
            }
            Log::info('Migration completed successfully.');
            dd("Loop iteration completed");
        } catch (\Exception $e) {
            Log::error('Error during migration: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            dd('Error during migration: ' . $e->getMessage());
        }
    }
}
