<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;


class SampleReportExport implements FromView, ShouldAutoSize


{
    protected $data;
  
    protected $visit_type;
  
    // Accept data via constructor
    public function __construct($data,$visit_type)
    {
        // dd($data,'hii');
        $this->data = $data;
    
        $this->visit_type = $visit_type;
     
        
    }
    public function view(): View
    {
        $data = $this->data;
     
        $visit_type = $this->visit_type;
      


        //dd($data);
        return view('report::samplecollection.export.samplecollectionexport', compact('data','visit_type'));
    }
}
