<?php

namespace Modules\Settings\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReviewSurveyRequest extends FormRequest
{
    protected $id;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $this->id = $this->route('id') ? $this->route('id') : '';
        return [
            'language' => 'required',
            'survey_title' => 'required',
            'survey_settings_json' => 'required'
        ];
    }
    public function messages()
    {
        return [
            'language.required' => 'Language is required',
            'survey_title.required' => 'Survey title is required',
            'survey_settings_json.required' => 'Please add at least one question to the survey',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
