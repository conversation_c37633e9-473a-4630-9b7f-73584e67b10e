<?php

namespace Modules\Diagnostic\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Diagnostic\Http\Requests\SampleCollectionRequest;
use Modules\Diagnostic\Http\Requests\SampleReCollectionRequest;
use Modules\Diagnostic\Http\Requests\SampleCollectionBarcodeEditRequest;
use Modules\Diagnostic\Http\Requests\AssignPhleboRequest;
use Modules\Diagnostic\Http\Requests\LabStatusItdoseRequest;
use Modules\Diagnostic\Services\DiagnosticService;
use Modules\Diagnostic\Services\DiagnosticTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupBillService;
use Modules\Diagnostic\Services\DiagnosticPhleboAssignmentService;
use Modules\Doctor\Services\DoctorService;
use Modules\Patient\Services\PatientService;
use Modules\Reward\Services\RewardService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Users\Services\UserService;
use Modules\Diagnostic\Services\DiagnosticApiService;
use Modules\Diagnostic\Services\DiagnosticBarcodeResubmissionService;
use App\Services\SMSMsgService;
use App\Services\ImageUploadService;
use Modules\Diagnostic\Jobs\DiagnosticApiJob;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Modules\Diagnostic\Models\SampleCollectionBreakupTestItdose;
use Modules\Diagnostic\Models\SampleCollectionApiLog;
use Modules\Diagnostic\Models\SampleCollectionBarcodeResubmission;
use DB;
use PDF;

class SampleCollectionController extends Controller
{
    private $diagnosticService;
    private $diagnosticTestService;
    private $diagnosticBreakupTestService;
    private $diagnosticBreakupBillService;
    private $patientService;
    private $smsMsgService;
    private $imageUploadService;
    private $rewardService;
    private $paymentService;
    private $paymentBillService;
    private $paymentDetailService;
    private $doctorService;
    private $userService;
    private $diagnosticPhleboAssignmentService;
    private $diagnosticApiService;
    private $diagnosticBarcodeResubmissionService;
    

    public function __construct(DiagnosticService $diagnosticService, DiagnosticTestService $diagnosticTestService, PatientService $patientService, SMSMsgService $smsMsgService, ImageUploadService $imageUploadService, RewardService $rewardService, DiagnosticBreakupTestService $diagnosticBreakupTestService, DiagnosticBreakupBillService $diagnosticBreakupBillService, PaymentBillService $paymentBillService, PaymentService $paymentService, PaymentDetailService $paymentDetailService, DoctorService $doctorService, UserService $userService, DiagnosticPhleboAssignmentService $diagnosticPhleboAssignmentService, DiagnosticApiService $diagnosticApiService, DiagnosticBarcodeResubmissionService $diagnosticBarcodeResubmissionService)
    {
        $this->diagnosticService = $diagnosticService;
        $this->diagnosticTestService = $diagnosticTestService;
        $this->diagnosticBreakupTestService = $diagnosticBreakupTestService;
        $this->diagnosticBreakupBillService = $diagnosticBreakupBillService;
        $this->patientService = $patientService;
        $this->smsMsgService = $smsMsgService;
        $this->imageUploadService = $imageUploadService;
        $this->rewardService = $rewardService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->doctorService = $doctorService;
        $this->userService = $userService;
        $this->diagnosticPhleboAssignmentService = $diagnosticPhleboAssignmentService;
        $this->diagnosticApiService = $diagnosticApiService;
        $this->diagnosticBarcodeResubmissionService = $diagnosticBarcodeResubmissionService;
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticService->allClinics()->toArray()
        ];
        // dd($data);
        return view('diagnostic::sampleCollection.index',compact('data'));
    }
    public function indexIndividual(Request $request)
    {
        $role_id = DB::table('phlebo_assignments')->where('sample_id',$request->id)->orderBy('id', 'desc')->value('role_id');
        // dd($role_id);
        $data = [
            'id' => $request->id,
            'role_id' => $role_id
        ];
        // dd($data);
        return view('diagnostic::sampleCollection.indexIndividual',compact('data'));
    }
    public function indexRecollection(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticService->allClinics()->toArray()
        ];
        // dd($data);
        return view('diagnostic::sampleRecollection.index',compact('data'));
    }
    public function indexBarcodeEdit(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticService->allClinics()->toArray()
        ];
        // dd($data);
        return view('diagnostic::barcodeEdit.index',compact('data'));
    }
    public function list(Request $request)
    {
        try {
            $role = $this->getUserRole();
            $filter = $request['filter'];
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            // dd($clinic_id);
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $filter['type_of_collection'] =  [
                    'type' => 'eq',
                    'value' => 'CV'
                ];
            $request->merge([
                'filter' => $filter
            ]);
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ],
                // 'sample_collection_breakup_tests' => [
                //     'reletion' => [
                //         'prefix' => 'id',
                //         'suffix' => 'sample_collection_id'
                //     ]
                // ],
                // 'users' => [
                //     'reletion' => [
                //         'prefix' => 'phlebo_id',
                //         'suffix' => 'id'
                //     ]
                // ]
            ];
            // $request['with'] = 'assignPhlebotomists';
            // $request['with'] = [
            //     'tests' => 'id,sample_collection_id,item_id',
            //     'assignPhlebotomists' => 'id,sample_id,phlebo_id,schedule_time,remarks,created_at',
            // ];
            // $request['withFunc'] = [
            //     // 'diagnosticBill' => [
            //     //     'method' => 'withSum',
            //     //     'column' => 'net_amount'
            //     // ],
            //     'paymentBill as total_amount_sum' => [
            //         'method' => 'withSum',
            //         'column' => 'total_amount'
            //     ],
            //     'paymentBill as due_amount_sum' => [
            //         'method' => 'withSum',
            //         'column' => 'due_amount'
            //     ]
            // ];
            $request['withFunc'] = [
                'diagnosticBreakupItemStatus' => [
                    'method' => 'whereHas'
                ]
            ];
            $filter = $request['filter'];
            // $filter['status'] =  [
            //     'type' => 'eq',
            //     'value' => [0,1,2]
            // ];
            
            $request->merge([
                'filter' => $filter
            ]);
            array_push(
                $this->diagnosticService->columns,
                'patients.name as patient_name',
                // 'sample_collection_breakup_tests.status as sample_status',
                // 'clinics.clinic_name as clinic_name'
                // DB::raw('SUM(sample_collection_breakup_bills.net_amount) as total_amount')
            );
            $this->diagnosticService->setRequest($request);
            $this->diagnosticService->findAll();
            // Filter results
            // $this->diagnosticService->entity = collect($this->diagnosticService->entity->items())->where('not_collected_test_count', '!=', 0)->values(); 
            // dd($this->diagnosticService->entity);
            $this->response['success']  = true;
            $data = $this->diagnosticService->getRows();
            
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            // dd($clinic_id);
            $this->response['tbody'] = view('diagnostic::sampleCollection.api.list',compact('data','permissionPage','status_list','clinic_id'))->render();
            $this->response['tfoot'] = $this->diagnosticService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function formCollection($id)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $getBarcodeSampleType = [];
            foreach ($diagnostic->getBarcodeSampleType as $row) {
                $getBarcodeSampleType[$row->sample_type] = $row->sin_no;
            }
            // dd($getBarcodeSampleType);
            // $paymentBill = $diagnostic->paymentBill;
            $role = $this->getUserRole();
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                'sampleCollectionBreakupItem' => $diagnostic->sampleCollectionBreakupItem,
                'getBarcodeSampleType' => $getBarcodeSampleType,
                'role_id' => $role->id ?? 0
            ];
            // $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['patient_detail']->membershipRegistrations);
            $list = [];
            // dd(empty($list['offer_list']));
            $this->response['form'] = view('diagnostic::sampleCollection.api.formCollection',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function patientSignatureCollection(Request $request)
    {
        // dd($request->all());
        try {
            $patient_esign = null;
            if ($request->hasFile('signature_pad')) {
                $fileinfo = $request->file('signature_pad');
                $this->imageUploadService->setFieldName('signature_pad');
                $this->imageUploadService->setFilePath('Diagnostic/Signature/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $patient_esign = $response[0]['name'];
            }
            // dd($patient_esign);
            $diagnostic = $this->diagnosticService->findById($request->sample_id);
            $this->diagnosticService->setRequest([
                'patient_esign' => $patient_esign,
                'modified_by' => $this->modifiedBy()
            ]);
            $diagnostic = $this->diagnosticService->update();
            $this->response['status']  = 'success';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    
    public function addSampleCollection($id,SampleCollectionRequest $request)
    {
        try {
            $request->validated();
            $sample_barcodes = [];
            foreach (json_decode($request->checked_sample_test) as $key => $row) {
                array_push($sample_barcodes,$row->sample_barcode);
            }
            $check_care = DB::table('sample_collection_breakup_tests')->whereIn('sin_no',$sample_barcodes)->pluck('sin_no')->toArray();
            $check_it = DB::table('sample_collection_itdose_barcodes')->whereIn('barcode',$sample_barcodes)->pluck('barcode')->toArray();
            $check = array_merge($check_care,$check_it);
            $check = array_unique($check);
            if(count($check) > 0){
                $this->response['success']  = false;
                $this->response['message']  = 'Barcode '.implode(',', $check).' Already exists.';
                return response()->json($this->response);
            }
            // dd($check);
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            // dd($diagnostic,$diagnostic->assignPhlebotomists);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'status' => 3
            ]);
            $this->diagnosticService->setRequest($request->except('sample_id','checked_sample_test'));
            $this->diagnosticService->update();
            // dd($request->checked_sample_test);
            $playload = [];
            foreach (json_decode($request->checked_sample_test) as $key => $row) {
                $request['sample_type'] = $row->sample_type;
                $request['sin_no'] = $row->sample_barcode;
                $request['vial_qty'] = $row->vialqty;
                $request['sin_created_by'] = $this->createdBy();
                $request['sin_created_on'] = date('Y-m-d H:i:s');
                
                $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                $this->diagnosticBreakupTestService->setRequest($request->except('sample_id','checked_sample_test'));
                $this->diagnosticBreakupTestService->update();
                $itdose_testid = $breakupTest->itdose_testid;
                $sample_type_id = DB::table('sample_types')
                    ->where([
                        'sample_name' => $row->sample_type,
                        'is_active' => 1
                    ])
                    ->value('id') ?? 0;
                $playload[] = [
                    "Test_ID" => $itdose_testid, // test table itdose_test_id
                    "SampleTypeID" => empty($sample_type_id) ? 89 : $sample_type_id, //sample_type table id (if SERUM == 19) if is_active == 1
                    "SampleTypeName" => empty($sample_type_id) ? 'BLOOD' : $row->sample_type, //sample_type table sample_name
                    "BarcodeNo" => $row->sample_barcode,//sin_no
                    "LedgertransactionNo" => $diagnostic->unique_id,//workorder id
                ];
            }
            $apiResponse = $this->collectItdoseTest($diagnostic->unique_id,$playload);
            // dd($apiResponse);
            if (!isset($apiResponse['status']) || $apiResponse['status'] == false) {
                foreach (json_decode($request->checked_sample_test) as $key => $row) {
                    $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                    $this->diagnosticBreakupTestService->setRequest(['sample_reject_status' => 2]);
                    $this->diagnosticBreakupTestService->update();
                }
            }
            $assign_by = $diagnostic->assignPhlebotomists;
            if(!empty($assign_by)){
                $collected_testcount = $assign_by->collected_testcount ?? 0;
                $collected_testcount = $collected_testcount + count(json_decode($request->checked_sample_test));
                
                $data = [
                    'actual_date_time_of_collection' => $request->actual_date_time_of_collection, 
                    'temp_of_bag_at_collection' => $request->temp_of_bag_at_collection, 
                    // 'payment_status' => $payment_status, 
                    // 'amount' => $amount, 
                    // 'mode_of_payment' => $mode, 
                    'collected_testcount'=>$collected_testcount,
                    'handover_status' => '0',
                    'collection_status' => '1',
                    'collected_by'=>$this->createdBy()
                ];
                $this->diagnosticPhleboAssignmentService->findById($assign_by->id);
                $this->diagnosticPhleboAssignmentService->setRequest($data);
                $this->diagnosticPhleboAssignmentService->update();
            }
            // dd($request->all());
            $this->response['success']  = true;
            $this->response['message']  = 'Sample has been collected successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    private function collectItdoseTest($work_order_id,$playload)
    {
        $dataArray = [];
        // $dataArray['url'] = 'https://lab.mymdindia.com/mymd_uat/api/allapi/CollectSample';
        $dataArray['template_id'] = 2;
        $dataArray['work_order_id'] = $work_order_id;
        $dataArray['created_by'] = $this->createdBy();
        $dataArray['playload'] = $playload;
        // dd($dataArray);
        // dispatch(new DiagnosticApiJob($this->diagnosticApiService,$dataArray))->onQueue('itdose_collect_api');
        $this->diagnosticApiService->setDataArray($dataArray);
        $apiResponse = $this->diagnosticApiService->callApi();
        return $apiResponse;
    }
    public function listRecollection(Request $request)
    {
        try {
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $role = $this->getUserRole();
            if($role->id != 1){
                $request['withFunc'] = [
                    'diagnosticApiLogsStatusCreated' => [
                        'method' => 'whereHas'
                    ]
                ];
            }
            else {
                $request['withFunc'] = [
                    'diagnosticApiLogsStatus' => [
                        'method' => 'whereHas'
                    ]
                ];
            }
            $filter = $request['filter'];
            // $filter['status'] =  [
            //     'type' => 'eq',
            //     'value' => [0,1,2]
            // ];
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            // dd($clinic_id);
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $request->merge([
                'filter' => $filter
            ]);
            $request['with'] = [
                'requestResubmission' => 'id,work_order_id,status',
            ];
            array_push(
                $this->diagnosticService->columns,
                'patients.name as patient_name',
            );
            $this->diagnosticService->setRequest($request);
            $this->diagnosticService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticService->getRows();
            // dd($data['rows']);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            // dd($status_list);
            $this->response['tbody'] = view('diagnostic::sampleRecollection.api.list',compact('data','permissionPage','status_list','clinic_id'))->render();
            $this->response['tfoot'] = $this->diagnosticService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function formRecollection($id)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // dd($diagnostic->sampleRecollectionBreakupItem);
            // $paymentBill = $diagnostic->paymentBill;
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                'sampleRecollectionBreakupItem' => $diagnostic->sampleRecollectionBreakupItem,
                'role_id' => $this->getUserRole()->id ?? 0
            ];
            // $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['patient_detail']->membershipRegistrations);
            $list = [];
            // dd(empty($list['offer_list']));
            $this->response['form'] = view('diagnostic::sampleRecollection.api.formCollection',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addSampleRecollection($id,SampleReCollectionRequest $request)
    {
        try {
            $request->validated();
            $sample_barcodes = [];
            foreach (json_decode($request->checked_sample_test) as $key => $row) {
                array_push($sample_barcodes,$row->sample_barcode);
            }
            $check_care = DB::table('sample_collection_breakup_tests')->whereIn('sin_no',$sample_barcodes)->pluck('sin_no')->toArray();
            $check_it = DB::table('sample_collection_itdose_barcodes')->whereIn('barcode',$sample_barcodes)->pluck('barcode')->toArray();
            $check = array_merge($check_care,$check_it);
            if(count($check) > 0){
                $this->response['success']  = false;
                $this->response['message']  = 'Barcode '.implode(',', $check).' Already exists.';
                return response()->json($this->response);
            }
            // dd($check);
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                // 'status' => 3
            ]);
            // $this->diagnosticService->setRequest($request->except('sample_id','checked_sample_test'));
            // $this->diagnosticService->update();
            // dd($request->checked_sample_test);
            if ($request->submit == 2) {// if admin accept
                // previous api log status update
                $api_link = DB::table('sample_collection_api_templates')->where('id',2)->value('api_link');
                SampleCollectionApiLog::where('work_order_id', $diagnostic->unique_id)
                    ->where('api_link', $api_link)
                    ->update(['status' => 2]);
                // end previous api log status update
                $playload = [];
                foreach (json_decode($request->checked_sample_test) as $key => $row) {
                    $request['sample_type'] = $row->sample_type;
                    $request['sin_no'] = $row->sample_barcode;
                    $request['vial_qty'] = $row->vialqty;
                    $request['sin_created_by'] = $this->createdBy();
                    $request['sin_created_on'] = date('Y-m-d H:i:s');
                    
                    $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                    $this->diagnosticBreakupTestService->setRequest($request->except('sample_id','checked_sample_test'));
                    $this->diagnosticBreakupTestService->update();
                    $itdose_testid = $breakupTest->itdose_testid;
                    // resubmission test
                    
                    $sample_type_id = DB::table('sample_types')
                        ->where([
                            'sample_name' => $row->sample_type,
                            'is_active' => 1
                        ])
                        ->value('id') ?? 0;
                    $playload[] = [
                        "Test_ID" => $itdose_testid, // test table itdose_test_id
                        "SampleTypeID" => empty($sample_type_id) ? 89 : $sample_type_id, //sample_type table id (if SERUM == 19) if is_active == 1
                        "SampleTypeName" => empty($sample_type_id) ? 'BLOOD' : $row->sample_type, //sample_type table sample_name
                        "BarcodeNo" => $row->sample_barcode,//sin_no
                        "LedgertransactionNo" => $diagnostic->unique_id,//workorder id
                    ];
                }
                $apiResponse = $this->collectItdoseTest($diagnostic->unique_id,$playload);
                // dd($apiResponse);
                foreach (json_decode($request->checked_sample_test) as $key => $row) {
                    $sample_reject_status = (!isset($apiResponse['status']) || $apiResponse['status'] == false) ? 2 : 1;
                    $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                    $this->diagnosticBreakupTestService->setRequest(['sample_reject_status' => $sample_reject_status]);
                    $this->diagnosticBreakupTestService->update();
                    // submit to resubmission table
                    $this->reSubmission($diagnostic->unique_id,$row->test_id,$breakupTest->test_id,$breakupTest->itdose_testid,$row->sample_type,$row->sample_barcode,$request->submit);
                }
                
            }
            
            else {// if clinic base user request or admin reject
                foreach (json_decode($request->checked_sample_test) as $key => $row) {
                    $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                    $this->reSubmission($diagnostic->unique_id,$row->test_id,$breakupTest->test_id,$breakupTest->itdose_testid,$row->sample_type,$row->sample_barcode,$request->submit);
                }
            }
            // dd($request->all());
            $this->response['success']  = true;
            $this->response['message']  = 'Sample has been recollected successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    private function reSubmission($work_order_id,$breakup_test_id,$test_id,$itdose_test_id,$sample_type,$sin_no,$status)
    {
        $resubmisiionData = [
            'work_order_id' => $work_order_id,
            'breakup_test_id' => $breakup_test_id,
            'test_id' => $test_id,
            'itdose_test_id' => $itdose_test_id,
            'sample_type' => $sample_type,
            'sin_no' => $sin_no,
            'status' => $status//1:request,2:Accept,3:reject
        ];
        //created_by ,modified_by
        // resubmission test
        $resub_id = SampleCollectionBarcodeResubmission::where([
            'work_order_id' => $work_order_id,
            'breakup_test_id' => $breakup_test_id
        ])->value('id') ?? 0;

        if (!$resub_id) {
            $resubmisiionData['created_by'] = $this->createdBy();
            $this->diagnosticBarcodeResubmissionService->setRequest($resubmisiionData);
            $resubmission = $this->diagnosticBarcodeResubmissionService->add();
        }
        else {
            $this->diagnosticBarcodeResubmissionService->findById($resub_id);
            $resubmisiionData['modified_by'] = $this->modifiedBy();
            $this->diagnosticBarcodeResubmissionService->setRequest($resubmisiionData);
            $resubmission = $this->diagnosticBarcodeResubmissionService->update();
        }
        return $resubmission;
    }
    public function updateLabStatusItdose(LabStatusItdoseRequest $request)
    {
        try {
            $request->validated();
            $authorizationHeader = request()->header('Authorization');

            // Check if the header exists and contains a Bearer token
            if ($authorizationHeader && preg_match('/Bearer\s(\S+)/', $authorizationHeader, $matches)) {
                $token = $matches[1];
            } else {
                $token = null; // or handle error as needed
            }

            $this->diagnosticApiService->setRequestApi();
            $this->diagnosticApiService->work_order_id = $request->WorkOrderID ?? null;
            $this->diagnosticApiService->url = url()->current();
            $this->diagnosticApiService->playloadData = json_encode($request->all());
            // $this->diagnosticApiService->responseData = json_encode($this->response);
            $this->diagnosticApiService->setDataForRestApi(0);
            $this->diagnosticApiService->add();

            if ($token != ENV('ITDOSE_TOKEN')) {
                $this->response['success']    = false;
                $this->response['message']  = 'Invalid Token!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            if ($request->success != "true") {
                $this->response['success']    = false;
                $this->response['message']  = 'Some problem occurred!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            $diagnostic = $this->diagnosticService->findByOtherId('unique_id',$request->WorkOrderID);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Invalid WorkOrderID!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            if (!isset($request->data) && count($request->data) == 0) {
                $this->response['success']  = false;
                $this->response['message']  = 'Data has some problem.Please check.!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            foreach ($request->data as $key => $row) {
                if (!isset($row['ItemCode'])) {
                    $this->response['success']  = false;
                    $this->response['message']  = 'Data has some problem.Please check.!';
                    $this->diagnosticApiService->responseData = json_encode($this->response);
                    $this->diagnosticApiService->setDataForRestApi(1);
                    $this->diagnosticApiService->update();
                    return response()->json($this->response);
                }
                $test = $diagnostic->diagnosticBreakupItem->where('test_code',$row['ItemCode'])->first();
                if (!isset($test)) {
                    $this->response['success']  = false;
                    $this->response['message']  = 'Data has some problem.Please check.!';
                    $this->diagnosticApiService->responseData = json_encode($this->response);
                    $this->diagnosticApiService->setDataForRestApi(1);
                    $this->diagnosticApiService->update();
                    return response()->json($this->response);
                }
                $test->update([
                    'itdose_test_status' => $row['TestStatus'],
                ]);
                // dd($test->itdose_testid);
                SampleCollectionBreakupTestItdose::create([
                    'breakup_test_id' => $test->id,
                    'workorder_id' => $request->WorkOrderID,
                    'itdose_test_id' => $test->itdose_testid,
                    'test_code' => $row['ItemCode'],
                    'status' => $row['TestStatus'],
                ]);
                // dd($test);
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Status has been updated successfully!';
            $this->response['data']     = [];
            $this->diagnosticApiService->responseData = json_encode($this->response);
            $this->diagnosticApiService->setDataForRestApi(2);
            $this->diagnosticApiService->update();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updateLabReportItdose(LabStatusItdoseRequest $request)
    {
        try {
            $request->validated();
            $authorizationHeader = request()->header('Authorization');

            // Check if the header exists and contains a Bearer token
            if ($authorizationHeader && preg_match('/Bearer\s(\S+)/', $authorizationHeader, $matches)) {
                $token = $matches[1];
            } else {
                $token = null; // or handle error as needed
            }
            
            $this->diagnosticApiService->setRequestApi();
            $this->diagnosticApiService->work_order_id = $request->WorkOrderID ?? null;
            $this->diagnosticApiService->url = url()->current();
            $this->diagnosticApiService->playloadData = json_encode($request->all());
            // $this->diagnosticApiService->responseData = json_encode($this->response);
            $this->diagnosticApiService->setDataForRestApi(0);
            $this->diagnosticApiService->add();

            if ($token != ENV('ITDOSE_TOKEN')) {
                $this->response['success']    = false;
                $this->response['message']  = 'Invalid Token!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            if ($request->success != "true") {
                $this->response['success']    = false;
                $this->response['message']  = 'Some problem occurred!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            $diagnostic = $this->diagnosticService->findByOtherId('unique_id',$request->WorkOrderID);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Invalid WorkOrderID!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
        
            if (!isset($request->testID) || !isset($request->data)) {
                $this->response['success']  = false;
                $this->response['message']  = 'Data has some problem.Please check.!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            $test = $diagnostic->diagnosticBreakupItem->where('itdose_testid',$request->testID)->where('unique_id',$request->WorkOrderID)->first();
            if (!isset($test)) {
                $this->response['success']  = false;
                $this->response['message']  = 'Data has some problem.Please check.!';
                $this->diagnosticApiService->responseData = json_encode($this->response);
                $this->diagnosticApiService->setDataForRestApi(1);
                $this->diagnosticApiService->update();
                return response()->json($this->response);
            }
            // dd($test);
            $test->update([
                'reportGenerated' => $request->data ?? null,
            ]);
            // dd($test);
            $this->response['success']  = true;
            $this->response['message']  = 'Report has been generated successfully!';
            $this->response['data']     = [];
            $this->diagnosticApiService->responseData = json_encode($this->response);
            $this->diagnosticApiService->setDataForRestApi(2);
            $this->diagnosticApiService->update();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function listBarcodeEdit(Request $request)
    {
        try {
            $role = $this->getUserRole();
            $filter = $request['filter'];
            switch ($role->id) {
                case 4: // Center Manager
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5: // Receptionist
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6: // Nurse
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7: // Phlebotomist
                    $clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            // dd($clinic_id);
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $filter['status'] =  [
                    'type' => 'eq',
                    'value' => [3,4,5]
                ];
            $request->merge([
                'filter' => $filter
            ]);
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $request['withFunc'] = [
                'diagnosticBreakupItemStatus' => [
                    'method' => 'whereHas'
                ]
            ];
            $filter = $request['filter'];
            
            $request->merge([
                'filter' => $filter
            ]);
            array_push(
                $this->diagnosticService->columns,
                'patients.name as patient_name',
            );
            $this->diagnosticService->setRequest($request);
            $this->diagnosticService->findAll();
            $data = $this->diagnosticService->getRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            // dd($clinic_id);
            $this->response['success']  = true;
            $this->response['tbody'] = view('diagnostic::barcodeEdit.api.list',compact('data','permissionPage','status_list','clinic_id'))->render();
            $this->response['tfoot'] = $this->diagnosticService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function formBarcodeEdit($id)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $getBarcodeSampleType = [];
            foreach ($diagnostic->getBarcodeSampleType as $row) {
                $getBarcodeSampleType[$row->sample_type] = $row->sin_no;
            }
            // dd($diagnostic->sampleCollectionBreakupItemBarcodeEdit);
            // $paymentBill = $diagnostic->paymentBill;
            $role = $this->getUserRole();
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                'sampleCollectionBreakupItem' => $diagnostic->sampleCollectionBreakupItemBarcodeEdit,
                'getBarcodeSampleType' => $getBarcodeSampleType,
                'role_id' => $role->id ?? 0
            ];
            // $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['patient_detail']->membershipRegistrations);
            $list = [];
            // dd(empty($list['offer_list']));
            $this->response['form'] = view('diagnostic::barcodeEdit.api.formBarcodeEdit',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addBarcodeEdit($id,SampleCollectionBarcodeEditRequest $request)
    {
        try {
            $request->validated();
            $sample_barcodes = [];
            foreach (json_decode($request->checked_sample_test) as $key => $row) {
                array_push($sample_barcodes,$row->sample_barcode);
            }
            // dd($sample_bar);
            $check_care = DB::table('sample_collection_breakup_tests')->whereIn('sin_no',$sample_barcodes)->pluck('sin_no')->toArray();
            $check_it = DB::table('sample_collection_itdose_barcodes')->whereIn('barcode',$sample_barcodes)->pluck('barcode')->toArray();
            $check = array_merge($check_care,$check_it);
            $check = array_unique($check);
            if(count($check) > 0){
                $this->response['success']  = false;
                $this->response['message']  = 'Barcode '.implode(',', $check).' Already exists.';
                return response()->json($this->response);
            }
            // dd($check);
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            // dd($diagnostic,$diagnostic->assignPhlebotomists);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'status' => 3
            ]);
            $this->diagnosticService->setRequest($request->except('sample_id','checked_sample_test'));
            $this->diagnosticService->update();
            // dd($request->checked_sample_test);
            $playload = [];
            foreach (json_decode($request->checked_sample_test) as $key => $row) {
                $request['sample_type'] = $row->sample_type;
                $request['sin_no'] = $row->sample_barcode;
                $request['vial_qty'] = $row->vialqty;
                $request['sin_created_by'] = $this->createdBy();
                $request['sin_created_on'] = date('Y-m-d H:i:s');
                $request['status'] = 3;
                
                $breakupTest = $this->diagnosticBreakupTestService->findById($row->test_id);
                $this->diagnosticBreakupTestService->setRequest($request->except('sample_id','checked_sample_test'));
                $this->diagnosticBreakupTestService->update();
                
                $data_barcode_resubmission = [
                    'status'=>2,
                    'modified_by'=>$this->modifiedBy()
                ];
                DB::table('sample_collection_barcode_resubmissions')
                    ->where([
                        'work_order_id' => $diagnostic->unique_id,
                        'breakup_test_id' => $row->test_id
                    ])
                    ->update($data_barcode_resubmission);
            }
            // dd($request->all());
            $this->response['success']  = true;
            $this->response['message']  = 'Barcode has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
}
