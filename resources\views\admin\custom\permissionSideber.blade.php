<li class="nav-item ps-2">
    <a class="nav-link sidebarActive" data-id="home" aria-current="page" href="{{ route('user.dashboard') }}">
        <i class="icon">
            <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-20">
                <path opacity="0.4"
                    d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z"
                    fill="currentColor"></path>
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2ZM4.53852 13.4655H7.92449C9.32676 13.4655 10.463 14.6114 10.463 16.0255V19.44C10.463 20.8532 9.32676 22 7.92449 22H4.53852C3.13626 22 2 20.8532 2 19.44V16.0255C2 14.6114 3.13626 13.4655 4.53852 13.4655ZM19.4615 13.4655H16.0755C14.6732 13.4655 13.537 14.6114 13.537 16.0255V19.44C13.537 20.8532 14.6732 22 16.0755 22H19.4615C20.8637 22 22 20.8532 22 19.44V16.0255C22 14.6114 20.8637 13.4655 19.4615 13.4655Z"
                    fill="currentColor"></path>
            </svg>
        </i>
        <span class="item-name">Home</span>
    </a>
</li>
@if (array_intersect(['view_speciality', 'view_doctor', 'view_doctor_type'], $permissionPage))
    {{-- Doctor --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="doctor" data-bs-toggle="collapse" href="#manu-doctor" role="button"
            aria-expanded="false" aria-controls="manu-doctor">
            <i class="icon">

                <svg class="icon-20" height="20" viewBox="0 0 100 100" width="20"
                    xmlns="http://www.w3.org/2000/svg">
                    <g>
                        <path
                            d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                            fill="currentColor" />
                        <path
                            d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                            fill="currentColor" opacity=".4" />
                        <circle cx="75.521" cy="70.648" r="3" fill="currentColor" />
                    </g>
                </svg>
            </i>
            <span class="item-name">Doctor</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-doctor" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_speciality'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="doctor-speciality"
                        href="{{ route('speciality.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10" viewBox="0 0 24 24"
                                fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> List Of Specialities </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_doctor_type'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="doctor-type" href="{{ route('doctor.type.index') }}">
                        <i class="icon svg-icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10" viewBox="0 0 24 24"
                                fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> D </i>
                        <span class="item-name">Doctor Types</span>
                    </a>
                </li>
            @endif
            {{-- @if (array_intersect(['view_doctor'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="doctor-list" href="{{ route('doctor.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10" viewBox="0 0 24 24"
                                fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> List of Doctor </span>
                    </a>
                </li>
            @endif --}}
        </ul>
    </li>
@endif
@if (array_intersect(['view_clinic', 'view_clinic_district', 'view_clinic_holiday'], $permissionPage))
    {{-- Manage Clinic --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="clinic" data-bs-toggle="collapse" href="#manu-clinic" role="button"
            aria-expanded="false" aria-controls="manu-clinic">
            <i class="icon">
                <svg class="icon-20" height="512" viewBox="0 0 32 32" width="512"
                    xmlns="http://www.w3.org/2000/svg" data-name="line expand copy">
                    <path
                        d="m16 0a12.9 12.9 0 0 0 -12.17 8.21 12.91 12.91 0 0 0 3 14.36l9.17 8.82 9.13-8.82a12.91 12.91 0 0 0 3-14.36 12.9 12.9 0 0 0 -12.13-8.21zm7 20h-14v-11.54l7-4.66 7 4.66z"
                        fill="currentColor" opacity=".4" />
                    <path d="m11 9.54v8.46h10v-8.46l-5-3.34zm7.5 3v2h-1.5v1.46h-2v-1.5h-1.5v-2h1.5v-1.5h2v1.5z"
                        fill="currentColor" />
                </svg>
            </i>
            <span class="item-name">Manage Clinic</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-clinic" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_clinic_district'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="clinic-district"
                        href="{{ route('clinic.district.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> District </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_clinic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="clinic-list" href="{{ route('clinic.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> List Of Clinics </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_clinic_holiday'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="clinic-holiday"
                        href="{{ route('clinic.holiday.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Holidays </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(
        [
            'view_user',
            // 'view_agent',
            // 'view_user_cluster',
            // 'view_user_center',
            // 'view_user_pharmacist',
            // 'view_user_receptionist',
            // 'view_user_nurse',
            // 'view_user_care',
            // 'view_user_phlebotomist',
        ],
        $permissionPage))
    {{-- Users --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="users" data-bs-toggle="collapse" href="#manu-users" role="button"
            aria-expanded="false" aria-controls="manu-users">
            <i class="icon">

                <svg width="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="icon-20" height="32">
                    <path
                        d="M9.34933 14.8577C5.38553 14.8577 2 15.47 2 17.9173C2 20.3665 5.364 20.9999 9.34933 20.9999C13.3131 20.9999 16.6987 20.3876 16.6987 17.9403C16.6987 15.4911 13.3347 14.8577 9.34933 14.8577Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M9.34935 12.5248C12.049 12.5248 14.2124 10.4062 14.2124 7.76241C14.2124 5.11865 12.049 3 9.34935 3C6.65072 3 4.48633 5.11865 4.48633 7.76241C4.48633 10.4062 6.65072 12.5248 9.34935 12.5248Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M16.1733 7.84873C16.1733 9.19505 15.7604 10.4513 15.0363 11.4948C14.961 11.6021 15.0275 11.7468 15.1586 11.7698C15.3406 11.7995 15.5275 11.8177 15.7183 11.8216C17.6165 11.8704 19.3201 10.6736 19.7907 8.87116C20.4884 6.19674 18.4414 3.79541 15.8338 3.79541C15.551 3.79541 15.2799 3.82416 15.0157 3.87686C14.9795 3.88453 14.9404 3.90177 14.9208 3.93244C14.8954 3.97172 14.914 4.02251 14.9394 4.05605C15.7232 5.13214 16.1733 6.44205 16.1733 7.84873Z"
                        fill="currentColor"></path>
                    <path
                        d="M21.779 15.1693C21.4316 14.4439 20.593 13.9465 19.3171 13.7022C18.7153 13.5585 17.0852 13.3544 15.5695 13.3831C15.547 13.386 15.5343 13.4013 15.5324 13.4109C15.5294 13.4262 15.5363 13.4492 15.5656 13.4655C16.2662 13.8047 18.9737 15.2804 18.6332 18.3927C18.6185 18.5288 18.729 18.6438 18.867 18.6246C19.5333 18.5317 21.2476 18.1704 21.779 17.0474C22.0735 16.4533 22.0735 15.7634 21.779 15.1693Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Users</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-users" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_user'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-user" href="{{ route('user.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> All </span>
                    </a>
                </li>
            @endif
            {{-- @if (array_intersect(['view_agent'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-agent" href="{{ route('agent.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Agent </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_cluster'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-cluster"
                        href="{{ route('user.cluster.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Cluster Users </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_center'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-center"
                        href="{{ route('user.center.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Center Managers </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_pharmacist'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-pharmacist"
                        href="{{ route('user.pharmacist.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Pharmacists </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_receptionist'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-receptionist"
                        href="{{ route('user.receptionist.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Receptionists </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_nurse'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-nurse" href="{{ route('user.nurse.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Nurses </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_care'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-care"
                        href="{{ route('user.customerCare.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Customer Cares </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_user_phlebotomist'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="users-phlebotomist"
                        href="{{ route('user.phlebotomist.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Phlebotomists </span>
                    </a>
                </li>
            @endif --}}
        </ul>
    </li>
@endif
@if (array_intersect(['view_patient', 'search_patient'], $permissionPage))
    {{-- Manage patient --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="patient" data-bs-toggle="collapse" href="#manu-patient" role="button"
            aria-expanded="false" aria-controls="manu-patient">
            <i class="icon">
                <svg class="icon-20" height="32" viewBox="0 0 100 100" width="32"
                    xmlns="http://www.w3.org/2000/svg">
                    <g>
                        <path
                            d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                            fill="currentColor" />
                        <path
                            d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                            fill="currentColor" opacity=".4" />
                    </g>
                </svg>
            </i>
            <span class="item-name">Patient</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-patient" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_patient'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="patient-list" href="{{ route('patient.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Patient List </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['search_patient'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="patient-searchList"
                        href="{{ route('patient.search') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Patient Search </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_schedule'], $permissionPage))
    {{-- Manage schedule --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="schedule" data-bs-toggle="collapse" href="#manu-schedule" role="button"
            aria-expanded="false" aria-controls="manu-schedule">
            <i class="icon">

                <svg width="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="icon-20" height="32">
                    <path opacity="0.4"
                        d="M16.34 1.99976H7.67C4.28 1.99976 2 4.37976 2 7.91976V16.0898C2 19.6198 4.28 21.9998 7.67 21.9998H16.34C19.73 21.9998 22 19.6198 22 16.0898V7.91976C22 4.37976 19.73 1.99976 16.34 1.99976Z"
                        fill="currentColor"></path>
                    <path
                        d="M15.5734 15.8143C15.4424 15.8143 15.3104 15.7803 15.1894 15.7093L11.2634 13.3673C11.0374 13.2313 10.8984 12.9863 10.8984 12.7223V7.67529C10.8984 7.26129 11.2344 6.92529 11.6484 6.92529C12.0624 6.92529 12.3984 7.26129 12.3984 7.67529V12.2963L15.9584 14.4193C16.3134 14.6323 16.4304 15.0923 16.2184 15.4483C16.0774 15.6833 15.8284 15.8143 15.5734 15.8143Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Schedule</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-schedule" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_schedule'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="schedule-list" href="{{ route('schedule.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> All Schedule </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_billing', 'view_ledger_billing'], $permissionPage))
    {{-- Manage membership billing --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="billing" data-bs-toggle="collapse" href="#manu-billing" role="button"
            aria-expanded="false" aria-controls="manu-billing">
            <i class="icon">
                <svg width="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.4"
                        d="M16.191 2H7.81C4.77 2 3 3.78 3 6.83V17.16C3 20.26 4.77 22 7.81 22H16.191C19.28 22 21 20.26 21 17.16V6.83C21 3.78 19.28 2 16.191 2Z"
                        fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.07996 6.6499V6.6599C7.64896 6.6599 7.29996 7.0099 7.29996 7.4399C7.29996 7.8699 7.64896 8.2199 8.07996 8.2199H11.069C11.5 8.2199 11.85 7.8699 11.85 7.4289C11.85 6.9999 11.5 6.6499 11.069 6.6499H8.07996ZM15.92 12.7399H8.07996C7.64896 12.7399 7.29996 12.3899 7.29996 11.9599C7.29996 11.5299 7.64896 11.1789 8.07996 11.1789H15.92C16.35 11.1789 16.7 11.5299 16.7 11.9599C16.7 12.3899 16.35 12.7399 15.92 12.7399ZM15.92 17.3099H8.07996C7.77996 17.3499 7.48996 17.1999 7.32996 16.9499C7.16996 16.6899 7.16996 16.3599 7.32996 16.1099C7.48996 15.8499 7.77996 15.7099 8.07996 15.7399H15.92C16.319 15.7799 16.62 16.1199 16.62 16.5299C16.62 16.9289 16.319 17.2699 15.92 17.3099Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Billing</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-billing" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_billing'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="billing-list" href="{{ route('billing.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Membership Billing </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_ledger_billing'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="billing-ledger"
                        href="{{ route('billing.ledger.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Ledger </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_appointment', 'view_today_appointment', 'view_request_appointment', 'view_capture_vitals'], $permissionPage))
    {{-- Manage membership appointment --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="appointment" data-bs-toggle="collapse" href="#manu-appointment" role="button"
            aria-expanded="false" aria-controls="manu-appointment">
            <i class="icon">

                <svg width="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="icon-20" height="32">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M3 16.8701V9.25708H21V16.9311C21 20.0701 19.0241 22.0001 15.8628 22.0001H8.12733C4.99561 22.0001 3 20.0301 3 16.8701ZM7.95938 14.4101C7.50494 14.4311 7.12953 14.0701 7.10977 13.6111C7.10977 13.1511 7.46542 12.7711 7.91987 12.7501C8.36443 12.7501 8.72997 13.1011 8.73985 13.5501C8.7596 14.0111 8.40395 14.3911 7.95938 14.4101ZM12.0198 14.4101C11.5653 14.4311 11.1899 14.0701 11.1701 13.6111C11.1701 13.1511 11.5258 12.7711 11.9802 12.7501C12.4248 12.7501 12.7903 13.1011 12.8002 13.5501C12.82 14.0111 12.4643 14.3911 12.0198 14.4101ZM16.0505 18.0901C15.596 18.0801 15.2305 17.7001 15.2305 17.2401C15.2206 16.7801 15.5862 16.4011 16.0406 16.3911H16.0505C16.5148 16.3911 16.8902 16.7711 16.8902 17.2401C16.8902 17.7101 16.5148 18.0901 16.0505 18.0901ZM11.1701 17.2401C11.1899 17.7001 11.5653 18.0611 12.0198 18.0401C12.4643 18.0211 12.82 17.6411 12.8002 17.1811C12.7903 16.7311 12.4248 16.3801 11.9802 16.3801C11.5258 16.4011 11.1701 16.7801 11.1701 17.2401ZM7.09989 17.2401C7.11965 17.7001 7.49506 18.0611 7.94951 18.0401C8.39407 18.0211 8.74973 17.6411 8.72997 17.1811C8.72009 16.7311 8.35456 16.3801 7.90999 16.3801C7.45554 16.4011 7.09989 16.7801 7.09989 17.2401ZM15.2404 13.6011C15.2404 13.1411 15.596 12.7711 16.0505 12.7611C16.4951 12.7611 16.8507 13.1201 16.8705 13.5611C16.8804 14.0211 16.5247 14.4011 16.0801 14.4101C15.6257 14.4201 15.2503 14.0701 15.2404 13.6111V13.6011Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M3.00293 9.25699C3.01577 8.66999 3.06517 7.50499 3.15803 7.12999C3.63224 5.02099 5.24256 3.68099 7.54442 3.48999H16.4555C18.7376 3.69099 20.3677 5.03999 20.8419 7.12999C20.9338 7.49499 20.9832 8.66899 20.996 9.25699H3.00293Z"
                        fill="currentColor"></path>
                    <path
                        d="M8.30465 6.59C8.73934 6.59 9.06535 6.261 9.06535 5.82V2.771C9.06535 2.33 8.73934 2 8.30465 2C7.86996 2 7.54395 2.33 7.54395 2.771V5.82C7.54395 6.261 7.86996 6.59 8.30465 6.59Z"
                        fill="currentColor"></path>
                    <path
                        d="M15.6953 6.59C16.1201 6.59 16.456 6.261 16.456 5.82V2.771C16.456 2.33 16.1201 2 15.6953 2C15.2606 2 14.9346 2.33 14.9346 2.771V5.82C14.9346 6.261 15.2606 6.59 15.6953 6.59Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Appointment</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-appointment" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_appointment'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="appointment-all"
                        href="{{ route('appointment.index', ['all']) }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> All </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_today_appointment'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="appointment-today"
                        href="{{ route('appointment.index', ['today']) }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Today </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_request_appointment'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="appointment-request"
                        href="{{ route('appointment.request') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Appointment Request </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_capture_vitals'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="capture-vitals"
                        href="{{ route('appointment.captureVitals') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Capture Vitals </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_pharmacy_order'], $permissionPage))
    {{-- order --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="pharmacy" data-bs-toggle="collapse" href="#manu-pharmacy" role="button"
            aria-expanded="false" aria-controls="manu-pharmacy">
            <i class="icon">
                <svg id="fi_2055176" height="18" viewBox="0 0 64 64" width="18"
                    xmlns="http://www.w3.org/2000/svg" data-name="Layer 2">
                    <path
                        d="m45 3h-26a1 1 0 0 0 -1 1v20a1 1 0 0 0 1 1h26a1 1 0 0 0 1-1v-20a1 1 0 0 0 -1-1zm-6 13a1 1 0 0 1 -1 1h-3v3a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1-1v-3h-3a1 1 0 0 1 -1-1v-4a1 1 0 0 1 1-1h3v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3h3a1 1 0 0 1 1 1z"
                        fill="currentColor"></path>
                    <path
                        d="m50.36 38a8.32 8.32 0 0 1 -5.36-1.94 8.32 8.32 0 0 1 -5.36 1.94c-1.26-.14-4.56.64-7.64-1.94a8.32 8.32 0 0 1 -5.36 1.94c-1.26-.14-4.56.64-7.64-1.94-3.08 2.58-6.38 1.8-7.64 1.94a8.36 8.36 0 0 1 -3.36-.71v22.71a1 1 0 0 0 1 1h5v-18a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1v18h26a1 1 0 0 0 1-1v-22.71c-2.31 1.02-4 .62-5.64.71zm-1.36 16a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1-1v-11a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1z"
                        fill="currentColor"></path>
                    <path
                        d="m18 26.82a3 3 0 0 1 -2-2.82v-11h-7a1 1 0 0 0 -1 .81l-3 15.64a1.37 1.37 0 0 0 0 .19 6.36 6.36 0 0 0 6.36 6.36h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                        fill="currentColor"></path>
                    <path d="m31 27h-11v7.27a6.35 6.35 0 0 0 4.36 1.73h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                        fill="currentColor"></path>
                    <path d="m44 27h-11v7.27a6.35 6.35 0 0 0 4.36 1.73h2.28a6.35 6.35 0 0 0 4.36-1.73z"
                        fill="currentColor"></path>
                    <path
                        d="m59 29.45-3-15.64a1 1 0 0 0 -1-.81h-7v11a3 3 0 0 1 -2 2.82v7.45a6.35 6.35 0 0 0 4.36 1.73h2.28a6.36 6.36 0 0 0 6.36-6.36 1.37 1.37 0 0 0 0-.19z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Pharmacy</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-pharmacy" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_pharmacy_order'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="pharmacy-order"
                        href="{{ route('pharmacy.order.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Order </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_pharmacy_category', 'view_pharmacy_medicine'], $permissionPage))
    {{-- Manage category --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="category" data-bs-toggle="collapse" href="#manu-category" role="button"
            aria-expanded="false" aria-controls="manu-category">
            <i class="icon">
                <svg height="32" class="icon-20" viewBox="0 0 512 512" width="32"
                    xmlns="http://www.w3.org/2000/svg">
                    <g id="MEDICAL">
                        <path d="m.7 386.41c9.29 70 68.91 123.94 141.1 123.94s131.8-54 141.09-123.94z"
                            fill="currentColor" opacity=".4" />
                        <path d="m282.89 348.24c-9.29-70-68.91-123.93-141.09-123.93s-131.8 53.97-141.1 123.93z"
                            fill="currentColor" opacity=".4" />
                        <path
                            d="m501 330.06-46-109.37a383.78 383.78 0 0 0 -128.45 30.14q-17.64 7.48-34.28 16.57a181.7 181.7 0 0 1 -3.64 205.17 129.62 129.62 0 0 0 143.24 28.62c66.13-28.07 97.13-104.69 69.13-171.13z"
                            fill="currentColor" />
                        <path
                            d="m395.53 79.32c-27.94-66.43-104.21-97.53-170.34-69.45-66.13 28.07-97.08 104.69-69.13 171.13l2.48 5.91a179.41 179.41 0 0 1 109 50.57 429.35 429.35 0 0 1 44.18-21.81 421.5 421.5 0 0 1 127.62-32.11zm-41.81 39.68a19 19 0 0 1 -24.88-10.15 62.31 62.31 0 0 0 -41.79-36.37 19 19 0 0 1 9.7-36.74 100.07 100.07 0 0 1 67.12 58.37 19 19 0 0 1 -10.15 24.89z"
                            fill="currentColor" />
                    </g>
                </svg>
            </i>
            <span class="item-name">Medicine</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-category" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_pharmacy_category'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="category-list"
                        href="{{ route('pharmacy.category.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> List Of Category </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_pharmacy_medicine'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="medicine-list"
                        href="{{ route('pharmacy.medicine.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> List Of Medicine </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(
        [
            'view_department',
            'view_diagnostic_test',
            'view_diagnostic',
            'view_sample_collection_diagnostic',
            'view_sample_recollection_diagnostic',
        ],
        $permissionPage))
    {{-- diagnostic --}}
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="diagnostic" data-bs-toggle="collapse" href="#manu-diagnostic" role="button"
            aria-expanded="false" aria-controls="manu-diagnostic">
            <i class="icon">
                <svg id="fi_2183886" enable-background="new 0 0 512 512" height="20" viewBox="0 0 512 512"
                    width="20" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="m199.463 116h-132c-8.284 0-15 6.716-15 15 0 6.528 4.178 12.067 10 14.128v83.872h142v-83.872c5.822-2.061 10-7.6 10-14.128 0-8.284-6.716-15-15-15z"
                        opacity="0.3" fill="currentColor"></path>
                    <path
                        d="m224.495 30h10.042v163.249c-.025.01-.049.02-.073.03v118.728c.12-.001.237-.008.357-.008 9.186 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728 2.874 0 6.987-1.811 11.341-3.728 6.346-2.794 14.244-6.272 23.43-6.272s17.083 3.478 23.43 6.272c4.354 1.917 8.467 3.728 11.341 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 9.187 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 4.47 0 8.705.632 12.73 1.595-4.804-23.468-14.695-45.575-29.291-65.145-18.702-25.076-43.554-44-72.345-55.201v-163.248h10.042c8.284 0 15-6.716 15-15s-6.716-15-15-15h-140.084c-8.284 0-15 6.716-15 15s6.716 15 15 15zm103.509 223.999c8.262 0 15 6.738 15 15s-6.738 15-15 15-15-6.739-15-15c0-8.262 6.738-15 15-15z"
                        opacity="0.3" fill="currentColor"></path>
                    <path
                        d="m459.53 346.224c-5.836-2.253-11.337-4.225-16.087-4.225-2.874 0-6.986 1.811-11.34 3.728-6.346 2.794-14.244 6.272-23.43 6.272s-17.083-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.341-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728-.117 0-.24.017-.357.019v98.971c0 19.39-5.497 37.519-15.008 52.917 22.532 11.564 48.058 18.094 75.08 18.094 90.981 0 165-74.019 165-165 0-.259-.006-.517-.007-.776zm-175.567 56.776c-8.262 0-15-6.739-15-15 0-8.262 6.738-15 15-15s15 6.738 15 15c0 8.261-6.738 15-15 15zm55 29.999c-8.262 0-15-6.738-15-15s6.739-15 15-15c8.262 0 15 6.738 15 15s-6.738 15-15 15z"
                        fill="currentColor"></path>
                    <path
                        d="m62.463 285.999h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30.001h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v4.989c0 39.149 31.851 71 71 71 39.15 0 71-31.851 71-71v-181.989h-142z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Diagnostic Collection</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-diagnostic" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_department'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-department"
                        href="{{ route('department.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Departments </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_diagnostic_test'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-test"
                        href="{{ route('diagnostic.test.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Tests </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-all"
                        href="{{ route('diagnostic.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> All </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_sample_collection_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-sample_collection"
                        href="{{ route('diagnostic.sampleCollection.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Sample Collection </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_home_collection_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-home_collection"
                        href="{{ route('diagnostic.homeCollection.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Home Collection </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_sample_recollection_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-sample_recollection"
                        href="{{ route('diagnostic.sampleCollection.indexRecollection') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Sample ReCollection </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_sample_s_t_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-sample_s_t"
                        href="{{ route('diagnostic.sampleSegregation.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Sample S & T </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_estimation_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-estimation"
                        href="{{ route('diagnostic.estimation.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Estimation List </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['ledger_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-ledger"
                        href="{{ route('diagnostic.ledger.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Ledger </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['edit_barcode_diagnostic'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="diagnostic-edit_barcode"
                        href="{{ route('diagnostic.barcodeEdit.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Barcode Edit </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_templates'], $permissionPage))
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="templates" data-bs-toggle="collapse" href="#manu-templates" role="button"
            aria-expanded="false" aria-controls="manu-templates">
            <i class="icon">
                <svg width="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M10.1528 5.55559C10.2037 5.65925 10.2373 5.77027 10.2524 5.8844L10.5308 10.0243L10.669 12.1051C10.6705 12.3191 10.704 12.5317 10.7687 12.7361C10.9356 13.1326 11.3372 13.3846 11.7741 13.3671L18.4313 12.9316C18.7196 12.9269 18.998 13.0347 19.2052 13.2313C19.3779 13.3952 19.4894 13.6096 19.5246 13.8402L19.5364 13.9802C19.2609 17.795 16.4592 20.9767 12.6524 21.7981C8.84555 22.6194 4.94186 20.8844 3.06071 17.535C2.51839 16.5619 2.17965 15.4923 2.06438 14.389C2.01623 14.0624 1.99503 13.7326 2.00098 13.4026C1.99503 9.31279 4.90747 5.77702 8.98433 4.92463C9.47501 4.84822 9.95603 5.10798 10.1528 5.55559Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M12.8701 2.00082C17.43 2.11683 21.2624 5.39579 22.0001 9.81229L21.993 9.84488L21.9729 9.89227L21.9757 10.0224C21.9652 10.1947 21.8987 10.3605 21.784 10.4945C21.6646 10.634 21.5014 10.729 21.3217 10.7659L21.2121 10.7809L13.5313 11.2786C13.2758 11.3038 13.0214 11.2214 12.8314 11.052C12.6731 10.9107 12.5719 10.7201 12.5433 10.5147L12.0277 2.84506C12.0188 2.81913 12.0188 2.79102 12.0277 2.76508C12.0348 2.55367 12.1278 2.35384 12.2861 2.21023C12.4444 2.06662 12.6547 1.9912 12.8701 2.00082Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Templates</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-templates" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_templates'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="templates-list"
                        href="{{ route('templates.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Prescription Templates </span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_otc_balance'], $permissionPage))
    <li class="nav-item ps-2">
        <a class="nav-link sidebarActive" data-id="otc" aria-current="page"
            href="{{ route('otcBalance.index') }}">
            <i class="icon">
                <svg height="20" viewBox="-44 0 368 368" width="20" xmlns="http://www.w3.org/2000/svg"
                    id="fi_1366276">
                    <path
                        d="m176 0h80v32h-80zm-24 120c.269531-15.847656 7.886719-30.671875 20.617188-40.121094l12.292968-9.597656c1.941406-1.507812 3.078125-3.824219 3.089844-6.28125v-16h56v16c0 2.46875 1.136719 4.804688 3.089844 6.320312l12.292968 9.601563c12.71875 9.4375 20.335938 24.242187 20.617188 40.078125v24h-128zm0 40h128v24c0 4.417969-3.582031 8-8 8h-112c-4.417969 0-8-3.582031-8-8zm0 56c0-4.417969 3.582031-8 8-8h112c4.417969 0 8 3.582031 8 8v16c0 4.417969-3.582031 8-8 8h-112c-4.417969 0-8-3.582031-8-8zm0 48c0-4.417969 3.582031-8 8-8h112c4.417969 0 8 3.582031 8 8v16c0 4.417969-3.582031 8-8 8h-112c-4.417969 0-8-3.582031-8-8zm0 48c0-4.417969 3.582031-8 8-8h112c4.417969 0 8 3.582031 8 8v48c0 4.417969-3.582031 8-8 8h-112c-4.417969 0-8-3.582031-8-8zm0 0"
                        fill="currentColor"></path>
                    <path d="m88 368h-88v-120h88zm0-136h-88v-32h88zm0 0" fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">OTC Balance</span>
        </a>
    </li>
@endif
@if (array_intersect(
        [
            'view_action_module',
            'view_action_page',
            'view_role',
            'view_service',
            'view_membership',
            'create_reward_management',
            'view_setting_csquare',
            'view_campaign',
            'data_migration',
        ],
        $permissionPage))
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="settings" data-bs-toggle="collapse" href="#manu-settings" role="button"
            aria-expanded="false" aria-controls="manu-settings">
            <i class="icon">

                <svg width="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                    class="icon-20">
                    <path opacity="0.4"
                        d="M10.0833 15.958H3.50777C2.67555 15.958 2 16.6217 2 17.4393C2 18.2559 2.67555 18.9207 3.50777 18.9207H10.0833C10.9155 18.9207 11.5911 18.2559 11.5911 17.4393C11.5911 16.6217 10.9155 15.958 10.0833 15.958Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M22.0001 6.37867C22.0001 5.56214 21.3246 4.89844 20.4934 4.89844H13.9179C13.0857 4.89844 12.4102 5.56214 12.4102 6.37867C12.4102 7.1963 13.0857 7.86 13.9179 7.86H20.4934C21.3246 7.86 22.0001 7.1963 22.0001 6.37867Z"
                        fill="currentColor"></path>
                    <path
                        d="M8.87774 6.37856C8.87774 8.24523 7.33886 9.75821 5.43887 9.75821C3.53999 9.75821 2 8.24523 2 6.37856C2 4.51298 3.53999 3 5.43887 3C7.33886 3 8.87774 4.51298 8.87774 6.37856Z"
                        fill="currentColor"></path>
                    <path
                        d="M21.9998 17.3992C21.9998 19.2648 20.4609 20.7777 18.5609 20.7777C16.6621 20.7777 15.1221 19.2648 15.1221 17.3992C15.1221 15.5325 16.6621 14.0195 18.5609 14.0195C20.4609 14.0195 21.9998 15.5325 21.9998 17.3992Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Settings</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-settings" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['data_migration'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-data_migration"
                        href="{{ route('dataMigrate.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Data Migration </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_action_module'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-modules" href="{{ route('module.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Modules </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_action_page'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-pages"
                        href="{{ route('permission.index') }}">
                        <i class="icon svg-icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> D </i>
                        <span class="item-name">Pages</span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_role'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-roles" href="{{ route('role.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Role Management </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_service'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-services"
                        href="{{ route('service.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Services </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_membership'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-membership"
                        href="{{ route('membership.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Memberships </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['create_reward_management'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-reward"
                        href="{{ route('reward.management.addForm') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Reward Management </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_setting_csquare'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-csquare"
                        href="{{ route('settings.pharmacy_sale.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Upload Csquare Daily data </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_campaign'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-campaign"
                        href="{{ route('campaign.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Campaign Master </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_review_survey'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="settings-review_survey_form"
                        href="{{ route('reviewSurvey.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Review & Survey Forms </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(
                    [
                        'view_channel_notification',
                        'view_vendor_notification',
                        'view_category_notification',
                        'view_template_sms_notification',
                    ],
                    $permissionPage))
                <li class="nav-item">
                    <a class="nav-link" data-id="settings-notificationTemplate" data-bs-toggle="collapse"
                        href="#menu-settings-notificationTemplate" role="button" aria-expanded="false"
                        aria-controls="menu-settings-notificationTemplate">
                        <i class="icon">
                            <svg width="20" class="icon-20" height="20" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4"
                                    d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z"
                                    fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2ZM4.53852 13.4655H7.92449C9.32676 13.4655 10.463 14.6114 10.463 16.0255V19.44C10.463 20.8532 9.32676 22 7.92449 22H4.53852C3.13626 22 2 20.8532 2 19.44V16.0255C2 14.6114 3.13626 13.4655 4.53852 13.4655ZM19.4615 13.4655H16.0755C14.6732 13.4655 13.537 14.6114 13.537 16.0255V19.44C13.537 20.8532 14.6732 22 16.0755 22H19.4615C20.8637 22 22 20.8532 22 19.44V16.0255C22 14.6114 20.8637 13.4655 19.4615 13.4655Z"
                                    fill="currentColor"></path>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon" data-bs-toggle="tooltip" data-bs-placement="right"
                            data-bs-original-title="Shop">
                            SP
                        </i>
                        <span class="item-name">Notification Template</span>
                        <i class="right-icon">
                            <svg class="submit icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5l7 7-7 7"></path>
                            </svg>
                        </i>
                    </a>
                    <ul class="sub-nav collapse" id="menu-settings-notificationTemplate"
                        data-bs-parent="#manu-settings" style="">
                        @if (array_intersect(['view_channel_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notificationTemplate-channel"
                                    href="{{ route('notification.channel.index') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Channels </span>
                                </a>
                            </li>
                        @endif
                        @if (array_intersect(['view_vendor_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notificationTemplate-vendor"
                                    href="{{ route('notification.vendor.index') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Vendors </span>
                                </a>
                            </li>
                        @endif
                        @if (array_intersect(['view_category_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notificationTemplate-category"
                                    href="{{ route('notification.category.index') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Categorys </span>
                                </a>
                            </li>
                        @endif
                        @if (array_intersect(['view_template_sms_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notificationTemplate-sms"
                                    href="{{ route('notification.templateSms.index') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Template SMS </span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            @if (array_intersect(
                    ['view_requests_notification', 'view_chunks_notification', 'view_logs_notification'],
                    $permissionPage))
                <li class="nav-item">
                    <a class="nav-link" data-id="settings-notification" data-bs-toggle="collapse"
                        href="#menu-settings-notification" role="button" aria-expanded="false"
                        aria-controls="menu-settings-notification">
                        <i class="icon">
                            <svg width="20" class="icon-20" height="20" viewBox="0 0 24 24"
                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4"
                                    d="M16.0756 2H19.4616C20.8639 2 22.0001 3.14585 22.0001 4.55996V7.97452C22.0001 9.38864 20.8639 10.5345 19.4616 10.5345H16.0756C14.6734 10.5345 13.5371 9.38864 13.5371 7.97452V4.55996C13.5371 3.14585 14.6734 2 16.0756 2Z"
                                    fill="currentColor"></path>
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M4.53852 2H7.92449C9.32676 2 10.463 3.14585 10.463 4.55996V7.97452C10.463 9.38864 9.32676 10.5345 7.92449 10.5345H4.53852C3.13626 10.5345 2 9.38864 2 7.97452V4.55996C2 3.14585 3.13626 2 4.53852 2ZM4.53852 13.4655H7.92449C9.32676 13.4655 10.463 14.6114 10.463 16.0255V19.44C10.463 20.8532 9.32676 22 7.92449 22H4.53852C3.13626 22 2 20.8532 2 19.44V16.0255C2 14.6114 3.13626 13.4655 4.53852 13.4655ZM19.4615 13.4655H16.0755C14.6732 13.4655 13.537 14.6114 13.537 16.0255V19.44C13.537 20.8532 14.6732 22 16.0755 22H19.4615C20.8637 22 22 20.8532 22 19.44V16.0255C22 14.6114 20.8637 13.4655 19.4615 13.4655Z"
                                    fill="currentColor"></path>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon" data-bs-toggle="tooltip" data-bs-placement="right"
                            data-bs-original-title="Shop">
                            SP
                        </i>
                        <span class="item-name">Notification</span>
                        <i class="right-icon">
                            <svg class="submit icon-18" xmlns="http://www.w3.org/2000/svg" width="18"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5l7 7-7 7"></path>
                            </svg>
                        </i>
                    </a>
                    <ul class="sub-nav collapse" id="menu-settings-notification" data-bs-parent="#manu-settings"
                        style="">
                        @if (array_intersect(['view_requests_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notification-requests"
                                    href="{{ route('notification.requestList') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Requests </span>
                                </a>
                            </li>
                        @endif
                        @if (array_intersect(['view_chunks_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notification-chunks"
                                    href="{{ route('notification.chunkList') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Chunks </span>
                                </a>
                            </li>
                        @endif
                        @if (array_intersect(['view_logs_notification'], $permissionPage))
                            <li class="nav-item">
                                <a class="nav-link sidebarActive" data-id="settings-notification-logs"
                                    href="{{ route('notification.logList') }}">
                                    <i class="icon">
                                        <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                            viewBox="0 0 24 24" fill="currentColor">
                                            <g>
                                                <circle cx="12" cy="12" r="8" fill="currentColor">
                                                </circle>
                                            </g>
                                        </svg>
                                    </i>
                                    <i class="sidenav-mini-icon"> H </i>
                                    <span class="item-name"> Logs </span>
                                </a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_otc_utilization'], $permissionPage))
    <li class="nav-item ps-2">
        <a class="nav-link" data-id="reports" data-bs-toggle="collapse" href="#manu-reports" role="button"
            aria-expanded="false" aria-controls="manu-reports">
            <i class="icon">
                <svg width="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M10.1528 5.55559C10.2037 5.65925 10.2373 5.77027 10.2524 5.8844L10.5308 10.0243L10.669 12.1051C10.6705 12.3191 10.704 12.5317 10.7687 12.7361C10.9356 13.1326 11.3372 13.3846 11.7741 13.3671L18.4313 12.9316C18.7196 12.9269 18.998 13.0347 19.2052 13.2313C19.3779 13.3952 19.4894 13.6096 19.5246 13.8402L19.5364 13.9802C19.2609 17.795 16.4592 20.9767 12.6524 21.7981C8.84555 22.6194 4.94186 20.8844 3.06071 17.535C2.51839 16.5619 2.17965 15.4923 2.06438 14.389C2.01623 14.0624 1.99503 13.7326 2.00098 13.4026C1.99503 9.31279 4.90747 5.77702 8.98433 4.92463C9.47501 4.84822 9.95603 5.10798 10.1528 5.55559Z"
                        fill="currentColor"></path>
                    <path opacity="0.4"
                        d="M12.8701 2.00082C17.43 2.11683 21.2624 5.39579 22.0001 9.81229L21.993 9.84488L21.9729 9.89227L21.9757 10.0224C21.9652 10.1947 21.8987 10.3605 21.784 10.4945C21.6646 10.634 21.5014 10.729 21.3217 10.7659L21.2121 10.7809L13.5313 11.2786C13.2758 11.3038 13.0214 11.2214 12.8314 11.052C12.6731 10.9107 12.5719 10.7201 12.5433 10.5147L12.0277 2.84506C12.0188 2.81913 12.0188 2.79102 12.0277 2.76508C12.0348 2.55367 12.1278 2.35384 12.2861 2.21023C12.4444 2.06662 12.6547 1.9912 12.8701 2.00082Z"
                        fill="currentColor"></path>
                </svg>
            </i>
            <span class="item-name">Reports</span>
            <i class="right-icon">
                <svg class="icon-18" xmlns="http://www.w3.org/2000/svg" width="18" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </i>
        </a>
        <ul class="sub-nav collapse" id="manu-reports" data-bs-parent="#sidebar-menu">
            @if (array_intersect(['view_otc_utilization'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-otc"
                        href="{{ route('report.OtcUtilization.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> OTC Utilization Report </span>
                    </a>
                </li>
            @endif

            @if (array_intersect(['view_membership_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-membership"
                        href="{{ route('report.membershipDetails.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Membership Report </span>
                    </a>
                </li>
            @endif


            @if (array_intersect(['view_appointment_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-appointment"
                        href="{{ route('report.appointment.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Appointment Report </span>
                    </a>
                </li>
            @endif

            @if (array_intersect(['view_schedulewisedoctor_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-scheduledoctor"
                        href="{{ route('report.sechedulewisedoctor.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name"> Schedule wise Doctor </span>
                    </a>
                </li>
            @endif


            @if (array_intersect(['view_medicineorder_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-medicineorder"
                        href="{{ route('report.medicineorder.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name">Medicine Order Report </span>
                    </a>
                </li>
            @endif


            @if (array_intersect(['lead_generation_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-leadgenerationreport"
                        href="{{ route('report.leadgenerationreport.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name">Lead Generation Report </span>
                    </a>
                </li>
            @endif

            @if (array_intersect(['view_diagnostic_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-diagnosticreport"
                        href="{{ route('report.diagnosticreport.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name">Diagnostic Report </span>
                    </a>
                </li>
            @endif
            @if (array_intersect(['view_sample_collection_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-samplecollectionreport"
                        href="{{ route('report.samplecollectionreport.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name">Sample Collection Report</span>
                    </a>
                </li>
            @endif

            @if (array_intersect(['view_download_report'], $permissionPage))
                <li class="nav-item">
                    <a class="nav-link sidebarActive" data-id="reports-downloadreport"
                        href="{{ route('report.downloadreport.index') }}">
                        <i class="icon">
                            <svg class="icon-10" xmlns="http://www.w3.org/2000/svg" width="10"
                                viewBox="0 0 24 24" fill="currentColor">
                                <g>
                                    <circle cx="12" cy="12" r="8" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </i>
                        <i class="sidenav-mini-icon"> H </i>
                        <span class="item-name">Download Report</span>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif
@if (array_intersect(['view_footer_capture_vitals'], $permissionPage))
    <script>
        $('#captureVitals').show();
    </script>
@endif
