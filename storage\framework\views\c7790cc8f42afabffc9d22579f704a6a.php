<?php $__env->startSection('title'); ?>
    <?php echo e(config('membership.title_otc', 'Otc Balance')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">OTC Balance</h4>
                </div>

                <div class="header-action">

                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>
                    <div class="col-md-9">
                        <div class="row justify-content-end">
                            <div class="col-md-4">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="0">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Date
                                </th>
                                <th>
                                    Patient
                                </th>
                                <th>
                                    Phone
                                </th>
                                <th>
                                    Valid upto
                                </th>
                                <th>
                                    Membership No
                                </th>
                                <th>
                                    Membership Type
                                </th>
                                <th>
                                    Token No
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo $__env->make('admin.custom.loading', ['td' => 9, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tbody>
                        <tfoot>
                            <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Membership membership Info</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        function updateStatusMembership(url, method, parent) {
            let status = $(parent).attr('data-stat');
            let changeStatus = (status == 1 ? 3 : 1);
            let title = 'Membership '+(status == 3 ? 'Deactive' : 'Active');
            swal({
                    title: 'Do you want to '+(status == 1 ? 'Deactive' : 'Active')+'?',
                    // text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes'
                },
                function(isConfirm) {
                    if (isConfirm) {
                        // console.log(url,method);
                        if (!jwtToken) {
                            redirectToSwal(loginUrl);
                            return false;
                        }
                        $(parent).attr('data-stat', changeStatus);
                        $(parent).attr('title', title);

                        $.ajax({
                            type: method,
                            url: url,
                            crossDomain: true,
                            dataType: 'json',
                            cache: false, // Corrected from 'catch' to 'cache'
                            contentType: "application/json",
                            headers: {
                                'Accept': 'application/json',
                                'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                            },
                            data: JSON.stringify({ status: status }), // Convert data object to JSON string
                            processData: false, // Prevent jQuery from automatically transforming the data into a query string
                            success: function (data) {
                                console.log(data);
                            },
                            error: function (response) {
                                console.error(response); // Log the error response for debugging
                                // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                            }
                        });
                    }
                    else{
                        return false;
                    }
                }
            );
        }
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('membership.url_otc') . 'list'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id"
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {

                },
                "with": {
                    "membershipRegistrations":"id,patient_id,phone,start_date,end_date,registration_no",
                    "membershipRegistrations.patients":"id,name",
                    "membershipRegistrations.memberships":"id,name"
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Membership\resources/views/otcBalance/index.blade.php ENDPATH**/ ?>