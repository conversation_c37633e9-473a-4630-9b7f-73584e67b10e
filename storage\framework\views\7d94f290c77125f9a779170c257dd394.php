<?php $__env->startSection('title'); ?>
    <?php echo e(config('patient.title', 'Patient Search')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="">
                            <div class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                                <div class="header-title">
                                    <h5 class="h5 mb-0">Search Patient</h5>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row justify-content-center">
                                    <div class="form-group col-lg-6 col-md-6 col-12">
                                        <div class="row row-cols-auto align-items-center justify-content-center">
                                            <label for="exampleInputEmail1"
                                                class="fw-bold col-sm-12 form-label p-0 text-center">Search with Patient
                                                Name or Phone No</label>
                                            <div class="col-12 col-sm-12">
                                                <input type="text" class="form-control" name="search_patient"
                                                    id="search_patient" placeholder="Search">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-1 justify-content-center mb-5">
                                        <button onclick="searchPatient()" type="button" name="button"
                                            class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Search</button>
                                    </div>
                                    <div class="col-lg-12 col-md-12 col-12">
                                        <div class="Table-custom-padding1 table-responsive">
                                            <table id="data-list-family" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table" style="display: none;">
                                                <thead>
                                                    <tr>
                                                        <th>
                                                            Name
                                                        </th>
                                                        <th>
                                                            Gender
                                                        </th>
                                                        <th>
                                                            Membership
                                                        </th>
                                                        <th>
                                                            Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php echo $__env->make('admin.custom.loading', ['td' => 4, 'action' => 1], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="5" class="border-0">
                                                            <button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center placeholder"
                                                                type="button"></button>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body placeholder-glow data-add" id="data-add-patient" style="display: none;">
                            <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4,4,4,4,4,4,4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                        <div class="card-body placeholder-glow data-add" id="data-add-member" style="display: none;">
                            <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4,4,4,4,4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            refreshToken();
        });
        let ht_family_list = $('#data-list-family tbody').html();
        let ht_patient = $('#data-add-patient').html();
        let ht_member = $('#data-add-member').html();
        $(document).ready(function() {
            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
        function searchPatient() {
            let phone = $('#search_patient').val();
            if (phone == '') {
                alert('Please enter phone number');
                return false;
            }
            const validationErrors = validatePhoneNumber(phone);            
            if (Object.keys(validationErrors).length > 0) {
                alert(validationErrors.phone);
                return false;
            }
            // $('#data-add-edit').html('');
            $('.data-add').hide();
            let datajson = {
                "phone": phone
            };
            $('#data-list-family tbody').html(ht_family_list);
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('patient.search_url') . 'listFamily'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#data-list-family').show();
                },
                success: function(data) {
                    $('#data-list-family tbody').html(data.tbody);
                    $('#data-list-family tfoot').html(data.tfoot);
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }
        function showForm(stat,parent_id,phone) {
            $('.data-add').hide();
            let redirectUrl = "";
            setRedirectUrl(redirectUrl);
            $('#data-add-patient').html(ht_patient);
            $('#data-add-member').html(ht_member);
            if (stat == 0) {
                let ht_id = '#data-add-member';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'addFamily/'); ?>"+parent_id+"?stat=1";
                setListUrl(url); // api url for show table
            }
            else{
                let ht_id = '#data-add-patient';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'create'); ?>"+"?parent_id="+parent_id+"&phone="+phone;
                setListUrl(url); // api url for show table
            }
            $(ht_id).show();
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }
        function resChildHtml(data){
            console.log(data);//res_data
            let patient_id = data.res_data.id;
            let phone = $('#search_patient').val();
            searchPatient();
            $('#data-add-patient').hide();
            $('#data-add-member').hide();
            $('#data-add-patient').html(ht_patient);
            $('#data-add-member').html(ht_member);
            // proceedPayment(patient_id,phone,0);
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Patient\resources/views/patientSearch/add.blade.php ENDPATH**/ ?>