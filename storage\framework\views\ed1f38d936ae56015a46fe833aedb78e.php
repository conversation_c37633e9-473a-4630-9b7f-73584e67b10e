<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['request_id']); ?></td>
        <td><?php echo e($row['vendor_response'] ? json_decode($row['vendor_response'])->message : ''); ?></td>
        <td>
            <?php echo e($row['status'] == 1 ? 'Active' : 'Inactive'); ?>

        </td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Notification\resources/views/notification/api/chunkList.blade.php ENDPATH**/ ?>