<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;

class SchedulewiseDoctorReportExport implements FromView, ShouldAutoSize
{
    protected $data;

    // Accept data via constructor
    public function __construct($data)
    {
        // dd($data,'hii');
        $this->data = $data;
    }
    public function view(): View
    {
        $data = $this->data;
        // dd($data);
        return view('report::schedulewisedoctor.export.scheduleWiseDoctor', compact('data'));
    }
}
