<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\SampleCollectionBreakupBill;
use Modules\Report\Models\Clinic;
use DB;
use Carbon\Carbon;


class DiagnosticBreakupBillService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;
    public $columns = [
        'id',
        'sample_collection_id',
        'item_id',
        'home_collection_id',
        'amount',
        'discount',
        'net_amount',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'sample_collection_id',
        'item_id',
        'home_collection_id',
        'amount',
        'discount',
        'net_amount',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(SampleCollectionBreakupBill $entity,Clinic $entityClinic) {
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
    }
    public function allSampleCollectionBreakupBills(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }

     public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
 
}
