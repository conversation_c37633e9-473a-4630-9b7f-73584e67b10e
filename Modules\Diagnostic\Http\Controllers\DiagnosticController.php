<?php
// updatelabstatus
// updatelabReport
namespace Modules\Diagnostic\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Diagnostic\Http\Requests\DiagnosticRequest;
use Modules\Diagnostic\Http\Requests\DiagnosticWithPhleboRequest;
use Modules\Diagnostic\Http\Requests\DiagnosticDisputeRequest;
use Modules\Diagnostic\Http\Requests\AssignPhleboRequest;
use Modules\Diagnostic\Http\Requests\PaymentSettlementRequest;
use Modules\Diagnostic\Services\DiagnosticService;
use Modules\Diagnostic\Services\DiagnosticTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupTestService;
use Modules\Diagnostic\Services\DiagnosticBreakupBillService;
use Modules\Diagnostic\Services\DiagnosticPhleboAssignmentService;
use Modules\Doctor\Services\DoctorService;
use Modules\Patient\Services\PatientService;
use Modules\Reward\Services\RewardService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Users\Services\UserService;
use Modules\Diagnostic\Services\EstimationService;
use Modules\Diagnostic\Services\DiagnosticApiService;
use Modules\Appointment\Services\AppointmentService;
use Modules\Diagnostic\Services\MapSourceService;
use App\Services\SMSMsgService;
use App\Services\ImageUploadService;
use Modules\Diagnostic\Jobs\DiagnosticApiJob;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use DB;
use PDF;
use Carbon\Carbon;

class DiagnosticController extends Controller
{
    private $diagnosticService;
    private $diagnosticTestService;
    private $diagnosticBreakupTestService;
    private $diagnosticBreakupBillService;
    private $patientService;
    private $smsMsgService;
    private $imageUploadService;
    private $rewardService;
    private $paymentService;
    private $paymentBillService;
    private $paymentDetailService;
    private $doctorService;
    private $userService;
    private $diagnosticPhleboAssignmentService;
    private $estimationService;
    private $diagnosticApiService;
    private $appointmentService;
    private $mapSourceService;
    

    public function __construct(
        DiagnosticService $diagnosticService, DiagnosticTestService $diagnosticTestService, 
        PatientService $patientService, SMSMsgService $smsMsgService, ImageUploadService $imageUploadService, 
        RewardService $rewardService, DiagnosticBreakupTestService $diagnosticBreakupTestService, 
        DiagnosticBreakupBillService $diagnosticBreakupBillService, PaymentBillService $paymentBillService, 
        PaymentService $paymentService, PaymentDetailService $paymentDetailService, DoctorService $doctorService, 
        UserService $userService, DiagnosticPhleboAssignmentService $diagnosticPhleboAssignmentService, 
        EstimationService $estimationService, DiagnosticApiService $diagnosticApiService, AppointmentService $appointmentService,
        MapSourceService $mapSourceService
    )
    {
        $this->diagnosticService = $diagnosticService;
        $this->diagnosticTestService = $diagnosticTestService;
        $this->diagnosticBreakupTestService = $diagnosticBreakupTestService;
        $this->diagnosticBreakupBillService = $diagnosticBreakupBillService;
        $this->patientService = $patientService;
        $this->smsMsgService = $smsMsgService;
        $this->imageUploadService = $imageUploadService;
        $this->rewardService = $rewardService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->doctorService = $doctorService;
        $this->userService = $userService;
        $this->diagnosticPhleboAssignmentService = $diagnosticPhleboAssignmentService;
        $this->estimationService = $estimationService;
        $this->diagnosticApiService = $diagnosticApiService;
        $this->appointmentService = $appointmentService;
        $this->mapSourceService = $mapSourceService;
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
            'visit_type' => config('diagnostic.visit_type'),
        ];
        // dd($data);
        return view('diagnostic::diagnostic.index',compact('data'));
    }
    public function labTestStatus($id,Request $request)
    {
        $id = base64_decode($id);
        $data = [];
        // dd($id);
        return view('diagnostic::diagnostic.labTestStatus',compact('id','data'));
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $patient_id = $id ? $this->diagnosticService->findById($id)->patient_id : null;
        // dd($patient_id);
        $list = [
            'offer_not_avil' => $this->diagnosticService->offerNotAvil()
        ];
        // dd($list);
        // pid=64872&phone=**********&service=Arogya%20Bandhu
        return view('diagnostic::diagnostic.add',compact('id','patient_id','list'));
    }
    
    public function phleboAssign($id)
    {
        $list = [];
        return view('diagnostic::diagnostic.phleboAssign',compact('id','list'));
    }
    public function paymentSettlement(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $list = [];
        return view('diagnostic::diagnostic.paymentSettlement',compact('id','list'));
    }
    public function refundSettlement(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $list = [];
        return view('diagnostic::diagnostic.refundSettlement',compact('id','list'));
    }
    public function indexSampleStatus(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
            'test_status_list' => config('diagnostic.test_status_list'),
        ];
        return view('diagnostic::diagnostic.sampleStatus',compact('data'));
    }
    public function list(Request $request)
    {
        try {
            $role = $this->getUserRole();
            $filter = $request['filter'];
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            // dd($clinic_id);
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $request->merge([
                'filter' => $filter
            ]);
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ],
                'clinics' => [
                    'reletion' => [
                        'prefix' => 'clinic_id',
                        'suffix' => 'id'
                    ]
                ],
                // 'users' => [
                //     'reletion' => [
                //         'prefix' => 'phlebo_id',
                //         'suffix' => 'id'
                //     ]
                // ]
            ];
            // $request['with'] = 'assignPhlebotomists';
            $request['with'] = [
                'tests' => 'id,sample_collection_id,item_id',
                'diagnosticBreakupItem' => 'id,sample_collection_id',
                'diagnosticBreakupReportedTest' => 'id,sample_collection_id',
                'assignPhlebotomists' => 'id,sample_id,phlebo_id,schedule_time,remarks,created_at',
            ];
            $request['withFunc'] = [
                // 'diagnosticBill' => [
                //     'method' => 'withSum',
                //     'column' => 'net_amount'
                // ],
                'paymentBill as total_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'total_amount'
                ],
                'paymentBill as due_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'due_amount'
                ]
            ];
            array_push(
                $this->diagnosticService->columns,
                'patients.name as patient_name',
                // 'users.username as username',
                'clinics.clinic_name as clinic_name'
                // DB::raw('SUM(sample_collection_breakup_bills.net_amount) as total_amount')
            );
            $this->diagnosticService->setRequest($request);
            $data = $this->diagnosticService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticService->getRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            $itdose_report_url = config('diagnostic.itdose_report_url');
            // dd($status_list);
            $this->response['tbody'] = view('diagnostic::diagnostic.api.list',compact('data','permissionPage','status_list','itdose_report_url'))->render();
            $this->response['tfoot'] = $this->diagnosticService->paginationCustom();
            $this->response['headerAction'] = view('diagnostic::diagnostic.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function labTestMaster(Request $request)
    {
        try {
            $id = $request->id ? $request->id : null;
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            
            $paymentBill = $diagnostic->paymentBill;
            // dd($paymentBill);
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                // 'patient_membership' => $diagnostic->patient->membershipRegistrations[0]->registration_no ?? '',
                'total_amount' => $paymentBill->total_amount,
                'paid_amount' => $paymentBill->paid_amount,
                'due_amount' => $paymentBill->due_amount,
            ];
            
            // dd($data['patient_detail']);
            $this->response['detail'] = view('diagnostic::diagnostic.api.labTestMaster',compact('id','data'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function labTestList(Request $request)
    {
        try {
            $request['join'] = [
                'tests' => [
                    'reletion' => [
                        'prefix' => 'test_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            
            array_push(
                $this->diagnosticBreakupTestService->columns,
                'tests.test_name as test_name',
            );
            $this->diagnosticBreakupTestService->setRequest($request);
            $data = $this->diagnosticBreakupTestService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticBreakupTestService->getRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            $itdose_report_url = config('diagnostic.itdose_report_url');
            $this->response['tbody'] = view('diagnostic::diagnostic.api.labTestList',compact('data','permissionPage','status_list','itdose_report_url'))->render();
            $this->response['tfoot'] = $this->diagnosticBreakupTestService->paginationCustom();
            // $this->response['headerAction'] = view('diagnostic::diagnostic.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listFamily(Request $request)
    {
        $this->response['success']  = true;
        $phone = $request->phone;
        $this->response['data'] = [];
        $list = [
            'patients' => $this->diagnosticService->getFamilys($phone)
        ];
        $parent_id = $this->diagnosticService->getPatientID($phone);
        if (!isset($parent_id)) {
            $parent_id = 0;
        }
        // dd($phone,$parent_id,$list);
        $html = '<tr>';
            $html .= '<td colspan="5" class="border-0">';
            if (count($list['patients']) > 0) {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(0,'.$parent_id.','.$request->phone.')">Add Member</button>';
            }
            else {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(1,'.$parent_id.','.$request->phone.')">Add Patient</button>';
            }
            $html .= '</td>';
        $html .= '</tr>';
        $permissionPage = $this->getPermissionList();
        // dd($list['patients'],$list['patients'][0]->membershipRegistrations[0]->memberships);
        $this->response['tbody'] = view('diagnostic::diagnostic.api.listFamily',compact('list','parent_id','phone','permissionPage'))->render();
        $this->response['tfoot'] = $html;
        return response()->json($this->response);
    }
    public function create(Request $request)
    {
        try {
            $id = null;
            $s_id = $request->s_id ? $request->s_id : null;
            $type = $request->type ? $request->type : null;
            // dd($s_id,$type);
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $service = $request->service;
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $user_clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $user_clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $user_clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            $source_list = [];
            if ($s_id) {
                if ($type == 'EST') {
                    $estimate_list = $this->estimationService->findById($s_id) ?? null;
                    $patient_id = $estimate_list->patient_id ?? null;
                    $phone = $this->patientService->getParentPhone($patient_id);
                    $service = $estimate_list->patient->membershipRegistrations[0]->memberships->name ?? '';
                    $source_list = [
                        'source_id' => $estimate_list->id ?? null,
                        'source_type' => $type,
                        'source_clinic_id' => $estimate_list->clinic_id ?? null,
                        'source_test_id' => explode(',',$estimate_list->test_id) ?? null,
                        'hc_address' => [
                            'full_address' => $estimate_list->full_address ?? null,
                            'landmark' => $estimate_list->land_mark ?? null,
                            'city' => $estimate_list->city ?? null,
                            'pincode' => $estimate_list->pincode ?? null,
                        ]
                    ];
                    // dd($source_list);
                }
                else if ($type == 'OPD') {
                    $appointment_list = $this->appointmentService->findById($s_id) ?? null;
                    $patient_id = $appointment_list->patient_id ?? null;
                    $phone = $this->patientService->getParentPhone($patient_id);
                    $service = $appointment_list->patients->membershipRegistrations[0]->memberships->name ?? '';
                    $source_list = [
                        'source_id' => $appointment_list->id ?? null,
                        'source_type' => $type,
                        'source_clinic_id' => $appointment_list->clinic_id ?? null,
                        'source_test_id' => $appointment_list->prescription->prescriptionChildTests->pluck('type_id')->toArray() ?? null,
                        'hc_address' => []
                    ];
                    // dd($source_list);
                }
                // else if ($type == 'Dispute') {
                //     $diagnostic_list = $this->diagnosticService->findById($s_id) ?? null;
                //     $patient_id = $diagnostic_list->patient_id ?? null;
                //     $phone = $this->patientService->getParentPhone($patient_id);
                //     $service = $diagnostic_list->patient->membershipRegistrations[0]->memberships->name ?? '';
                //     $source_list = [
                //         'source_id' => $diagnostic_list->id ?? null,
                //         'source_type' => $type,
                //         'source_clinic_id' => $diagnostic_list->clinic_id ?? null,
                //         'source_test_id' => $diagnostic_list->diagnosticBill->pluck('item_id')->toArray() ?? null,
                //         'hc_address' => []
                //     ];
                //     // dd($source_list);
                // }
            }
            // dd($patient_id,$phone,$service);
            $patient = $this->patientService->findById($patient_id);
            if (!$patient) {
                $this->response['success']  = false;
                $this->response['message']  = 'Patient not found!';
                return response()->json($this->response);
            }
            // dd($patient);
            if(empty($patient->uhid_no))
            {
                $uhid_no = "mymd" . rand(100000, 999999);
                $data = [
                    'uhid_no'=> $uhid_no,
                    'modified_by' => $this->modifiedBy()
                ];
                $this->patientService->setRequest($data);
                $this->patientService = $this->patientService->update();
            }
            // dd($patient);
            $this->response['success']  = true;
            // dd($otc_check);
            $data = [
                'patient_detail' => $patient
            ];
            // dd($data['patient_detail']['uhid_no']);
            $distance_list = $this->diagnosticService->allSampleHomecollectionCharges()->toArray();
            $offer_list = [];
            // offer not avil test
            // offer for Membership
            $offer_list = $this->diagnosticService->membershipOffer($patient,$offer_list);
            // offer for Employee
            $offer_list = $this->diagnosticService->employeeOffer($patient,$offer_list);
            // offer for Campaign
            $offer_list = $this->diagnosticService->campaignOffer($patient,$offer_list);
            // dd($offer_list);

            $list = [
                'visit_type' => config('diagnostic.visit_type'),
                'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                'user_clinic_id' => $user_clinic_id,
                'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                'test_list' => $this->diagnosticService->allTests()->toArray(),
                'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'reward_points' => $this->rewardService->rewardPoints($phone),
                'offer_list' => $offer_list,
                'source_list' => $source_list
            ];
            // dd($list['offer_list']);
            // if ($appointment_type == 2) {
            //     $list['tc_clinic_list'] = $this->diagnosticService->allClinics();
            // }
            $this->response['form'] = view('diagnostic::diagnostic.api.addEdit',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function createWithPhlebo(Request $request)
    {
        try {
            $id = null;
            $s_id = $request->s_id ? $request->s_id : null;
            $type = $request->type ? $request->type : null;
            // dd($s_id,$type);
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $service = $request->service;
            $user_clinic_id = null;
            $source_list = [];
            if ($s_id) {
                if ($type == 'EST') {
                    $estimate_list = $this->estimationService->findById($s_id) ?? null;
                    $patient_id = $estimate_list->patient_id ?? null;
                    $phone = $this->patientService->getParentPhone($patient_id);
                    $service = $estimate_list->patient->membershipRegistrations[0]->memberships->name ?? '';
                    $source_list = [
                        'source_id' => $estimate_list->id ?? null,
                        'source_type' => $type,
                        'source_clinic_id' => $estimate_list->clinic_id ?? null,
                        'source_test_id' => explode(',',$estimate_list->test_id) ?? null,
                        'hc_address' => [
                            'full_address' => $estimate_list->full_address ?? null,
                            'landmark' => $estimate_list->land_mark ?? null,
                            'city' => $estimate_list->city ?? null,
                            'pincode' => $estimate_list->pincode ?? null,
                        ]
                    ];
                    // dd($source_list);
                }
                else if ($type == 'OPD') {
                    $appointment_list = $this->appointmentService->findById($s_id) ?? null;
                    $patient_id = $appointment_list->patient_id ?? null;
                    $phone = $this->patientService->getParentPhone($patient_id);
                    $service = $appointment_list->patients->membershipRegistrations[0]->memberships->name ?? '';
                    $source_list = [
                        'source_id' => $appointment_list->id ?? null,
                        'source_type' => $type,
                        'source_clinic_id' => $appointment_list->clinic_id ?? null,
                        'source_test_id' => $appointment_list->prescription->prescriptionChildTests->pluck('type_id')->toArray() ?? null,
                        'hc_address' => []
                    ];
                    // dd($source_list);
                }
            }
            // dd($patient_id,$phone,$service);
            $patient = $this->patientService->findById($patient_id);
            if (!$patient) {
                $this->response['success']  = false;
                $this->response['message']  = 'Patient not found!';
                return response()->json($this->response);
            }
            // dd($patient);
            if(empty($patient->uhid_no))
            {
                $uhid_no = "mymd" . rand(100000, 999999);
                $data = [
                    'uhid_no'=> $uhid_no,
                    'modified_by' => $this->modifiedBy()
                ];
                $this->patientService->setRequest($data);
                $this->patientService = $this->patientService->update();
            }
            // dd($patient);
            $this->response['success']  = true;
            // dd($otc_check);
            $data = [
                'patient_detail' => $patient
            ];
            // dd($data['patient_detail']['uhid_no']);
            $distance_list = $this->diagnosticService->allSampleHomecollectionChargesForCC()->toArray();
            $offer_list = [];
            // offer not avil test
            // offer for Membership
            $offer_list = $this->diagnosticService->membershipOffer($patient,$offer_list);
            // offer for Employee
            $offer_list = $this->diagnosticService->employeeOffer($patient,$offer_list);
            // offer for Campaign
            $offer_list = $this->diagnosticService->campaignOffer($patient,$offer_list);

            $offer_list = array_filter($offer_list, function ($offer) {
                return $offer['status'] == 0;
            });
            $offer_list = array_values($offer_list);
            // dd($offer_list);

            $list = [
                'visit_type' => config('diagnostic.visit_type'),
                'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                'user_clinic_id' => $user_clinic_id,
                'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                'test_list' => $this->diagnosticService->allTests()->toArray(),
                'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'reward_points' => $this->rewardService->rewardPoints($phone),
                'offer_list' => $offer_list,
                'source_list' => $source_list
            ];
            // dd($list['offer_list']);
            // if ($appointment_type == 2) {
            //     $list['tc_clinic_list'] = $this->diagnosticService->allClinics();
            // }
            $this->response['form'] = view('diagnostic::diagnostic.api.addEditWithPhlebo',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function getPhleboWithAssignCount(Request $request)
    {
        try {
            $pincode = $request->pincode;
            $date_of_collection = $request->date_of_collection;
            $selectedRadioId = $request->selectedRadioId;
            $phlebo_list = $this->diagnosticService->getPhleboWithAssignCount($pincode,$date_of_collection);
            // dd($phlebo_list,$selectedRadioId);
            $this->response['success']  = true;
            $this->response['tbody'] = view('diagnostic::diagnostic.api.phleboList',compact('phlebo_list','selectedRadioId'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function createDispute(Request $request)
    {
        try {
            $id = null;
            $s_id = $request->s_id ? $request->s_id : null;
            $type = $request->type ? $request->type : null;
            $source_list = [];
            if ($s_id) {
                if ($type == 'Dispute') {
                    $diagnostic_list = $this->diagnosticService->findById($s_id) ?? null;
                    $patient_id = $diagnostic_list->patient_id ?? null;
                    $phone = $this->patientService->getParentPhone($patient_id);
                    $service = $diagnostic_list->patient->membershipRegistrations[0]->memberships->name ?? '';
                    $source_list = [
                        'source_id' => $diagnostic_list->id ?? null,
                        'source_type' => $type,
                        // 'source_clinic_id' => $diagnostic_list->clinic_id ?? null,
                        // 'source_test_id' => $diagnostic_list->diagnosticBillItem->pluck('item_id')->toArray() ?? null,
                        // 'hc_address' => [],
                        'diagnostic' => $diagnostic_list
                    ];
                    // dd($source_list);
                }
            }
            $diagnostic = $this->diagnosticService->findById($source_list['source_id'] ?? null);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $user_clinic_id = null;
            // dd($diagnostic);
            $paymentBill = $diagnostic->paymentBill;
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient ?? null,
                'diagnosticBill' => $diagnostic->diagnosticBill ,
                'diagnosticBillHC' => $diagnostic->diagnosticBillHC ?? null,
                'diagnosticBillItem' => $diagnostic->diagnosticBillItem ?? null,
                'diagnosticBreakupItemMergePackage' => $diagnostic->diagnosticBreakupItemMergePackage() ?? null,
                'diagnosticBreakupItemGroup' => $diagnostic->diagnosticBreakupItemGroup ?? null,
                'diagnosticPayment' => $paymentBill->payments ?? null,
                'reward_redeem' => $paymentBill->payments->sum('redeem_points') ?? 0,
            ];
            // dd($data['diagnosticBillItem'],$data['diagnosticBreakupItemGroup']);//package_id,report_delivery_date,is_urgent
            $patient_id = $diagnostic->patient_id;
            $phone = $diagnostic->patient_phone;
            // dd($data['patient_detail']->membershipRegistrations);
            $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['diagnostic']->doctor->first_name);
            $distance_list = $this->diagnosticService->allSampleHomecollectionCharges()->toArray();
            // dd($data['diagnostic']->offered_type,$data['diagnostic']->offered_id);
            // 1:membership,2:campaign,3:employee
            $offer_list = [];
            // offer for Dispute
            $offer_list = $this->diagnosticService->disputeOffer($data['patient_detail'],$offer_list);
            // dd($offer_list);
            $list = [
                'visit_type' => config('diagnostic.visit_type'),
                'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                'user_clinic_id' => $user_clinic_id,
                'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                'test_list' => $this->diagnosticService->allTests()->toArray(),
                'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'offer_list' => $offer_list,
                'source_list' => $source_list
            ];
            $this->response['form'] = view('diagnostic::diagnostic.api.addEditDispute',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function testItemList(Request $request)
    {
        try {
            $test_id = $request->test_id;

            $test = $this->diagnosticTestService->findById($test_id);
            if (!$test) {
                $this->response['success']  = false;
                $this->response['message']  = 'Test not found!';
                return response()->json($this->response);
            }
            
            $this->response['success']  = true;
            $delivery_date = date("Y-m-d H:i:s", strtotime('+'.$test->delivery_date.' hours'));
            $delivery_date = $this->diagnosticService->checkLabTiming($delivery_date);
            // dd($test->delivery_date,$delivery_date);
            $data = [
                'test' => $test,
                'discount' => 0,
                'delivery_date' => $delivery_date,
                'is_urgent' => 0,
                'package_test_list' => $test->is_package == 2 ? $test->packageTest($test->package_test) : [],
            ];
            // dd($data);
            $this->response['data'] = $data;
            $this->response['pkg_test_list'] = $test->is_package == 2 ? json_decode($test->package_test) : [];
            $this->response['form'] = view('diagnostic::diagnostic.api.testItemList',compact('data'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function add(DiagnosticRequest $request)
    {
        // dd($request->all());
        try {
            $request->validated();
            $bill_id = $this->paymentBillService->billingIncrementId($request->clinic_id,'DG',8);
            $unique_queue_number = $this->diagnosticService->queueIncrementId($request->clinic_id);
            if ($request->doctortype == 1) {
                $doctor_id = $request->doctor_id;
                $userService = $this->userService->findById($doctor_id);
                $doctor_name = $userService->username;
                $itdose_doctorid = $userService->doctor->itdose_doctorid;
            }
            else {
                $doctor_name = $request->doctor_first_name.' '.$request->doctor_last_name;
                $data = [
                    'ip_address' => $this->getIp(),
                    'username' => $doctor_name,
                    'active' => 2,
                    'status' => 0,
                    'created_by' => $this->createdBy()
                ];
                $this->userService->setRequest($data);
                $userService = $this->userService->add();
                $itdose_doctorid = $this->doctorService->getItdoseDoctorId($this->diagnosticApiService,$userService->id,$doctor_name,"NA","NA","NA","Others",$this->createdBy());
                $data = [
                    'user_id' => $userService->id,
                    'itdose_doctorid' => $itdose_doctorid,
                    'first_name' => $request->doctor_first_name,
                    'last_name' => $request->doctor_last_name,
                    'created_by' => $this->createdBy(),
                    'doctor_slug' =>Str::slug($doctor_name),
                    'status' => 0,
                    'data_source' => 3
                ];
                $this->doctorService->setRequest($data);
                $doctorService = $this->doctorService->add();
                $doctor_id = $userService->id;
            }
            // dd($itdose_doctorid);
            $sample_remarks = $request->sample_remarks;
            $note_remarks = $request->remarks;
            $is_homecollection = 1;
            $hc_quantity_arearange = null;
            if ($request->home_collection_range && $request->type_of_collection == 'HC') {
                $is_homecollection = 2;
                $sampleHomecollection = $this->diagnosticService->getSampleHomecollection($request->home_collection_range);
                // dd($sampleHomecollection);
                $hc_quantity_arearange = $sampleHomecollection->area_range.','.$request->home_collection_quantity;
            }
            $request->merge([
                'created_by' => $this->createdBy(),
                'test_id' => json_encode($request->item_ids),
                'unique_queue_number' => $unique_queue_number,
                'doctor_id' => $doctor_id,
                'doctor_name' => $doctor_name,
                'remarks' => $sample_remarks,
                'is_homecollection' => $is_homecollection,
                'hc_quantity_arearange' => $hc_quantity_arearange
            ]);
            // dd($request->all());
            if ($request->hasFile('prescription_upload_file')) {
                $fileinfo = $request->file('prescription_upload_file');
                $this->imageUploadService->setFieldName('prescription_upload_file');
                $this->imageUploadService->setFilePath('Diagnostic/Prescription/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            $this->diagnosticService->setRequest($request->except(
                'doctor_first_name','doctor_last_name',
                'sample_remarks','item_ids','test_names','test_codes','delivery_dates',
                'test_prices','discounts','prices','is_urgents','home_collection_range',
                'home_collection_charge','home_collection_quantity','reward_phone_no',
                'sub_total','discount_offer','reward_points_final','payable_amount',
                'payment_modes','amounts','payment_list','total_amount','gross'));
            $diagnostic = $this->diagnosticService->add();
            $unique_id = 'myMDL'.$request->clinic_id.$diagnostic->id;
            $request['unique_id'] = $unique_id;
            $this->diagnosticService->setRequest(['unique_id' => $unique_id]);
            $diagnostic = $this->diagnosticService->update();
            $request['remarks'] = $note_remarks;
            // dd($diagnostic->fully_paid);
            //diagnosticBreakupTestService
            //diagnosticBreakupBillService
            if (isset($request->item_ids)) {
                foreach ($request->item_ids as $key => $row) {
                    $request['sample_collection_id'] = $diagnostic->id;
                    // same data
                    $request['report_delivery_date'] = $request->delivery_dates[$key];
                    $request['is_urgent'] = isset($request->is_urgents) && in_array($row, $request->is_urgents) ? 1 : 0;
                    
                    if (!empty(json_decode($request->package_test_list[$key]))) {
                        foreach (json_decode($request->package_test_list[$key]) as $id => $value) {
                            // dd($key,$value);
                            $request['package_id'] = $row;
                            $request['test_id'] = $id;
                            $request['itdose_testid'] = DB::table('tests')->where('id', $id)->value('itdose_testid');
                            $request['test_code'] = DB::table('tests')->where('id', $id)->value('test_code');
                            $this->breakupTest($request);
                        }
                        // dd(json_decode($request->package_test_list[1]),$request->package_test_list[1]);
                    }
                    else {
                        $request['package_id'] = 0;
                        $request['test_id'] = $row;
                        $request['itdose_testid'] = DB::table('tests')->where('id', $row)->value('itdose_testid');
                        $request['test_code'] = DB::table('tests')->where('id', $row)->value('test_code');
                        $this->breakupTest($request);
                    }                    
                    
                    $request['item_id'] = $row;
                    $request['amount'] = $request->test_prices[$key];
                    $request['discount'] = $request->discounts[$key];
                    $request['net_amount'] = $request->prices[$key];
                    $this->breakupBill($request);
                }
            }
            if ($is_homecollection == 2) {
                $amount = $sampleHomecollection->charges * $request->home_collection_quantity;
                $net_amount = $request->home_collection_charge;
                $data = [
                    'sample_collection_id' => $diagnostic->id,
                    'home_collection_id' => $sampleHomecollection->id,
                    'amount' => $amount,
                    'discount' => $amount-$net_amount,
                    'net_amount' => $net_amount,
                    'created_by' => $this->createdBy(),
                ];
                $this->breakupBillHC($data,$request);
            }            

            $pay_type = config('billing.types.2');
            // payment here
            $fully_paid = $this->diagnosticService->findById($diagnostic->id)->fully_paid;
            // dd($fully_paid);
            $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,$request->discount_offer,$diagnostic->id,$diagnostic->patient_id,$diagnostic->patient_phone,$fully_paid);
            $source_id = $request->source_id;
            $source_type = $request->source_type;
            if (isset($request->item_ids) && $source_id && $source_type == 'Dispute') {
                $source_data = [
                    'type' => $source_type,
                    'service_id' => $source_id,
                    'sample_collection_id' => $diagnostic->id,
                    'patient_id' => $diagnostic->patient_id,
                    'created_by' => $this->createdBy()
                ];
                $this->mapSourceService->setRequest($source_data);
                $this->mapSourceService->add();
            }
            if (isset($request->item_ids) && $source_id && $request->source_test_id) {
                if (!empty(array_intersect($request->item_ids, explode(',',$request->source_test_id)))) {
                    $source_data = [
                        'type' => $source_type,
                        'service_id' => $source_id,
                        'sample_collection_id' => $diagnostic->id,
                        'patient_id' => $diagnostic->patient_id,
                        'created_by' => $this->createdBy()
                    ];
                    $this->mapSourceService->setRequest($source_data);
                    $this->mapSourceService->add();
                    if ($source_type == 'EST') {
                        $estimate_data = [
                            'collection_id' => $diagnostic->id,
                            'modified_by' => $this->modifiedBy()
                        ];
                        $this->estimationService->findById($source_id);
                        $this->estimationService->setRequest($estimate_data);
                        $estimation = $this->estimationService->update();
                        // assign phlebo
                        if ($estimation->assign_by != '' && $is_homecollection == 2) {
                            $data = [
                                'phlebo_id' => $estimation->assign_by,
                                'modified_by' => $this->modifiedBy(),
                                'phlebo_assign_status' => '1',
                                'status' => '2'
                            ];
                            $this->diagnosticService->setRequest($data);
                            $this->diagnosticService->update();
                            $assign_estimation = DB::table('sample_collection_assign_estimations')
                                ->where('estimation_id',$estimation->id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $role_id = DB::table('users')
                                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                                ->select('model_has_roles.role_id')
                                ->where('users.id',$estimation->assign_by)
                                ->first()->role_id;
                            $data = [
                                'role_id' => $role_id,
                                'phlebo_id' => $estimation->assign_by,
                                'schedule_time' => $assign_estimation->schedule_time,
                                'remarks' => $assign_estimation->remarks,
                                'created_by' => $this->createdBy(),
                                'sample_id' => $diagnostic->id,
                                'test_count' => $diagnostic->diagnosticBreakupItem->count(),
                                'collected_by' => 0
                            ];
                            $this->diagnosticPhleboAssignmentService->setRequest($data);
                            $this->diagnosticPhleboAssignmentService->add();

                            $diagnosticBreakupTest = $this->diagnosticBreakupTestService->findByOtherId('sample_collection_id',$diagnostic->id);
                            $this->diagnosticBreakupTestService->setRequest(['status' => 2]);
                            $this->diagnosticBreakupTestService->update();
                        }
                    }
                }
            }
            $panel_id = DB::table('clinics')->where('id',$request->clinic_id)->value('panel_id');
            $patient = $this->patientService->findById($diagnostic->patient_id);
            $patientDtl = [
                'name' => $patient->name,
                'phone' => $request->patient_phone,
                'age' => Carbon::parse($patient->birthdate)->age ?? 0,//age calculate year
                'gender' => $patient->sex,
            ];
            $apiResponse = $this->bookItdoseAppoinment($request,$unique_id,$panel_id,$patientDtl,$itdose_doctorid,$request->sub_total,$request->gross_total,$diagnostic->date_of_collection,$is_homecollection);
            $this->response['success']  = true;
            $this->response['message']  = 'Diagnostic Appointment has been created successfully!';
            $this->response['data']     = [
                'api_response' => $apiResponse
            ];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function addWithPhlebo(DiagnosticWithPhleboRequest $request)
    {
        // dd($request->all());//phlebo_assign_id
        try {
            $request->validated();
            $bill_id = $this->paymentBillService->billingIncrementId($request->clinic_id,'DG',8);
            $unique_queue_number = $this->diagnosticService->queueIncrementId($request->clinic_id);
            if ($request->doctortype == 1) {
                $doctor_id = $request->doctor_id;
                $userService = $this->userService->findById($doctor_id);
                $doctor_name = $userService->username;
                $itdose_doctorid = $userService->doctor->itdose_doctorid;
            }
            else {
                $doctor_name = $request->doctor_first_name.' '.$request->doctor_last_name;
                $data = [
                    'ip_address' => $this->getIp(),
                    'username' => $doctor_name,
                    'active' => 2,
                    'status' => 0,
                    'created_by' => $this->createdBy()
                ];
                $this->userService->setRequest($data);
                $userService = $this->userService->add();
                $itdose_doctorid = $this->doctorService->getItdoseDoctorId($this->diagnosticApiService,$userService->id,$doctor_name,"NA","NA","NA","Others",$this->createdBy());
                $data = [
                    'user_id' => $userService->id,
                    'itdose_doctorid' => $itdose_doctorid,
                    'first_name' => $request->doctor_first_name,
                    'last_name' => $request->doctor_last_name,
                    'created_by' => $this->createdBy(),
                    'doctor_slug' =>Str::slug($doctor_name),
                    'status' => 0,
                    'data_source' => 3
                ];
                $this->doctorService->setRequest($data);
                $doctorService = $this->doctorService->add();
                $doctor_id = $userService->id;
            }
            // dd($itdose_doctorid);
            $sample_remarks = $request->sample_remarks;
            $note_remarks = $request->remarks;
            $is_homecollection = 1;
            $hc_quantity_arearange = null;
            if ($request->home_collection_range && $request->type_of_collection == 'HC') {
                $is_homecollection = 2;
                $sampleHomecollection = $this->diagnosticService->getSampleHomecollection($request->home_collection_range);
                // dd($sampleHomecollection);
                $hc_quantity_arearange = $sampleHomecollection->area_range.','.$request->home_collection_quantity;
            }
            $request->merge([
                'created_by' => $this->createdBy(),
                'test_id' => json_encode($request->item_ids),
                'unique_queue_number' => $unique_queue_number,
                'doctor_id' => $doctor_id,
                'doctor_name' => $doctor_name,
                'remarks' => $sample_remarks,
                'is_homecollection' => $is_homecollection,
                'hc_quantity_arearange' => $hc_quantity_arearange,
                'appointment_type' => 3
            ]);
            // dd($request->all());
            if ($request->hasFile('prescription_upload_file')) {
                $fileinfo = $request->file('prescription_upload_file');
                $this->imageUploadService->setFieldName('prescription_upload_file');
                $this->imageUploadService->setFilePath('Diagnostic/Prescription/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            $this->diagnosticService->setRequest($request->except(
                'doctor_first_name','doctor_last_name',
                'sample_remarks','item_ids','test_names','test_codes','delivery_dates',
                'test_prices','discounts','prices','is_urgents','home_collection_range',
                'home_collection_charge','home_collection_quantity','reward_phone_no',
                'sub_total','discount_offer','reward_points_final','payable_amount',
                'payment_modes','amounts','payment_list','total_amount','gross'));
            $diagnostic = $this->diagnosticService->add();
            $unique_id = 'myMDL'.$request->clinic_id.$diagnostic->id;
            $request['unique_id'] = $unique_id;
            $this->diagnosticService->setRequest(['unique_id' => $unique_id]);
            $diagnostic = $this->diagnosticService->update();
            $request['remarks'] = $note_remarks;
            // dd($diagnostic->fully_paid);
            //diagnosticBreakupTestService
            //diagnosticBreakupBillService
            if (isset($request->item_ids)) {
                foreach ($request->item_ids as $key => $row) {
                    $request['sample_collection_id'] = $diagnostic->id;
                    // same data
                    $request['report_delivery_date'] = $request->delivery_dates[$key];
                    $request['is_urgent'] = isset($request->is_urgents) && in_array($row, $request->is_urgents) ? 1 : 0;
                    
                    if (!empty(json_decode($request->package_test_list[$key]))) {
                        foreach (json_decode($request->package_test_list[$key]) as $id => $value) {
                            // dd($key,$value);
                            $request['package_id'] = $row;
                            $request['test_id'] = $id;
                            $request['itdose_testid'] = DB::table('tests')->where('id', $id)->value('itdose_testid');
                            $request['test_code'] = DB::table('tests')->where('id', $id)->value('test_code');
                            $this->breakupTest($request);
                        }
                        // dd(json_decode($request->package_test_list[1]),$request->package_test_list[1]);
                    }
                    else {
                        $request['package_id'] = 0;
                        $request['test_id'] = $row;
                        $request['itdose_testid'] = DB::table('tests')->where('id', $row)->value('itdose_testid');
                        $request['test_code'] = DB::table('tests')->where('id', $row)->value('test_code');
                        $this->breakupTest($request);
                    }                    
                    
                    $request['item_id'] = $row;
                    $request['amount'] = $request->test_prices[$key];
                    $request['discount'] = $request->discounts[$key];
                    $request['net_amount'] = $request->prices[$key];
                    $this->breakupBill($request);
                }
            }
            if ($is_homecollection == 2) {
                $amount = $sampleHomecollection->charges * $request->home_collection_quantity;
                $net_amount = $request->home_collection_charge;
                $data = [
                    'sample_collection_id' => $diagnostic->id,
                    'home_collection_id' => $sampleHomecollection->id,
                    'amount' => $amount,
                    'discount' => $amount-$net_amount,
                    'net_amount' => $net_amount,
                    'created_by' => $this->createdBy(),
                ];
                $this->breakupBillHC($data,$request);
                // assign phlebo from cc end
                if (isset($request->phlebo_assign_id)) {
                    $role_id = DB::table('users')
                        ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                        ->select('model_has_roles.role_id')
                        ->where('users.id',$request->phlebo_assign_id)
                        ->first()->role_id;
                    $phlebo_assign_id = explode(",",$request->phlebo_assign_id);
                    $phlebo_id = $phlebo_assign_id[0];
                    $slotid = $phlebo_assign_id[1];
                    switch($slotid){
                        case 1:
                            $timeslot = "08:00";
                            break;
                        case 2:
                            $timeslot = "10:00";
                            break;
                        case 3:
                            $timeslot = "12:00";
                            break;
                        case 4:
                            $timeslot = "02:00";
                            break;
                        case 5:
                            $timeslot = "04:00";
                            break;
                        case 6:
                            $timeslot = "06:00";
                            break;
                    }
                    $test_count = DB::table('sample_collection_breakup_tests')->where('sample_collection_id', $diagnostic->id)->count();
                    $data = [
                        'role_id' => $role_id,
                        'phlebo_id' => $phlebo_id,
                        'schedule_time' => $timeslot,
                        'created_by' => $this->createdBy(),
                        'sample_id' => $diagnostic->id,
                        'test_count' => $test_count,
                        'collected_by' => 0
                    ];
                    $this->diagnosticPhleboAssignmentService->setRequest($data);
                    $this->diagnosticPhleboAssignmentService->add();
                    $dataSample = [
                        'phlebo_id' => $phlebo_id,
                        'phlebo_assign_status' => '1',
                        'status' => '2'
                    ];
                    $this->diagnosticService->setRequest($dataSample);
                    $this->diagnosticService->update();
                }
            }            

            $pay_type = config('billing.types.2');
            // payment here
            $fully_paid = $this->diagnosticService->findById($diagnostic->id)->fully_paid;
            // dd($fully_paid);
            $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,$request->discount_offer,$diagnostic->id,$diagnostic->patient_id,$diagnostic->patient_phone,$fully_paid);
            $source_id = $request->source_id;
            $source_type = $request->source_type;
            if (isset($request->item_ids) && $source_id && $request->source_test_id) {
                if (!empty(array_intersect($request->item_ids, explode(',',$request->source_test_id)))) {
                    $source_data = [
                        'type' => $source_type,
                        'service_id' => $source_id,
                        'sample_collection_id' => $diagnostic->id,
                        'patient_id' => $diagnostic->patient_id,
                        'created_by' => $this->createdBy()
                    ];
                    $this->mapSourceService->setRequest($source_data);
                    $this->mapSourceService->add();
                    if ($source_type == 'EST') {
                        $estimate_data = [
                            'collection_id' => $diagnostic->id,
                            'modified_by' => $this->modifiedBy()
                        ];
                        $this->estimationService->findById($source_id);
                        $this->estimationService->setRequest($estimate_data);
                        $estimation = $this->estimationService->update();
                        // assign phlebo
                        if ($estimation->assign_by != '' && $is_homecollection == 2) {
                            $data = [
                                'phlebo_id' => $estimation->assign_by,
                                'modified_by' => $this->modifiedBy(),
                                'phlebo_assign_status' => '1',
                                'status' => '2'
                            ];
                            $this->diagnosticService->setRequest($data);
                            $this->diagnosticService->update();
                            $assign_estimation = DB::table('sample_collection_assign_estimations')
                                ->where('estimation_id',$estimation->id)
                                ->orderBy('id', 'desc')
                                ->first();
                            $role_id = DB::table('users')
                                ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                                ->select('model_has_roles.role_id')
                                ->where('users.id',$estimation->assign_by)
                                ->first()->role_id;
                            $data = [
                                'role_id' => $role_id,
                                'phlebo_id' => $estimation->assign_by,
                                'schedule_time' => $assign_estimation->schedule_time,
                                'remarks' => $assign_estimation->remarks,
                                'created_by' => $this->createdBy(),
                                'sample_id' => $diagnostic->id,
                                'test_count' => $diagnostic->diagnosticBreakupItem->count(),
                                'collected_by' => 0
                            ];
                            $this->diagnosticPhleboAssignmentService->setRequest($data);
                            $this->diagnosticPhleboAssignmentService->add();

                            $diagnosticBreakupTest = $this->diagnosticBreakupTestService->findByOtherId('sample_collection_id',$diagnostic->id);
                            $this->diagnosticBreakupTestService->setRequest(['status' => 2]);
                            $this->diagnosticBreakupTestService->update();
                        }
                    }
                }
            }
            $panel_id = DB::table('clinics')->where('id',$request->clinic_id)->value('panel_id');
            $patient = $this->patientService->findById($diagnostic->patient_id);
            $patientDtl = [
                'name' => $patient->name,
                'phone' => $request->patient_phone,
                'age' => Carbon::parse($patient->birthdate)->age ?? 0,//age calculate year
                'gender' => $patient->sex,
            ];
            $apiResponse = $this->bookItdoseAppoinment($request,$unique_id,$panel_id,$patientDtl,$itdose_doctorid,$request->sub_total,$request->gross_total,$diagnostic->date_of_collection,$is_homecollection);
            $this->response['success']  = true;
            $this->response['message']  = 'Diagnostic Appointment has been created successfully!';
            $this->response['data']     = [
                'api_response' => $apiResponse
            ];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    private function bookItdoseAppoinment($request,$work_order_id,$panel_id,$patientDtl,$itdose_doctorid,$bill_amount,$paid_amount,$date_of_collection,$is_homecollection)
    {
        $dataArray = [];
        // $dataArray['url'] = 'https://lab.mymdindia.com/mymd_uat/api/BookingAPI/BookingAPINew';
        $dataArray['template_id'] = 1;
        $dataArray['work_order_id'] = $work_order_id;
        $dataArray['created_by'] = $this->createdBy();

        $dataArray['playload'] = [
            "Panel_ID" => $panel_id, // clinic table panel id
            "CentreID" => 1, // static
            "mobile" => $patientDtl['phone'], // patient phone
            "email" => null,
            "age" => $patientDtl['age'], // patient age
            "designation" => "",
            "fullName" => $patientDtl['name'], // patient name
            "gender" => $patientDtl['gender'], // Male/Female
            "area" => "",
            "city" => "",
            "Address" => "",
            "patientType" => "",
            "labPatientId" => "",
            "pincode" => "",
            "patientId" => "",
            "dob" => "",
            "passportNo" => "",
            "panNumber" => "",
            "aadharNumber" => "",
            "insuranceNo" => "",
            "nationality" => "",
            "ethnicity" => "",
            "nationalIdentityNumber" => "",
            "workerCode" => "",
            "doctorCode" => $itdose_doctorid, // Doctor table itdose doctor_id
        ];
        $dataArray['playload']['billDetails'] = [
            "emergencyFlag" => 0,
            "totalAmount" => $bill_amount, // bill amount
            "advance" => $paid_amount, // paid amount
            "billDate" => $date_of_collection, // date_of_collection
            "paymentType" => "CASH", // payment mode static
            "referralName" => "",
            "otherReferral" => "",
            "sampleId" => "",
            "orderNumber" => $work_order_id, // workorder id
            "referralIdLH" => "",
            "organisationName" => "",
            "additionalAmount" => "",
            "organizationIdLH" => "",
            "comments" => "",
        ];
        if (isset($request->item_ids)) {
            foreach ($request->item_ids as $key => $row) {
                $itdose_testid = DB::table('tests')->where('id', $row)->value('itdose_testid');
                $dataArray['playload']['billDetails']['testList'][] = [
                    "testID" => $itdose_testid, // test table itdose_test_id
                    "testCode" => $itdose_testid, // test table itdose_test_id
                    "Rate" => $request->test_prices[$key], // test table bill amount
                    "DiscountAmt" => $request->discounts[$key], // test table discount
                    "integrationCode" => "",
                    "dictionaryId" => "",
                    "sampleId" => ""
                ];
            }
        }
        if ($is_homecollection == 2) { // if home collection
            $dataArray['playload']['billDetails']['testList'][] = [
                "testID" => "8562", // hc id
                "testCode" => "8562",
                "Rate" => $request->home_collection_charge, // test table hc net amount
                "DiscountAmt" => 0,
                "integrationCode" => "",
                "dictionaryId" => "",
                "sampleId" => ""
            ];
        }
        if (isset($request->payment_modes)) {
            foreach ($request->payment_modes as $key => $row) {
                switch ($row) {
                    case 'cash':
                        $paymentType = 'Cash';
                        break;
                    case 'credit_card':
                        $paymentType = 'Credit Card';
                        break;
                    case 'debit_card':
                        $paymentType = 'Debit Card';
                        break;
                    case 'upi':
                        $paymentType = 'PayTM';
                        break;
                    default:
                        $paymentType = 'NA';
                        break;
                }
                $paymentList = [
                    "paymentType" => $paymentType, // Cash, Credit Card, Debit Card, PayTM
                    "paymentAmount" => $request->amounts[$key],
                    "issueBank" => "",
                    "chequeNo" => ""
                ];
                $dataArray['playload']['billDetails']['paymentList'][] = $paymentList;
            }
        }
        else {
            $paymentList = [
                "paymentType" => "Cash",
                "paymentAmount" => 0,
                "issueBank" => "",
                "chequeNo" => ""
            ];
            $dataArray['playload']['billDetails']['paymentList'][] = $paymentList;
        }
        if($request->reward_points_final > 0){ // if get redeem point
            $dataArray['playload']['billDetails']['paymentList'][] = [
                "paymentType" => "Redemption Point",
                "paymentAmount" => $request->reward_points_final,
                "issueBank" => "",
                "chequeNo" => ""
            ];
        }
        // dd($dataArray);
        dispatch(new DiagnosticApiJob($this->diagnosticApiService,$dataArray))->onQueue('itdose_book_api');
        // $this->diagnosticApiService->setDataArray($dataArray);
        // $apiResponse = $this->diagnosticApiService->callApi();
        return $dataArray;
    }
    private function breakupTest($request)
    {
        $this->diagnosticBreakupTestService->setRequest($request->except(
            'patient_id','patient_phone','type_of_collection','date_of_collection',
            'doctortype','doctor_first_name','doctor_last_name',
            'sample_remarks','item_ids','test_names','test_codes','delivery_dates',
            'test_prices','discounts','prices','is_urgents','home_collection_range',
            'home_collection_charge','home_collection_quantity','reward_phone_no',
            'sub_total','discount_offer','reward_points_final','payable_amount',
            'payment_modes','amounts','payment_list','total_amount','gross'));
        $breakupTest = DB::table('sample_collection_breakup_tests')->where('sample_collection_id', $request['sample_collection_id'])
            ->where('test_id', $request['test_id'])
            ->whereNull('deleted_at')
            ->select('id')
            ->first();
        if (isset($breakupTest)) {
            $this->diagnosticBreakupTestService->findById($breakupTest->id);
            $this->diagnosticBreakupTestService->update();
        }
        else {
            $this->diagnosticBreakupTestService->add();
        }
    }
    private function breakupBill($request)
    {
        $this->diagnosticBreakupBillService->setRequest($request->except(
            'patient_id','patient_phone','type_of_collection','date_of_collection',
            'status','doctortype','doctor_first_name','doctor_last_name',
            'sample_remarks','item_ids','test_names','test_codes','delivery_dates',
            'test_prices','discounts','prices','is_urgents','home_collection_range',
            'home_collection_charge','home_collection_quantity','reward_phone_no',
            'sub_total','discount_offer','reward_points_final','payable_amount',
            'payment_modes','amounts','payment_list','total_amount','gross'));
        $breakupBill = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $request['sample_collection_id'])
            ->where('item_id', $request['item_id'])
            ->whereNull('deleted_at')
            ->select('id')
            ->first();
        if (isset($breakupBill)) {
            $this->diagnosticBreakupBillService->findById($breakupBill->id);
            $this->diagnosticBreakupBillService->update();
        }
        else {
            $this->diagnosticBreakupBillService->add();
            // occerance
            $patient = $this->patientService->findById($request->patient_id);
            if (in_array($request->offered_id, [7,8,9,10])) {
                $own_membership = $patient->membershipRegistrations->where('card_type',$request->offered_id)
                    ->first();
                $registration_no = $own_membership->registration_no;
                $memberships = $own_membership->memberships;
                $no_of_occurrence = explode(',',$memberships->no_of_occurrence);
                $complementory_diagnostic = explode(',',$memberships->complementory_diagnostic);
                foreach ($complementory_diagnostic as $key => $row) {
                    if ($request->item_id == $row) {
                        $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                            ->where('item_id', $row)
                            ->where('membership_no', $registration_no)
                            ->where('patient_id', $request->patient_id)
                            ->count();
                        if($check_occurrence < $no_of_occurrence[$key]){
                            $check_occurrence = DB::table('sample_collection_membership_test_discounts')
                            ->insert([
                                'item_id' => $row,
                                'workorder_id' => $request->unique_id,
                                'patient_id' => $request->patient_id,
                                'membership_no' => $registration_no,
                                'created_by' => $this->createdBy(),
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                        }
                    }
                }
            }
        }
        
    }
    private function breakupBillHC($data,$request)
    {
        $this->diagnosticBreakupBillService->setRequest($data);
        $breakupBill = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $data['sample_collection_id'])
            ->where('home_collection_id', $data['home_collection_id'])
            ->whereNull('deleted_at')
            ->select('id')
            ->first();
        if (isset($breakupBill)) {
            $this->diagnosticBreakupBillService->findById($breakupBill->id);
            $this->diagnosticBreakupBillService->update();
        }
        else {
            $this->diagnosticBreakupBillService->add();
            // home collection occerance
            $patient = $this->patientService->findById($request->patient_id);
            if (in_array($request->offered_id, [7,8,9,10])) {
                $own_membership = $patient->membershipRegistrations->where('card_type',$request->offered_id)
                    ->first();
                $registration_no = $own_membership->registration_no;
                if($own_membership->memberships->home_collection_distance_offer && $own_membership->homecollection_no_of_occurrence < $own_membership->memberships->home_collection_free_distance_occerence){
                    $check_occurrence = DB::table('membership_registrations')
                        ->where([
                            'registration_no' => $registration_no,
                            'patient_id' => $request->patient_id,
                            'card_type' => $request->offered_id
                        ])
                        ->increment('homecollection_no_of_occurrence');
                }
            }
        }
    }
    private function paymentWithReward($request,$bill_id,$pay_type,$bill_amount = 0,$discount = 0,$service_id,$patient_id,$phone,$fully_paid)
    {
        $request->merge([
            'created_by' => $this->createdBy(),
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'total_amount' => $request->gross_total,
            'status' => 1
        ]);
        // dd($service->paymentBill);
        $this->paymentBillService->setRequest($request->except('remarks','payment_modes','amounts','payment_list','upi_mode'));
        $paymentBill = $this->paymentBillService->add();
        
        $total_amount = 0;
        $total_discount = $discount;
        $reward_point_credit = 0;
        if($request->reward_points_final > 0 || isset($request->payment_modes)){
            // payment create
            $request->merge([
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d'),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','DG-receipt',8),
                'status' => 'Paid'
            ]);
            $this->paymentService->setRequest($request->except('phone','clinic_id','start_date','end_date','registration_no','category_id','card_type','remarks','payment_modes','amounts','payment_list','upi_mode'));
            $payment = $this->paymentService->add();
            // payment details
            
            if (isset($request->payment_modes)) {
                foreach ($request->payment_modes as $key => $row) {
                    if ($row == 'upi') {
                        $payment_details = $request->payment_list[$key].' '.$request->upi_mode;
                    }
                    else {
                        $payment_details = $request->payment_list[$key];
                    }
                    $request['payment_id'] = $payment->id;
                    $request['payment_mode'] = $row;
                    $request['amount'] = $request->amounts[$key];
                    $request['payment_details'] = $payment_details;
                    $total_amount += $request->amounts[$key];
                    $this->paymentDetailService->setRequest($request->except('patient_id','status','remarks','payment_modes','amounts','payment_list','upi_mode'));
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }
            // reward points
            
            $request['openning_points'] = $this->rewardService->rewardPoints($phone);
            // reward points debit
            if($request->reward_points_final > 0){
                $reward = [
                    'phone_no' => $request->reward_phone_no,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $request->reward_points_final*-1,
                    'credit_debit' => 2,
                    'is_redeem' => 2,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // reward points credit
            $due_amount = $paymentBill->total_amount - $total_amount;
            // dd($paymentBill->total_amount,$fully_paid,$due_amount,$total_amount);
            if(($paymentBill->total_amount > 0 && $fully_paid == 1 && $due_amount == 0) || ($fully_paid == 2 && $total_amount > 0)){
                $percentage = $this->rewardService->getPercentage(4);
                if ($paymentBill->total_amount > 0 && $fully_paid == 1 && $due_amount == 0) {
                    $reward_point_credit = $this->paymentBillService->rewardCalculation($paymentBill->total_amount,$percentage);
                    $this->diagnosticService->findById($service_id);
                    $this->diagnosticService->setRequest(['fully_paid' => 2]);
                    $this->diagnosticService->update();
                }
                else {
                    $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
                }
                $reward = [
                    'phone_no' => $phone,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $reward_point_credit,
                    'credit_debit' => 1,
                    'is_redeem' => 1,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // payment table reward point update
            $request['amount'] = $total_amount;
            // $request['discount'] = $total_discount;
            // $request['gross_total'] = ($total_amount+$total_discount);
            $request['redeem_points'] = $request->reward_points_final == '' ? 0 : $request->reward_points_final;
            $request['closing_points'] = $this->rewardService->rewardPoints($phone);
            $payment = $this->paymentService->findById($payment->id);
            $this->paymentService->setRequest($request->except(
                'bill_id','date','remarks','recpit_no','status',
                'patient_id','remarks','payment_modes',
                'amounts','payment_list','upi_mode'
            ));
            $this->paymentService->update();
        }
        // payment bill table amount update
        
        $request['discount'] = $total_discount;
        $request['paid_amount'] = $total_amount;
        $request['due_amount'] = $paymentBill->total_amount - $request['paid_amount'];
        $paymentBill = $this->paymentBillService->findById($paymentBill->id);
        $this->paymentBillService->setRequest($request->except(
            'bill_show_id','type','service_id','patient_id','status',
            'patient_id','phone','clinic_id','start_date','end_date',
            'registration_no','card_type','remarks','payment_modes',
            'amounts','payment_list','upi_mode'
        ));
        $this->paymentBillService->update();

        return $reward_point_credit;
    }
    public function edit($id,Request $request)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $user_clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $user_clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $user_clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            // dd($diagnostic->paymentBill);
            $paymentBill = $diagnostic->paymentBill;
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                'diagnosticBill' => $diagnostic->diagnosticBill,
                'diagnosticBillHC' => $diagnostic->diagnosticBillHC,
                'diagnosticBillItem' => $diagnostic->diagnosticBillItem,
                'diagnosticBreakupItemMergePackage' => $diagnostic->diagnosticBreakupItemMergePackage(),
                'diagnosticBreakupItemGroup' => $diagnostic->diagnosticBreakupItemGroup,
                'diagnosticPayment' => $paymentBill->payments,
                'reward_redeem' => $paymentBill->payments->sum('redeem_points'),
            ];
            // dd($data['diagnosticBillItem'],$data['diagnosticBreakupItemGroup']);//package_id,report_delivery_date,is_urgent
            $patient_id = $diagnostic->patient_id;
            $phone = $diagnostic->patient_phone;
            // dd($data['patient_detail']->membershipRegistrations);
            $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['diagnostic']->doctor->first_name);
            $distance_list = $this->diagnosticService->allSampleHomecollectionCharges()->toArray();
            // dd($data['diagnostic']->offered_type,$data['diagnostic']->offered_id);
            // 1:membership,2:campaign,3:employee
            $offer_list = [];
            if ($data['diagnostic']->offered_type == 1) {
                // offer for Membership
                $offer_list = $this->diagnosticService->membershipOffer($data['patient_detail'],$offer_list,$data['diagnostic']->offered_id);
            }
            elseif ($data['diagnostic']->offered_type == 2) {
                // offer for Campaign
                $offer_list = $this->diagnosticService->campaignOffer($data['patient_detail'],$offer_list,$data['diagnostic']->offered_id);
            }
            elseif ($data['diagnostic']->offered_type == 3) {
                // offer for Employee
                $offer_list = $this->diagnosticService->employeeOffer($data['patient_detail'],$offer_list,$data['diagnostic']->offered_id);
            }
            // dd($offer_list);
            $list = [
                'visit_type' => config('diagnostic.visit_type'),
                'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                'user_clinic_id' => $user_clinic_id,
                'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                'test_list' => $this->diagnosticService->allTests()->toArray(),
                'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'offer_list' => $offer_list
            ];
            // dd($list['offer_list']);
            $this->response['form'] = view('diagnostic::diagnostic.api.addEdit',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function update($id,DiagnosticRequest $request)
    {
        // dd($request->all());
        try {
            $request->validated();
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }

            if ($request->doctortype == 1) {
                $doctor_id = $request->doctor_id;
                $userService = $this->userService->findById($doctor_id);
                $doctor_name = $userService->username;
                $itdose_doctorid = $userService->doctor->itdose_doctorid;
            }
            else {
                $doctor_name = $request->doctor_first_name.' '.$request->doctor_last_name;
                if($diagnostic->doctor_id && $diagnostic->doctortype == 2){
                    $data = [
                        'username' => $doctor_name,
                        'modified_by' => $this->modifiedBy(),
                    ];
                    $this->userService->findById($diagnostic->doctor_id);
                    $this->userService->setRequest($data);
                    $userService = $this->userService->update();
                    $data = [
                        'first_name' => $request->doctor_first_name,
                        'last_name' => $request->doctor_last_name,
                        'modified_by' => $this->modifiedBy(),
                    ];
                    $this->doctorService->findByOtherId('user_id',$userService->id);
                    $this->doctorService->setRequest($data);
                    $doctorService = $this->doctorService->update();
                    $itdose_doctorid = $doctorService->itdose_doctorid;
                }
                else {
                    $data = [
                        'ip_address' => $this->getIp(),
                        'username' => $doctor_name,
                        'active' => 2,
                        'status' => 0,
                        'created_by' => $this->createdBy()
                    ];
                    $this->userService->setRequest($data);
                    $userService = $this->userService->add();
                    $itdose_doctorid = $this->doctorService->getItdoseDoctorId($this->diagnosticApiService,$userService->id,$doctor_name,"NA","NA","NA","Others",$this->createdBy());
                    $data = [
                        'user_id' => $userService->id,
                        'itdose_doctorid' => $itdose_doctorid,
                        'first_name' => $request->doctor_first_name,
                        'last_name' => $request->doctor_last_name,
                        'created_by' => $this->createdBy(),
                        'doctor_slug' =>Str::slug($doctor_name),
                        'status' => 0,
                        'data_source' => 3
                    ];
                    $this->doctorService->setRequest($data);
                    $doctorService = $this->doctorService->add();
                    $doctor_id = $userService->id;
                }                
                $doctor_id = $userService->id;
            }
            ////////////////////// itdose edit ////////////////////////
            $prev_test = $diagnostic->diagnosticBillItem->pluck('item_id')->toArray() ?? [];
            $cur_test = $request->item_ids ?? [];
            $removeCheckArr = array_diff($prev_test, $cur_test);
            $addCheckArr = array_diff($cur_test, $prev_test);
            $deleted_by = $this->createdBy();
            if (!empty($removeCheckArr)) {
                foreach ($removeCheckArr as $row) {
                    $sample_collection_id = $diagnostic->id;
                    $itdose_testid = DB::table('tests')->where('id', $row)->value('itdose_testid');
                    $test_bill = DB::table('sample_collection_breakup_bills')->where([
                        'sample_collection_id' => $sample_collection_id,
                        'item_id' => $row
                    ])->select('amount','discount')->first();
                    $totalAmount = $test_bill->amount*-1;
                    $testList = [];
                    $testList[] = [
                        'testID' => $itdose_testid,
                        'testCode' => $itdose_testid,
                        'Rate' => $test_bill->amount,
                        'DiscountAmt' => $test_bill->discount,
                        'TestType' => 'Test-Remove'
                    ];
                    $this->editAppoinmentItdose($request,$diagnostic->unique_id,$itdose_doctorid,$totalAmount,$testList);
                    // it could be soft delete
                    $breakupBill = DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $diagnostic->id)->where('item_id', $row)->value('id') ?? null;
                    $this->diagnosticBreakupBillService->findById($breakupBill);
                    $this->diagnosticBreakupBillService->delete($deleted_by);
                    $breakupTest = DB::table('sample_collection_breakup_tests')->where('sample_collection_id', $diagnostic->id)->where('test_id', $row)->orWhere('package_id', $row)->select('id')->get();
                    if(count($breakupTest) > 0){
                        foreach ($breakupTest as $rowTest) {
                            $this->diagnosticBreakupTestService->findById($rowTest->id);
                            $this->diagnosticBreakupTestService->delete($deleted_by);
                        }
                    }
                }
            }
            // dd($removeCheckArr,8888);
            
            if (!empty($addCheckArr)) {
                foreach ($request->item_ids as $key => $row) {
                    if(in_array($row, $addCheckArr)){
                        $totalAmount = $request->test_prices[$key];
                        $testList = [];
                        $itdose_testid = DB::table('tests')->where('id', $row)->value('itdose_testid');
                        $testList[] = [
                            'testID' => $itdose_testid,
                            'testCode' => $itdose_testid,
                            'Rate' => $request->test_prices[$key],
                            'DiscountAmt' => $request->discounts[$key],
                            'TestType' => 'Test-Add'
                        ];
                        $this->editAppoinmentItdose($request,$diagnostic->unique_id,$itdose_doctorid,$totalAmount,$testList);
                    }
                }
            }
            ///////////////////// end itdose edit /////////////////////
            $sample_remarks = $request->sample_remarks;
            $note_remarks = $request->remarks;
            $is_homecollection = 1;
            $hc_quantity_arearange = null;
            if ($request->home_collection_range && $request->type_of_collection == 'HC') {
                $is_homecollection = 2;
                $sampleHomecollection = $this->diagnosticService->getSampleHomecollection($request->home_collection_range);
                // dd($sampleHomecollection);
                $hc_quantity_arearange = $sampleHomecollection->area_range.','.$request->home_collection_quantity;
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'test_id' => json_encode($request->item_ids),
                'doctor_id' => $doctor_id,
                'doctor_name' => $doctor_name,
                'remarks' => $sample_remarks,
                'is_homecollection' => $is_homecollection,
                'hc_quantity_arearange' => $hc_quantity_arearange
            ]);
            // dd($request->all());
            if ($request->hasFile('prescription_upload_file')) {
                $fileinfo = $request->file('prescription_upload_file');
                $this->imageUploadService->setFieldName('prescription_upload_file');
                $this->imageUploadService->setFilePath('Diagnostic/Prescription/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            $this->diagnosticService->setRequest($request->except(
                'doctor_first_name','doctor_last_name',
                'sample_remarks','item_ids','test_names','test_codes','delivery_dates',
                'test_prices','discounts','prices','is_urgents','home_collection_range',
                'home_collection_charge','home_collection_quantity',
                'sub_total','discount_offer','reward_points_final',
                'gross_total'));
            $diagnostic = $this->diagnosticService->update();
            // dd($diagnostic);
            $request['remarks'] = $note_remarks;
            $request['unique_id'] = $diagnostic->unique_id;
            $request['created_by'] = $this->createdBy();
            // dd($request->item_ids);
            if (isset($request->item_ids)) {
                // DB::table('sample_collection_breakup_tests')->where('sample_collection_id', $diagnostic->id)->delete();
                // DB::table('sample_collection_breakup_bills')->where('sample_collection_id', $diagnostic->id)->delete();
                
                foreach ($request->item_ids as $key => $row) {
                    $request['sample_collection_id'] = $diagnostic->id;
                    // same data 
                    $request['report_delivery_date'] = $request->delivery_dates[$key];
                    $request['is_urgent'] = isset($request->is_urgents) && in_array($row, $request->is_urgents) ? 1 : 0;
                    if (!empty(json_decode($request->package_test_list[$key]))) {
                        foreach (json_decode($request->package_test_list[$key]) as $id => $value) {
                            // dd($key,$value);
                            $request['package_id'] = $row;
                            $request['test_id'] = $id;
                            $request['itdose_testid'] = DB::table('tests')->where('id', $id)->value('itdose_testid');
                            $request['test_code'] = DB::table('tests')->where('id', $id)->value('test_code');
                            $this->breakupTest($request);
                        }
                        // dd(json_decode($request->package_test_list[1]),$request->package_test_list[1]);
                    }
                    else {
                        $request['package_id'] = 0;
                        $request['test_id'] = $row;
                        $request['itdose_testid'] = DB::table('tests')->where('id', $row)->value('itdose_testid');
                        $request['test_code'] = DB::table('tests')->where('id', $row)->value('test_code');
                        $this->breakupTest($request);
                    }                    
                    
                    $request['item_id'] = $row;
                    $request['amount'] = $request->test_prices[$key];
                    $request['discount'] = $request->discounts[$key];
                    $request['net_amount'] = $request->prices[$key];
                    $this->breakupBill($request);
                }
            }
            if ($is_homecollection == 2) {
                $amount = $sampleHomecollection->charges * $request->home_collection_quantity;
                $net_amount = $request->home_collection_charge;
                $data = [
                    'sample_collection_id' => $diagnostic->id,
                    'home_collection_id' => $sampleHomecollection->id,
                    'amount' => $amount,
                    'discount' => $amount-$net_amount,
                    'net_amount' => $net_amount,
                    'created_by' => $this->createdBy(),
                ];
                
                $this->breakupBillHC($data,$request);
            }
            
            // bill amount update
            if(isset($diagnostic->paymentBill->id)){
                $paymentBill = $this->paymentBillService->findById($diagnostic->paymentBill->id);
                $billData = [
                    'modified_by' => $this->modifiedBy(),
                    'bill_amount' => $request->sub_total,
                    'discount' => $request->discount_offer,
                    'total_amount' => $request->gross_total,
                    'due_amount' => $request->gross_total - $diagnostic->paymentBill->paid_amount
                ];
                $this->paymentBillService->setRequest($billData);
                $paymentBill = $this->paymentBillService->update();
                // note_remarks
            }
            
            $this->response['success']  = true;
            $this->response['message']  = 'Diagnostic has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    private function editAppoinmentItdose($request,$work_order_id,$doctorCode,$totalAmount,$testList)
    {
        $dataArray = [];
        // $dataArray['url'] = 'https://lab.mymdindia.com/mymd_uat/api/allapi/Dueclearance';
        $dataArray['template_id'] = 5;
        $dataArray['work_order_id'] = $work_order_id;
        $dataArray['created_by'] = $this->createdBy();

        $dataArray['playload'] = [
            "doctorCode" => $doctorCode, //it dose doctor id
        ];
        $dataArray['playload']['billDetails'] = [
            'totalAmount' => $totalAmount,
            'paymentType' => 'NA',
            'orderNumber' => $work_order_id,
            'comments' => "",
            'testList' => $testList
        ];
        // dd($dataArray);
        dispatch(new DiagnosticApiJob($this->diagnosticApiService,$dataArray))->onQueue('itdose_edit_api');
        // $this->diagnosticApiService->setDataArray($dataArray);
        // $apiResponse = $this->diagnosticApiService->callApi();
        return $dataArray;
    }
    public function updateStatus($id,Request $request)
    {
        // dd($request->all());
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->diagnosticService->setRequest($request);
            $this->diagnosticService->update();
            
            $this->diagnosticBreakupTestService->updateBreakupTest($diagnostic->id,$request);

            $this->response['success']  = true;
            $this->response['message']  = 'Diagnostic Appointment has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function viewInvoice($id,Request $request)
    {
        $diagnostic = $this->diagnosticService->findById($id);
        $earned_points = 0;
        if ($diagnostic) {
            $billPayment = $diagnostic->paymentBill;
            // dd($billPayment);
            $bill_id = $billPayment->bill_show_id;
            // dd($billPayment->payments->sum('discount'));
            
            $earned_points = DB::table('reward_points')
                ->where([
                    'bill_id' => $bill_id,
                    'type' => 'DG',
                    'credit_debit' => 1
                ])->value('point');
        }
        $data = [
            'diagnostic' => $diagnostic,
            'doctor_dtl' => $diagnostic->userDoctors,
            'clinic_dtl' => $diagnostic->clinic,
            'patient_dtl' => $diagnostic->patient,
            'patient_membership' => $this->diagnosticService->activeMembership($diagnostic->patient_id),
            'bill_id' => $bill_id,
            'billPayment' => $billPayment,
            'billTests' => $diagnostic->diagnosticBillItemName,
            'billHomeCollectionCharges' => $diagnostic->diagnosticBillHC->sum('net_amount'),
            'redeem_points' => $billPayment->payments->sum('redeem_points'),
            'reward_payment_dtl' => $billPayment->payments->last(),
            'earned_points' => $earned_points ? $earned_points : 0,
            'createdBy' => $diagnostic->createdBy->username,
        ];
        // dd($data['reward_payment_dtl']);
        $pdf = PDF::loadView('diagnostic::diagnostic.pdf.viewInvoice', $data);
        $pdf = $pdf->setPaper('a4', 'portrait');
        return $pdf->stream('bill.pdf');
    }
    public function viewTRF($id,Request $request)
    {
        $diagnostic = $this->diagnosticService->findById($id);
        $earned_points = 0;
        if ($diagnostic) {
            $billPayment = $diagnostic->paymentBill;
            // dd($billPayment);
            $bill_id = $billPayment->bill_show_id;
            // dd($billPayment->payments->sum('discount'));
            
            $earned_points = DB::table('reward_points')
                ->where([
                    'bill_id' => $bill_id,
                    'type' => 'DG',
                    'credit_debit' => 1
                ])->value('point');
        }
        $data = [
            'diagnostic' => $diagnostic,
            'doctor_dtl' => $diagnostic->userDoctors,
            'clinic_dtl' => $diagnostic->clinic,
            'patient_dtl' => $diagnostic->patient,
            'diagnosticTests' => $diagnostic->diagnosticBreakupItemTest,
            'sin_created_on' => $diagnostic->diagnosticBreakupCollectionAt->value('sin_created_on'),
            'sin_created_by_name' => $diagnostic->diagnosticBreakupCollectionBy->value('sin_created_by_name'),
            'diagnosticTestSample' => $diagnostic->diagnosticBreakupItemGroupSample,
            'transfer_on' => $diagnostic->diagnosticBreakupTransferAt->value('transfer_on'),
            'patient_esign' => $diagnostic->patient_esign
        ];
        // dd($data['diagnostic']->patient_esign);
        $pdf = PDF::loadView('diagnostic::diagnostic.pdf.viewTRF', $data);
        $pdf = $pdf->setPaper('a4', 'portrait');
        return $pdf->stream('bill.pdf');
    }
    public function editAssignPhlebo($id)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // $this->response['data']     = $diagnostic;
            $data = [
                'diagnostic' => $diagnostic,
                'assignPhlebotomists' => $diagnostic->assignPhlebotomists
            ];
            // dd($data['assignPhlebotomists']);
            $role = $this->getUserRole();
            if(in_array($role->id, [7,6]))
            {
                $phlebotomist_list = $this->userService->entity->role('Phlebotomist')->select($this->userService->columns)
                    ->where('status',1)
                    ->whereHas('phlebotomist', function ($query) use ($data) {
                        $query->whereRaw("FIND_IN_SET(?, working_city_pincode)", [$data['diagnostic']->pincode]);
                    })
                    ->get();
                $nurse_list = $this->userService->entity->role('Nurse')->select($this->userService->columns)
                    ->where('status',1)
                    ->whereHas('nurse', function ($query) use ($data) {
                        $query->where('clinic_id', $data['diagnostic']->clinic_id);
                    })
                    ->get();
            }
            else
            {
                $phlebotomist_list = $this->userService->entity->role('Phlebotomist')->select($this->userService->columns)
                    ->where('status',1)
                    ->get();
                $nurse_list = $this->userService->entity->role('Nurse')->select($this->userService->columns)
                    ->where('status',1)
                    ->get();
            }
            // dd($phlebotomist_list,$nurse_list);
            $list = [
                'roles' => $this->userService->allRoles()->whereIn('id',[6,7]),
                'phlebotomist_list' => $phlebotomist_list,
                'nurse_list' => $nurse_list
            ];
            // dd($list);
            $this->response['form'] = view('diagnostic::diagnostic.api.editAssignPhlebo',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updateAssignPhlebo($id,AssignPhleboRequest $request)
    {
        try {
            $request->validated();

            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->diagnosticBreakupItem->count());
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'diagnostic not found!';
                return response()->json($this->response);
            }
            
            if($request->role_id == 6){
                $request->merge([
                    'phlebo_id' => $request->nurse_id,
                ]);
                $assign_to = 'Nurse';
            }
            elseif($request->role_id == 7){
                $request->merge([
                    'phlebo_id' => $request->phlebo_id,
                ]);
                $assign_to = 'Phlebotomist';
            }
            $message = $diagnostic->phlebo_id ? $assign_to.' has been reassigned successfully!' : $assign_to.' has been assigned successfully!';
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'phlebo_assign_status' => '1',
                'status' => '2'
            ]);
            $this->diagnosticService->setRequest($request->except('remarks'));
            $this->diagnosticService->update();
            
            $request->merge([
                'created_by' => $this->createdBy(),
                'sample_id' => $diagnostic->id,
                'test_count' => $diagnostic->diagnosticBreakupItem->count(),
                'collected_by' => 0
            ]);
            $this->diagnosticPhleboAssignmentService->setRequest($request->except('modified_by','phlebo_assign_status','status'));
            $this->diagnosticPhleboAssignmentService->add();

            $diagnosticBreakupTest = $this->diagnosticBreakupTestService->findByOtherId('sample_collection_id',$diagnostic->id);
            $this->diagnosticBreakupTestService->setRequest(['status' => 2]);
            $this->diagnosticBreakupTestService->update();
            
            // dd('sdyfbuby');
            $this->response['success']  = true;
            $this->response['message']  = $message;
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function paymentSettlementForm($id,Request $request)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // dd($diagnostic->paymentBill);
            $role = $this->getUserRole();
            $paymentBill = $diagnostic->paymentBill;
            $data = [
                'role_id' => $role->id,
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                // 'diagnosticBill' => $diagnostic->diagnosticBill,
                'diagnosticBillHC' => $diagnostic->diagnosticBillHC,
                'diagnosticBillItem' => $diagnostic->diagnosticBillItem,
                // 'diagnosticBreakupItemMergePackage' => $diagnostic->diagnosticBreakupItemMergePackage(),
                // 'diagnosticBreakupItemGroup' => $diagnostic->diagnosticBreakupItemGroup,
                'paymentBill' => $paymentBill,
                // 'diagnosticPayment' => $paymentBill->payments,
                'reward_redeem' => $paymentBill->payments->sum('redeem_points'),
            ];
            // dd($data['diagnosticBillItem'][0]['test']);//package_id,report_delivery_date,is_urgent
            $patient_id = $diagnostic->patient_id;
            $phone = $diagnostic->patient_phone;
            // dd($data['patient_detail']->membershipRegistrations);
            $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['diagnostic']->doctor->first_name);
            $distance_list = $this->diagnosticService->allSampleHomecollectionCharges()->toArray();
            // dd($data['diagnostic']->offered_type,$data['diagnostic']->offered_id);
            
            $list = [
                // 'visit_type' => config('diagnostic.visit_type'),
                // 'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                // 'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                // 'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                // 'test_list' => $this->diagnosticService->allTests()->toArray(),
                // 'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'reward_points' => $this->rewardService->rewardPoints($phone),
            ];
            // dd(empty($list['offer_list']));
            $this->response['form'] = view('diagnostic::diagnostic.api.paymentSettlementForm',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updatePaymentSettlement($id,PaymentSettlementRequest $request)
    {
        try {
            $request->validated();
            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $phone = $diagnostic->patient_phone;
            $paymentBill = $diagnostic->paymentBill;
            $bill_id = $paymentBill->bill_show_id;
            $pay_type = config('billing.types.2');
            // payment here
            // $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,0,$diagnostic->id,$diagnostic->patient_id,$diagnostic->patient_phone);
            $total_amount = 0;
            if($request->reward_points_final > 0 || isset($request->payment_modes)){
                // payment create
                $request->merge([
                    'bill_id' => $paymentBill->id,
                    'date' => date('Y-m-d'),
                    'recpit_no' => $this->paymentService->recpitIncrementId('myMD','DG-receipt',8),
                    'status' => 'Paid',
                    'created_by' => $this->createdBy()
                ]);
                $this->paymentService->setRequest($request->except('payment_modes','amounts','payment_list','upi_mode'));
                $payment = $this->paymentService->add();
                // payment details
                
                if (isset($request->payment_modes)) {
                    foreach ($request->payment_modes as $key => $row) {
                        if ($row == 'upi') {
                            $payment_details = $request->payment_list[$key].' '.$request->upi_mode;
                        }
                        else {
                            $payment_details = $request->payment_list[$key];
                        }
                        $request['payment_id'] = $payment->id;
                        $request['payment_mode'] = $row;
                        $request['amount'] = $request->amounts[$key];
                        $request['payment_details'] = $payment_details;
                        $total_amount += $request->amounts[$key];
                        $this->paymentDetailService->setRequest($request->except('status','remarks','payment_modes','amounts','payment_list','upi_mode'));
                        $this->paymentDetailService->add();
                        // dd($this->paymentDetailService);
                    }
                }
                // // reward points
                $request['openning_points'] = $this->rewardService->rewardPoints($phone);
                // reward points debit
                if($request->reward_points_final > 0){
                    $reward = [
                        'phone_no' => $request->reward_phone_no,
                        'date' => date('Y-m-d H:i:s'),
                        'type' => $pay_type,
                        'point' => $request->reward_points_final*-1,
                        'credit_debit' => 2,
                        'is_redeem' => 2,
                        'bill_id' => $bill_id,
                        // 'created_by' => $this->createdBy()
                    ];
                    $this->rewardService->setRequest($reward);
                    $this->rewardService->add();
                }
                // reward points credit
                $due_amount_for_reward = ($paymentBill->total_amount - $request['redeem_points']) - ($paymentBill->paid_amount + $total_amount);
                if(($paymentBill->total_amount > 0 && $diagnostic->fully_paid == 1 && $due_amount_for_reward == 0) || ($diagnostic->fully_paid == 2 && $total_amount > 0)){
                    $percentage = $this->rewardService->getPercentage(4);
                    if ($paymentBill->total_amount > 0 && $diagnostic->fully_paid == 1 && $due_amount_for_reward == 0) {
                        $reward_point_credit = $this->paymentBillService->rewardCalculation($paymentBill->total_amount,$percentage);
                        // $this->diagnosticService->findById($service_id);
                        $this->diagnosticService->setRequest(['fully_paid' => 2]);
                        $this->diagnosticService->update();
                    }
                    else {
                        $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
                    }
                    $reward = [
                        'phone_no' => $phone,
                        'date' => date('Y-m-d H:i:s'),
                        'type' => $pay_type,
                        'point' => $reward_point_credit,
                        'credit_debit' => 1,
                        'is_redeem' => 1,
                        'bill_id' => $bill_id,
                        // 'created_by' => $this->createdBy()
                    ];
                    $this->rewardService->setRequest($reward);
                    $this->rewardService->add();
                }
                // payment table reward point update
                $request['amount'] = $total_amount;
                // $request['discount'] = $total_discount;
                // $request['gross_total'] = ($total_amount+$total_discount);
                $request['redeem_points'] = $request->reward_points_final == '' ? 0 : $request->reward_points_final;
                $request['closing_points'] = $this->rewardService->rewardPoints($phone);
                $payment = $this->paymentService->findById($payment->id);
                $this->paymentService->setRequest($request->except(
                    'bill_id','date','remarks','recpit_no','status',
                    'patient_id','remarks','payment_modes',
                    'amounts','payment_list','upi_mode'
                ));
                $this->paymentService->update();
            }
            $pay_amount = $paymentBill->total_amount - $request['redeem_points'];
            $paid_amount = $paymentBill->paid_amount + $total_amount;
            $due_amount = $pay_amount - $paid_amount;
            $this->paymentBillService->findById($paymentBill->id);
            $this->paymentBillService->setRequest([
                'total_amount' => $pay_amount,
                'paid_amount' => $paid_amount,
                'due_amount' => $due_amount,
                'modified_by' => $this->modifiedBy()
            ]);
            $this->paymentBillService->update();
            $apiResponse = $this->dueclearanceItdose($request,$diagnostic->unique_id);
            $this->response['success']  = true;
            $this->response['message']  = 'Payment settlement completed successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    private function dueclearanceItdose($request,$work_order_id)
    {
        $dataArray = [];
        // $dataArray['url'] = 'https://lab.mymdindia.com/mymd_uat/api/allapi/Dueclearance';
        $dataArray['template_id'] = 4;
        $dataArray['work_order_id'] = $work_order_id;
        $dataArray['created_by'] = $this->createdBy();

        $dataArray['playload'] = [
            "BillNo" => $work_order_id, //work order id
            "dueamount" => ($request->total_amount+($request->reward_points_final ?? 0)), // total due amount
            "TransactionID" => "", // patient phone
        ];
        
        if (isset($request->payment_modes)) {
            foreach ($request->payment_modes as $key => $row) {
                switch ($row) {
                    case 'cash':
                        $paymentType = 'Cash';
                        break;
                    case 'credit_card':
                        $paymentType = 'Credit Card';
                        break;
                    case 'debit_card':
                        $paymentType = 'Debit Card';
                        break;
                    case 'upi':
                        $paymentType = 'PayTM';
                        break;
                    default:
                        $paymentType = 'NA';
                        break;
                }
                $paymentList = [
                    "Amount" => $request->amounts[$key],
                    "Bankname" => "",
                    "payMode" => $paymentType, // Cash, Credit Card, Debit Card, PayTM
                ];
                $dataArray['playload']['paymentMode'][] = $paymentList;
            }
        }
        if($request->reward_points_final > 0){ // if get redeem point
            $dataArray['playload']['paymentMode'][] = [
                "payMode" => "Redemption Point",
                "Amount" => $request->reward_points_final,
                "Bankname" => ""
            ];
        }
        // dd($dataArray);
        dispatch(new DiagnosticApiJob($this->diagnosticApiService,$dataArray))->onQueue('itdose_due_api');
        // $this->diagnosticApiService->setDataArray($dataArray);
        // $apiResponse = $this->diagnosticApiService->callApi();
        return $dataArray;
    }
    public function refundSettlementForm($id,Request $request)
    {
        try {
            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // dd($diagnostic->paymentBill);
            $paymentBill = $diagnostic->paymentBill;
            $data = [
                'diagnostic' => $diagnostic,
                'patient_detail' => $diagnostic->patient,
                // 'diagnosticBill' => $diagnostic->diagnosticBill,
                'diagnosticBillHC' => $diagnostic->diagnosticBillHC,
                'diagnosticBillItem' => $diagnostic->diagnosticBillItem,
                // 'diagnosticBreakupItemMergePackage' => $diagnostic->diagnosticBreakupItemMergePackage(),
                // 'diagnosticBreakupItemGroup' => $diagnostic->diagnosticBreakupItemGroup,
                'paymentBill' => $paymentBill,
                // 'diagnosticPayment' => $paymentBill->payments,
                'reward_redeem' => $paymentBill->payments->sum('redeem_points'),
            ];
            // dd($data['diagnosticBillItem'][0]['test']);//package_id,report_delivery_date,is_urgent
            $patient_id = $diagnostic->patient_id;
            $phone = $diagnostic->patient_phone;
            // dd($data['patient_detail']->membershipRegistrations);
            $service = $data['patient_detail']->membershipRegistrations[0]->memberships->name ?? '';
            // dd($data['diagnostic']->doctor->first_name);
            $distance_list = $this->diagnosticService->allSampleHomecollectionCharges()->toArray();
            // dd($data['diagnostic']->offered_type,$data['diagnostic']->offered_id);
            
            $list = [
                // 'visit_type' => config('diagnostic.visit_type'),
                // 'status_list' => Arr::except(config('diagnostic.status_list'), [2,3,4,5,6,7,8,9,10]),
                // 'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                // 'clinic_list' => $this->diagnosticService->allClinics()->toArray(),
                // 'test_list' => $this->diagnosticService->allTests()->toArray(),
                // 'distance_list' => $distance_list,
                'patient_phone' => $phone,
                'reward_points' => $this->rewardService->rewardPoints($phone),
            ];
            // dd(empty($list['offer_list']));
            $this->response['form'] = view('diagnostic::diagnostic.api.refundSettlementForm',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function updateRefundSettlement($id,PaymentSettlementRequest $request)
    {
        try {
            $request->validated();
            $diagnostic = $this->diagnosticService->findById($id);
            // dd($diagnostic->patient);
            if (!$diagnostic) {
                $this->response['success']    = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            $phone = $diagnostic->patient_phone;
            $paymentBill = $diagnostic->paymentBill;
            $bill_id = $paymentBill->bill_show_id;
            $pay_type = config('billing.types.2');
            // payment here
            // $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,0,$diagnostic->id,$diagnostic->patient_id,$diagnostic->patient_phone);
            $total_amount = 0;
            if(isset($request->payment_modes)){
                // payment create
                $request->merge([
                    'bill_id' => $paymentBill->id,
                    'date' => date('Y-m-d'),
                    'recpit_no' => $this->paymentService->recpitIncrementId('myMD','DG-receipt',8),
                    'status' => 'Refund',
                    'created_by' => $this->createdBy()
                ]);
                $this->paymentService->setRequest($request->except('payment_modes','amounts','payment_list','upi_mode'));
                $payment = $this->paymentService->add();
                // payment details
                
                if (isset($request->payment_modes)) {
                    foreach ($request->payment_modes as $key => $row) {
                        if ($row == 'refund_reward_points') {
                            $request['openning_points'] = $this->rewardService->rewardPoints($phone);
                            $reward = [
                                'phone_no' => $phone,
                                'date' => date('Y-m-d H:i:s'),
                                'type' => $pay_type,
                                'point' => $request->amounts[$key],
                                'credit_debit' => 1,
                                'is_redeem' => 3,
                                'bill_id' => $bill_id,
                                'created_by' => $this->createdBy()
                            ];
                            $this->rewardService->setRequest($reward);
                            $this->rewardService->add();
                        }
                        if ($row == 'upi') {
                            $payment_details = $request->payment_list[$key].' '.$request->upi_mode;
                        }
                        else {
                            $payment_details = $request->payment_list[$key];
                        }
                        $request['payment_id'] = $payment->id;
                        $request['payment_mode'] = $row;
                        $request['amount'] = $request->amounts[$key];
                        $request['payment_details'] = $payment_details;
                        $total_amount += $request->amounts[$key];
                        $this->paymentDetailService->setRequest($request->except('status','remarks','payment_modes','amounts','payment_list','upi_mode'));
                        $this->paymentDetailService->add();
                        // dd($this->paymentDetailService);
                    }
                }
                // payment table reward point update
                $request['amount'] = $total_amount*(-1);// for refund
                // $request['discount'] = $total_discount;
                // $request['gross_total'] = ($total_amount+$total_discount);
                $request['redeem_points'] = 0;
                $request['closing_points'] = $this->rewardService->rewardPoints($phone);
                $payment = $this->paymentService->findById($payment->id);
                $this->paymentService->setRequest($request->except(
                    'bill_id','date','remarks','recpit_no','status',
                    'patient_id','remarks','payment_modes',
                    'amounts','payment_list','upi_mode'
                ));
                $this->paymentService->update();
            }
            $pay_amount = $paymentBill->total_amount;
            $paid_amount = $paymentBill->paid_amount - $total_amount;
            $due_amount = $pay_amount - $paid_amount;
            $this->paymentBillService->findById($paymentBill->id);
            $this->paymentBillService->setRequest([
                'total_amount' => $pay_amount,
                'paid_amount' => $paid_amount,
                'due_amount' => $due_amount,
                'modified_by' => $this->modifiedBy()
            ]);
            $this->paymentBillService->update();
            $apiResponse = $this->refundClearanceItdose($request,$diagnostic->unique_id);
            $this->response['success']  = true;
            $this->response['message']  = 'Refund Payment completed successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    private function refundClearanceItdose($request,$work_order_id)
    {
        $dataArray = [];
        // $dataArray['url'] = 'https://lab.mymdindia.com/mymd_uat/api/allapi/RefundPatient';
        $dataArray['template_id'] = 6;
        $dataArray['work_order_id'] = $work_order_id;
        $dataArray['created_by'] = $this->createdBy();

        $dataArray['playload'] = [
            "BillNo" => $work_order_id, //work order id
            "dueamount" => $request->total_amount, // total refund amount
            "IsRefund" => 1,// satic
            "TransactionID" => "", // patient phone
        ];
        
        if (isset($request->payment_modes)) {
            foreach ($request->payment_modes as $key => $row) {
                switch ($row) {
                    case 'cash':
                        $paymentType = 'Cash';
                        break;
                    case 'credit_card':
                        $paymentType = 'Credit Card';
                        break;
                    case 'debit_card':
                        $paymentType = 'Debit Card';
                        break;
                    case 'upi':
                        $paymentType = 'PayTM';
                        break;
                    case 'refund_reward_points':
                        $paymentType = 'Redemption Point';
                        break;
                    default:
                        $paymentType = 'NA';
                        break;
                }
                $paymentList = [
                    "Amount" => $request->amounts[$key],
                    "Bankname" => "",
                    "payMode" => $paymentType, // Cash, Credit Card, Debit Card, PayTM
                ];
                $dataArray['playload']['paymentMode'][] = $paymentList;
            }
        }
        // dd($dataArray);
        dispatch(new DiagnosticApiJob($this->diagnosticApiService,$dataArray))->onQueue('itdose_refund_api');
        // $this->diagnosticApiService->setDataArray($dataArray);
        // $apiResponse = $this->diagnosticApiService->callApi();
        return $dataArray;
    }
    public function uploadPrescription(Request $request)
    {
        try {
            // $request->validated();
            // dd($request->all());
            $diagnostic = $this->diagnosticService->findById($request->sample_id);
            if (!$diagnostic) {
                $this->response['success']  = false;
                $this->response['message']  = 'Diagnostic not found!';
                return response()->json($this->response);
            }
            // dd($diagnostic);
            if ($request->hasFile('prescription_upload_file')) {
                $fileinfo = $request->file('prescription_upload_file');
                $this->imageUploadService->setFieldName('prescription_upload_file');
                $this->imageUploadService->setFilePath('Diagnostic/Prescription/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            // dd($request->all());
            $this->diagnosticService->setRequest($request->except('sample_id'));
            $diagnostic = $this->diagnosticService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Prescription has been uploaded successfully!';
            $this->response['data']     = [];
            $this->response['res_data'] = $diagnostic;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listPhleboDashboard(Request $request)
    {
        try {
            $filter = $request['filter'];
            $filter['phlebo_id'] =  [
                'type' => 'eq',
                'value' => auth()->user()->id
            ];
            $filter['type_of_collection'] =  [
                'type' => 'eq',
                'value' => 'HC'
            ];
            // $filter['date_of_collection'] =  [
            //     'type' => 'eq',
            //     'value' => date('Y-m-d')
            // ];
            $request->merge([
                'filter' => $filter
            ]);

            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $request['with'] = [
                'tests' => 'id,sample_collection_id,item_id',
                // 'diagnosticBreakupItem' => 'id,sample_collection_id',
                // 'diagnosticBreakupReportedTest' => 'id,sample_collection_id',
                'assignPhlebotomists' => 'id,sample_id,role_id,phlebo_id,schedule_time,remarks,test_count,collected_testcount,handover_testcount',
            ];
            $request['withFunc'] = [
                'paymentBill as total_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'total_amount'
                ],
                'paymentBill as due_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'due_amount'
                ]
            ];
            array_push(
                $this->diagnosticService->columns,
                'patients.name as patient_name',
                // 'clinics.clinic_name as clinic_name'
            );
            $this->diagnosticService->setRequest($request);
            $data = $this->diagnosticService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticService->getRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            $itdose_report_url = config('diagnostic.itdose_report_url');
            // dd($status_list);
            $this->response['tbody'] = view('diagnostic::diagnostic.api.listPhleboDashboard',compact('data','permissionPage','status_list','itdose_report_url'))->render();
            $this->response['tfoot'] = $this->diagnosticService->paginationCustom();
            // $this->response['headerAction'] = view('diagnostic::diagnostic.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listSampleStatus(Request $request)
    {
        try {
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $user_clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $user_clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                case 7:
                    $user_clinic_id = auth()->user()->phlebotomist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            if($user_clinic_id){
                $clinic_id = [
                    'type' => 'eq',
                    'value' => $user_clinic_id
                ];
            }
            else{
                $clinic_id = $request->filter['clinic_id'] ?? null;
            }
            $date_of_collection = $request->filter['date_of_collection'] ?? null;
            $join_condition = [];
            if($clinic_id){
                $join_condition['clinic_id'] = $clinic_id;
            }
            if($date_of_collection){
                $join_condition['date_of_collection'] = $date_of_collection;
            }
            $request['join'] = [
                'sample_collections' => [
                    'reletion' => [
                        'prefix' => 'sample_collection_id',
                        'suffix' => 'id'
                    ],
                    'condition' => $join_condition
                ]
            ];
            $request['with'] = [
                'test' => 'id,test_name',
                // 'sampleCollection' => 'id,date_of_collection,patient_phone',
                'sampleCollection.clinic' => 'id,clinic_name',
                'sampleCollection.patient' => 'id,name,birthdate,sex',
            ];
            // dd($request->all());
            array_push(
                $this->diagnosticBreakupTestService->columns,
                'sample_collections.date_of_collection as date_of_collection',
                'sample_collections.patient_phone as patient_phone',
            );
            $this->diagnosticBreakupTestService->setRequest($request->except('filter.clinic_id','filter.date_of_collection'));
            $data = $this->diagnosticBreakupTestService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticBreakupTestService->getRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $status_list = config('diagnostic.status_list');
            $itdose_report_url = config('diagnostic.itdose_report_url');
            // dd($status_list);
            $this->response['tbody'] = view('diagnostic::diagnostic.api.sampleStatusList',compact('data','permissionPage','status_list','itdose_report_url','user_clinic_id'))->render();
            $this->response['tfoot'] = $this->diagnosticBreakupTestService->paginationCustom();
            // $this->response['headerAction'] = view('diagnostic::diagnostic.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
}
