<?php

namespace Modules\Settings\Services;

use App\Services\ApplicationDefaultService;
use Modules\Settings\Models\ReviewsSurveyResultChild;
use DB;

class ReviewSurveyResultChildService extends ApplicationDefaultService
{
    public $entity;
    public $columns = [
        'id',
        'result_id',
        'question_no',
        'question',
        'answer',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'result_id',
        'question_no',
        'question',
        'answer',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(ReviewsSurveyResultChild $entity) {
        $this->entity =$entity;
    }
    public function allReviewSurveyResultChilds(){
        $this->entity = $this->entity->select($this->columns)
            ->where('status',1)
            ->get();
        return $this->entity;
    }
}
