<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
        <td><?php echo e($row['membership_registrations']['patients']['name'] ?? ''); ?></td>
        <td><?php echo e($row['membership_registrations']['phone'] ?? ''); ?></td>
        <td><?php echo e($row['membership_registrations']['start_date'] ?? ''); ?> to <?php echo e($row['membership_registrations']['end_date'] ?? ''); ?></td>
        <td><?php echo e($row['membership_registrations']['registration_no'] ?? ''); ?></td>
        <td><?php echo e($row['membership_registrations']['memberships']['name'] ?? ''); ?></td>
        <td>
            <h6 class="fw-bold text-primary"><?php echo e($row['token']); ?></h6>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Membership\resources/views/otcBalance/api/list.blade.php ENDPATH**/ ?>