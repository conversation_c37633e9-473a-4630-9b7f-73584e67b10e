<?php

namespace Modules\Appointment\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Appointment\Http\Requests\AppointmentRequest;
use Modules\Appointment\Http\Requests\AppointmentTeleConsultationRequest;
use Modules\Appointment\Http\Requests\AppointmentVideoConsultationRequest;
use Modules\Appointment\Http\Requests\AppointmentBillRequest;
use Modules\Appointment\Http\Requests\VitalsRequest;
use Modules\Appointment\Http\Requests\AssignDoctorRequest;
use Modules\Appointment\Http\Requests\AssignDoctorVideoRequest;
use Modules\Appointment\Http\Requests\CaptureVitalsRequest;
use Modules\Appointment\Services\AppointmentService;
use Modules\Billing\Services\PaymentBillService;
use Modules\Billing\Services\PaymentService;
use Modules\Billing\Services\PaymentDetailService;
use Modules\Appointment\Services\VitalsService;
use Modules\Appointment\Services\PrescriptionService;
use Modules\Reward\Services\RewardService;
use App\Services\SMSMsgService;
use App\Services\ZoomService;
use Modules\Appointment\Models\AppointmentVideoConsult;
use Modules\Schedule\Services\ScheduleService;
use Modules\Users\Services\UserService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use DB;
use PDF;

class AppointmentController extends Controller
{
    private $appointmentService;
    private $paymentBillService;
    private $paymentService;
    private $paymentDetailService;
    private $vitalsService;
    private $prescriptionService;
    private $smsMsgService;
    private $rewardService;
    private $scheduleService;
    private $zoomService;
    private $userService;

    public function __construct(AppointmentService $appointmentService, PaymentBillService $paymentBillService,PaymentService $paymentService, PaymentDetailService $paymentDetailService,VitalsService $vitalsService,PrescriptionService $prescriptionService,SMSMsgService $smsMsgService,RewardService $rewardService,ScheduleService $scheduleService, ZoomService $zoomService, UserService $userService)
    {
        $this->appointmentService = $appointmentService;
        $this->paymentBillService = $paymentBillService;
        $this->paymentService = $paymentService;
        $this->paymentDetailService = $paymentDetailService;
        $this->vitalsService = $vitalsService;
        $this->prescriptionService = $prescriptionService;
        $this->smsMsgService = $smsMsgService;
        $this->rewardService = $rewardService;
        $this->scheduleService = $scheduleService;
        $this->zoomService = $zoomService;
        $this->userService = $userService;
    }
    public function index($stat, Request $request)
    {
        // $this->smsMsgService->setFlowId('63d8db35d6fc052111700572');
        // $this->smsMsgService->sendSmsToRecipients(6000958785);
        // $variable = [
        //     'drname' => 'test doctor',
        //     'center' => 'Chinsurah',
        //     'name' => 'Amit Ojha',
        //     'date' => '2024-10-24',
        //     'time' => '10:00 AM To 06:00 PM',
        //     'mobile' => 7278303933
        // ];
        // $this->smsMsgService->setVariable($variable);
        // $data = $this->smsMsgService->send();
        // dd($data);
        session()->put('curStat', $stat);
        $data = [
            'clinic_list' => $this->appointmentService->allClinics()->toArray(),
            'doctor_list' => $this->userService->allUserWithRole('Doctor')
        ];
        // dd($data);
        return view('appointment::appointment.index',compact('data','stat'));
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        // $appointment_type = $request->id ? $this->appointmentService->findById($id)->appointment_type : null;
        // dd($appointment_type);
        $stat = session()->get('curStat');
        return view('appointment::appointment.add',compact('id','stat'));
    }
    public function addBill(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $stat = session()->get('curStat');
        return view('appointment::appointment.addBill',compact('id','stat'));
    }
    public function addVital(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $stat = session()->get('curStat');
        $vital_id = $request->vital_id ? $request->vital_id : null;
        return view('appointment::appointment.addVital',compact('id','vital_id','stat'));
    }
    public function assignDoctor(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $stat = session()->get('curStat');
        return view('appointment::appointment.assignDoctor',compact('id','stat'));
    }
    public function assignDoctorVideo(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $stat = session()->get('curStat');
        return view('appointment::appointment.assignDoctorVideo',compact('id','stat'));
    }
    public function captureVitals(Request $request)
    {
        return view('appointment::appointment.captureVitals');
    }
    public function captureVitalsAdd(Request $request)
    {
        return view('appointment::appointment.captureVitalsAdd');
    }
    public function doctorVideoConsultation(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $stat = session()->get('curStat');
        $appointment = $this->appointmentService->findById($id);
        $meeting_dtl = $appointment->appointmentVideoConsult;
        // $role = $this->getUserRole();
        // if($role->id == 2){
        //     $meeting_role = 0;
        //     $display_name = auth()->user()->doctor;
        // }
        // else {
        //     $meeting_role = 1;
        //     $display_name = 'Clinic';
        // }
        // dd($display_name);
        $meeting_role = 0;
        $display_name = 'Clinic';
        $cur_meeting = [
            'id' => $meeting_dtl->id,
            'password' => $meeting_dtl->password,
            'meeting_role' => $meeting_role,//0=>Attendee,1=>Host
            'meeting_auto_join' => 1,//0=>default join,1=>auto join
            'name' => $display_name,
            'sdk_client_id' => $this->zoomService->getSDKClientId(),
            'sdk_client_secret' => $this->zoomService->getSDKClientSecret()
        ];
        $list = [
            'meeting_dtl' => $meeting_dtl,
            'cur_meeting' => $cur_meeting
        ];
        return view('appointment::appointment.doctorVideoConsultation',compact('id','stat','appointment','list'));
    }
    public function list(Request $request)
    {
        try {
            $request['join'] = [
                // 'patients' => [
                //     'reletion' => [
                //         'prefix' => 'patient_id',
                //         'suffix' => 'id'
                //     ]
                // ],
                'users' => [
                    'reletion' => [
                        'prefix' => 'doctor_id',
                        'suffix' => 'id'
                    ]
                ],
                'clinics' => [
                    'reletion' => [
                        'prefix' => 'clinic_id',
                        'suffix' => 'id'
                    ]
                ],
                'vitals' => [
                    'reletion' => [
                        'prefix' => 'id',
                        'suffix' => 'appointment_id'
                    ]
                ],
                'prescriptions' => [
                    'reletion' => [
                        'prefix' => 'id',
                        'suffix' => 'appointment_id'
                    ]
                ]
            ];
            $request['with'] = [
                'patientWithMembership' => 'id,name',
                'sampleCollectionMapSource' => 'id,type,service_id,sample_collection_id',
                'prescription.prescriptionChildTests' => 'id,prescription_id,type',
            ];
            // $request['collect'] = [
            //     // 'users' => [
            //     //     'table' => 'users',
            //     //     'condition' => 'id',
            //     //     'jsonValue' => 'user_id',
            //     //     'select' => 'username'
            //     // ],
            //     'membership_registrations' => [
            //         'table' => 'membership_registrations',
            //         'static_condition' => ['category_id'=>1,'status'=>3],
            //         'condition' => 'patient_id',
            //         'jsonValue' => 'patient_id',
            //         'select' => 'card_type,registration_no,end_date'
            //     ]
            // ];
            // $request['sub_collect'] = [
            //     'memberships' => [
            //         'table' => 'memberships',
            //         'condition' => 'id',
            //         'jsonValue' => ['sub_query'=>'membership_registrations','field'=>'card_type'],
            //         'select' => 'name'
            //     ]
            // ];
            // dd($request['filter']);
            $filter = $request['filter'];
            $role = $this->getUserRole();
            switch ($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            if ($role->id == 10) {
                $filter['status'] =  [
                    'type' => 'eq',
                    'value' => [1,2,7]
                ];
                // array_push($filter,$status);
                $status_list = Arr::except(config('appointment.status_list'), [3,4,5,6,8]);
            }
            elseif($role->id == 2){
                $filter['doctor_id'] =  [
                    'type' => 'eq',
                    'value' => auth()->user()->id
                ];
                $status_list = Arr::except(config('appointment.status_list'), []);
            }
            elseif(in_array($role->id, [4,5,6])){
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
                $status_list = Arr::except(config('appointment.status_list'), []);
            }
            else{
                $status_list = Arr::except(config('appointment.status_list'), []);
            }
            if($request->stat == 'today'){
                $filter['date'] =  [
                    'type' => 'eq',
                    'value' => date('Y-m-d')
                ];
            }
            $request->merge([
                'filter' => $filter
            ]);
            // dd($filter);
            array_push($this->appointmentService->columns,'users.username as username','clinics.clinic_name as clinic_name','vitals.id as vital_id','prescriptions.id as prescription_id','prescriptions.doctor_id as prescription_doctor_id');
            $this->appointmentService->setRequest($request);
            $this->appointmentService->findAll();
            $this->response['success']  = true;
            $this->appointmentService->getRows();
            // $this->appointmentService->collectionRows();
            $data = $this->appointmentService->checkPreviousPrescriptions(); // check previous prescription
            // dd($data);
            $permissionPage = $this->getPermissionList();
            // dd($status_list);
            $this->response['tbody'] = view('appointment::appointment.api.list',compact('data','permissionPage','status_list','role','clinic_id'))->render();
            $this->response['tfoot'] = $this->appointmentService->paginationCustom();
            $this->response['headerAction'] = view('appointment::appointment.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function listFamily(Request $request)
    {
        $this->response['success']  = true;
        $phone = $request->phone;
        $this->response['data'] = [];
        $list = [
            'patients' => $this->appointmentService->getFamilys($phone)
        ];
        // dd($list);
        $parent_id = $this->appointmentService->getPatientID($phone);
        if (!isset($parent_id)) {
            $parent_id = 0;
        }
        // dd($phone,$parent_id,$list);
        $html = '<tr>';
            $html .= '<td colspan="5" class="border-0">';
            if (count($list['patients']) > 0) {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(0,'.$parent_id.','.$request->phone.')">Add Member</button>';
            }
            else {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(1,'.$parent_id.','.$request->phone.')">Add Patient</button>';
            }
            $html .= '</td>';
        $html .= '</tr>';
        // dd($list['patients'],$list['patients'][0]->membershipRegistrations[0]->memberships);
        $this->response['tbody'] = view('appointment::appointment.api.listFamily',compact('list','parent_id','phone'))->render();
        $this->response['tfoot'] = $html;
        return response()->json($this->response);
    }
    public function listSlot(Request $request)
    {
        try {
            $role = $this->getUserRole();
            switch($role->id) {
                case 5: // Receptionist
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            // dd($clinic_id);
            $data = $this->appointmentService->getSlots($request->all());
            $list = [
                'slot_count' => count($data),
                'user_clinic_id' => $user_clinic_id
            ];
            $this->response['success']  = true;
            $this->response['list'] = $list;
            $this->response['slots'] = view('appointment::appointment.api.slotDropdown',compact('data'))->render();
            // dd($this->response);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function create(Request $request)
    {
        // dd($request->appointment_type);
        try {
            $id = null;
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            // $appointment_type = $request->appointment_type;
            $this->response['success']  = true;
            $patient = $this->appointmentService->findPatient($patient_id);

            // dd($otc_check);
            $data = [
                'patient_detail' => $patient
            ];
            $doctor_list = $this->userService->allUserWithRole('Doctor');
            $role = $this->getUserRole();
            switch($role->id) {
                case 5: // Receptionist
                    $user_clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            if($user_clinic_id){
                $doctor_ids = DB::table('time_schedules')
                    ->whereNotNull('doctor_id')
                    ->where('clinic_id',$user_clinic_id)
                    ->groupBy('doctor_id')
                    ->pluck('doctor_id')
                    ->toArray();
                $doctor_list = $doctor_list->whereIn('id',$doctor_ids);
            }
            $service = $patient->membershipRegistrations[0]->memberships->name ?? '';
            $list = [
                'doctor_list' => $doctor_list,
                'status_list' => Arr::except(config('appointment.status_list'), [4,5,6,7,8]),
                'appointment_type_list' => Arr::except(config('appointment.appointment_type_list'), [2]),
                'user_clinic_id' => $user_clinic_id,
                'patient_phone' => $phone,
            ];
            // if ($appointment_type == 2) {
            //     $list['tc_clinic_list'] = $this->appointmentService->allClinics();
            // }
            // dd($list);
            $this->response['form'] = view('appointment::appointment.api.addEdit',compact('id','patient_id','phone','data','list','service'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function add(AppointmentRequest $request)
    {
        try {
            $request->validated();
            $time_slot = isset($request->time_slot) ? explode(",",$request->time_slot) : '';

            $request->merge([
                'created_by' => $this->createdBy(),
                'payment_status' => 'unpaid',
                'data_source' => 1,
                'time_slot' => $time_slot[0],
                'schedule_id' => $time_slot[1],
            ]);
            // dd($request->all());
            $this->appointmentService->setRequest($request);
            $check_appo = $this->appointmentService->checkAppointment();
            // dd($check_appo);
            if($check_appo > 0){
                $this->response['success'] = false;
                $this->response['message'] = 'This patient has already an appointment.';
                return response()->json($this->response);
            }
            $appointment = $this->appointmentService->add();
            // dd($appointment->userDoctors);
            // sms send
            $this->smsMsgService->setFlowId('63d8db35d6fc052111700572');
            $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
            $variable = [
                'drname' => $appointment->userDoctors->username,
                'center' => $appointment->clinics->clinic_name,
                'name' => $appointment->patients->name,
                'date' => $appointment->date,
                'time' => $appointment->time_slot,
                'mobile' => $appointment->clinics->clinic_phno
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'OPD',
                'campaign_name' => 'OPD appointment booking',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['message']  = 'Appointment has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function edit($id)
    {
        try {
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // $this->response['data']     = $appointment;
            $data = $appointment;
            $list = [
                'doctor_list' => $this->userService->allUserWithRole('Doctor'),
                'clinic_list' => $this->appointmentService->getSlots(
                    [
                        "filter" => [
                            "doctor_id" => $data['doctor_id']
                        ],
                        "select" => "clinic_id",
                        "groupby" => "clinic_id",
                        "collect" => [
                            "name" => [
                                "table" => "clinics",
                                "condition" => "id",
                                "jsonValue" => "clinic_id",
                                "select" => "clinic_name"
                            ]
                        ]
                    ]
                ),
                'date_list' => $this->appointmentService->getSlots(
                    [
                        "filter" => [
                            "doctor_id" => $data['doctor_id'],
                            "clinic_id" => $data['clinic_id']
                        ],
                        "date_filter" => date('Y-m-d'),
                        "select" => "date as name",
                        "groupby" => "date"
                    ]
                ),
                'time_slot_list' => $this->appointmentService->getSlots(
                    [
                        "filter" => [
                            "doctor_id" => $data['doctor_id'],
                            "clinic_id" => $data['clinic_id'],
                            "date" => $data['date']
                        ],
                        "select" => "CONCAT(s_time, ' To ', e_time) AS name, id as schedule_id"
                    ]
                ),
            ];

            $this->response['form'] = view('appointment::appointment.api.addEdit',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function update($id,AppointmentRequest $request)
    {
        try {
            $request->validated();

            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']  = false;
                $this->response['message']  = 'appointment not found!';
                return response()->json($this->response);
            }
            $time_slot = isset($request->time_slot) ? explode(",",$request->time_slot) : '';
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'time_slot' => $time_slot[0],
                'schedule_id' => $time_slot[1],
            ]);
            $this->appointmentService->setRequest($request);
            $this->appointmentService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Appointment has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function createTeleConsultation(Request $request)
    {
        // dd($request->appointment_type);
        try {
            $id = null;
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $clinic_id = $request->clinic_id;
            $this->response['success']  = true;
            $patient = $this->appointmentService->findPatient($patient_id);

            // dd($otc_check);
            $data = [
                'patient_detail' => $patient
            ];

            $list = [
                'clinic_list' => $this->appointmentService->allClinics()
            ];

            // dd($list);
            $this->response['form'] = view('appointment::appointment.api.createTeleConsultation',compact('id','patient_id','phone','data','list','clinic_id'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addTeleConsultation(AppointmentTeleConsultationRequest $request)
    {
        try {
            $request->validated();

            // $time_slot = isset($request->time_slot) ? explode(",",$request->time_slot) : '';

            $request->merge([
                'created_by' => $this->createdBy(),
                'doctor_id' => 0,
                'appointment_type' => 2,
                'status' => 3,
                'payment_status' => 'unpaid',
                'data_source' => 7,
                // 'time_slot' => $time_slot[0],
                // 'schedule_id' => $time_slot[1],
            ]);
            // dd($request->all());
            $this->appointmentService->setRequest($request->except('symptoms_cheiefcomplants'));
            // $check_appo = $this->appointmentService->checkAppointment();

            // // dd($check_appo);
            // if($check_appo > 0){
            //     $this->response['success'] = false;
            //     $this->response['message'] = 'This patient has already an appointment.';
            //     return response()->json($this->response);
            // }
            $appointment = $this->appointmentService->add();
            if ($request->symptoms_cheiefcomplants) {//chief_complaints
                $request->merge([
                    'appointment_id' => $appointment->id,
                    'chief_complaints' => $request->symptoms_cheiefcomplants
                ]);
                $this->prescriptionService->setRequest($request->except('doctor_id','appointment_type','status','payment_status','data_source','patient_phone','clinic_id','date','symptoms_cheiefcomplants'));
                $this->prescriptionService->add();
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Appointment has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function createVideoConsultation(Request $request)
    {
        // dd($request->appointment_type);
        try {
            $id = null;
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $clinic_id = $request->clinic_id;
            $this->response['success']  = true;
            $patient = $this->appointmentService->findPatient($patient_id);

            // dd($otc_check);
            $data = [
                'patient_detail' => $patient
            ];

            $list = [
                'clinic_list' => $this->appointmentService->allClinics()
            ];

            // dd($list);
            $this->response['form'] = view('appointment::appointment.api.createVideoConsultation',compact('id','patient_id','phone','data','list','clinic_id'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addVideoConsultation(AppointmentVideoConsultationRequest $request)
    {
        try {
            $request->validated();
            $request->merge([
                'created_by' => $this->createdBy(),
                'doctor_id' => 0,
                'appointment_type' => 3,
                'status' => 3,
                'payment_status' => 'unpaid',
                'data_source' => 9,
            ]);
            $this->appointmentService->setRequest($request->except('symptoms_cheiefcomplants'));
            $appointment = $this->appointmentService->add();
            if ($request->symptoms_cheiefcomplants) {//chief_complaints
                $request->merge([
                    'appointment_id' => $appointment->id,
                    'chief_complaints' => $request->symptoms_cheiefcomplants
                ]);
                $this->prescriptionService->setRequest($request->except('doctor_id','appointment_type','status','payment_status','data_source','patient_phone','clinic_id','date','symptoms_cheiefcomplants'));
                $this->prescriptionService->add();
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Appointment has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function updateStatus($id,Request $request)
    {
        try {
            $appointment = $this->appointmentService->findById($id);
            // dd($appointment->status);
            if (!$appointment) {
                $this->response['success']  = false;
                $this->response['message']  = 'appointment not found!';
                return response()->json($this->response);
            }
            if($appointment->date != date('Y-m-d') && $request->status == 3){
                $this->response['success']  = false;
                $this->response['message']  = 'Arrived status can only be set for the current date.';
                return response()->json($this->response);
            }
            if($appointment->payment_status == 'unpaid' && $request->status == 4){
                $this->response['success']  = false;
                $this->response['message']  = 'Cannot proceed with doctor without bill payment.';
                return response()->json($this->response);
            }
            $check_engaged = $this->appointmentService->checkEngaged($appointment->schedule_id);
            // dd($check_engaged);
            if ($check_engaged > 0 && $request->status == 4) {
                $this->response['success']  = false;
                $this->response['message']  = 'Another patient has already been engaged.';
                return response()->json($this->response);
            }
            if($appointment->status == 4){// feedback sms with doctor status change
                $template_id = 1;// appointment survey form
                $review_survey = DB::table('reviews_survey_masters')->where('status',1)->where('id',$template_id)->first();
                $shortlink = $review_survey->short_link."?sTy=1&sId=" . $appointment->id . "&pId=" . base64_encode(base64_encode($appointment->patient_id));
                $this->smsMsgService->setFlowId('679a1a51d6fc0502812d8632');
                $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
                $variable = [
                    'var1' => $appointment->patients->name ?? '',
                    'var2' => $shortlink
                ];
                $this->smsMsgService->setVariable($variable);
                $data = $this->smsMsgService->send();
                $req_data = [
                    'created_by' => $this->createdBy(),
                    'event' => 'OPD',
                    'campaign_name' => 'Patient Feedback Link',
                    'status' => 2,
                    'reason' => ''
                ];
                $this->smsMsgService->add($req_data);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->appointmentService->setRequest($request);
            $this->appointmentService->update();

            if ($request->status == 7) {// sms send for cancel
                $this->smsMsgService->setFlowId('64e09405d6fc0540fd074a12');
                $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
                $variable = [
                    'name' => $appointment->patients->name,
                ];
                $this->smsMsgService->setVariable($variable);
                $data = $this->smsMsgService->send();
                $req_data = [
                    'created_by' => $this->createdBy(),
                    'event' => 'OPD',
                    'campaign_name' => 'OPD appointment cancellation',
                    'status' => 2,
                    'reason' => ''
                ];
                $this->smsMsgService->add($req_data);
            }
            elseif ($request->status == 8) {// sms send for complete
                if ($appointment->appointment_type == 2) {//teleconsultation
                    // sms send bill and prescription
                    $this->smsMsgService->setFlowId('65eef0dfd6fc0523a63850a3');
                    $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
                    $variable = [
                        'var' => route('appointment.viewBill', $appointment->id),
                        'var1' => route('prescription.viewPrescription', $appointment->id)
                    ];
                    // dd($variable);
                    $this->smsMsgService->setVariable($variable);
                    $data = $this->smsMsgService->send();
                    $req_data = [
                        'created_by' => $this->createdBy(),
                        'event' => 'OPD',
                        'campaign_name' => 'Send Bill and Prescription of OPD appointment',
                        'status' => 2,
                        'reason' => ''
                    ];
                    $this->smsMsgService->add($req_data);
                }
                else {
                    $this->smsMsgService->setFlowId('63d8db7bd6fc05791e047f23');
                    $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
                    $variable = [
                        'drname' => $appointment->userDoctors->username,
                        'center' => $appointment->clinics->clinic_name,
                    ];
                    $this->smsMsgService->setVariable($variable);
                    $data = $this->smsMsgService->send();
                    $req_data = [
                        'created_by' => $this->createdBy(),
                        'event' => 'OPD',
                        'campaign_name' => 'On successful completion of OPD appointment',
                        'status' => 2,
                        'reason' => ''
                    ];
                    $this->smsMsgService->add($req_data);
                }
            }
            
            $this->response['success']  = true;
            $this->response['message']  = 'Appointment has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function createBill($id)
    {
        try {
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // $this->response['data']     = $appointment;
            $data = $appointment;

            $list = [
                'patient_name' => $data->patients->name,
                'patient_sex' => $data->patients->sex,
                'patient_age' => $data->patients->birthdate,
                'patient_phone' => $data->patient_phone,
                'appointment_clinic' => $data->clinic_id,
                'appointment_type' => config('appointment.appointment_type_list')[$data->appointment_type],
                'patient_membership' => $this->appointmentService->activeMembership($data->patient_id),
                'doctor_name' => $data->userDoctors->username,
                'payment_mode' => config('billing.payment_mode'),
                'visit_price' => $data->schedules->visit_price ?? 0,
                'reward_points' => $this->rewardService->rewardPoints($data->patient_phone)
            ];
            // dd($data->userDoctors->doctor->doctor_type);
            $discount = 0;
            $membership_registration_no = null;
            if (!empty($list['patient_membership']) && $data->userDoctors->doctor->doctor_type != 2) {// doctor type 2 is specialist doctor
                if ($list['appointment_clinic'] == $list['patient_membership'][0]['clinic_id'] || $list['patient_membership'][0]['card_type'] == 2) {
                    $discount = $list['visit_price'];
                    $membership_registration_no = $list['patient_membership'][0]['registration_no'];
                }
            }
            elseif (!empty($list['patient_membership']) && $data->userDoctors->doctor->doctor_type == 2) {// doctor type 2 is specialist doctor
                if ($list['appointment_clinic'] == $list['patient_membership'][0]['clinic_id'] || $list['patient_membership'][0]['card_type'] == 2) {
                    $check_followup_ab = DB::table('appointments')
                        ->where('patient_id', $data->patient_id)
                        ->where('doctor_id', $data->doctor_id)
                        ->where('clinic_id', $list['patient_membership'][0]['clinic_id'])
                        ->where('date', '>=', date('Y-m-d', strtotime('-7 days', strtotime($data->date))))
                        ->where('date', '<', $data->date)
                        ->count();
                    $check_followup_abp = DB::table('appointments')
                        ->where('patient_id', $data->patient_id)
                        ->where('doctor_id', $data->doctor_id)
                        ->where('date', '>=', date('Y-m-d', strtotime('-7 days', strtotime($data->date))))
                        ->where('date', '<', $data->date)
                        ->count();
                    if ($check_followup_ab > 0 || ($check_followup_abp > 0)) {
                        $discount = $list['visit_price'];
                        $membership_registration_no = $list['patient_membership'][0]['registration_no'];
                    }
                }
            }
            // dd($discount,$membership_registration_no);
            $list['discount'] = $discount;
            $list['membership_registration_no'] = $membership_registration_no;
            $list['gross_total'] = $list['visit_price'] - $discount;
            $stat = session()->get('curStat');
            // dd($data,$list);
            $this->response['form'] = view('appointment::appointment.api.billForm',compact('id','data','list','stat'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addBillPost($id,AppointmentBillRequest $request)
    {
        try {
            $request->validated();
            // dd($request->all());
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']  = false;
                $this->response['message']  = 'appointment not found!';
                return response()->json($this->response);
            }
            $bill_id = $this->paymentBillService->billingIncrementId($appointment->clinic_id,'OPD',8);
            $unique_queue_number = $this->appointmentService->queueIncrementId($appointment->schedule_id);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'payment_status' => 'paid',
                'unique_queue_number' => $unique_queue_number,
            ]);
            $this->appointmentService->setRequest($request->except('sub_total','discount','gross_total','remarks','payment_modes','amounts','payment_list','upi_mode'));
            $this->appointmentService->update();

            $pay_type = config('billing.types.1');
            // payment here
            $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$request->sub_total,$request->discount,$appointment->id,$appointment->patient_id,$appointment->patient_phone);

            // sms send for point calculation
            $this->smsMsgService->setFlowId('667a6881d6fc056d1126b902');
            $this->smsMsgService->sendSmsToRecipients($appointment->patient_phone);
            $variable = [
                'var' => $request->reward_points_final == '' ? 0 : $request->reward_points_final,
                'var1' => $reward_point_credit,
                'var2' => $request['closing_points']
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'OPD',
                'campaign_name' => 'Cash back point redemption success',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['message']  = 'Appointment billing has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    private function paymentWithReward($request,$bill_id,$pay_type,$bill_amount = 0,$discount = 0,$service_id,$patient_id,$phone)
    {
        $request->merge([
            'created_by' => $this->createdBy(),
            'bill_show_id' => $bill_id,
            'type' => $pay_type,//ServiceCategory->membership
            'service_id' => $service_id,
            'patient_id' => $patient_id,
            'bill_amount' => $bill_amount,
            'status' => 1
        ]);
        // dd($service->paymentBill);
        $this->paymentBillService->setRequest($request->except('remarks','payment_modes','amounts','payment_list','upi_mode'));
        $paymentBill = $this->paymentBillService->add();
        
        $total_amount = 0;
        $total_discount = $discount;
        $reward_point_credit = 0;
        if($request->reward_points_final > 0 || isset($request->payment_modes)){
            // payment create
            $request->merge([
                'bill_id' => $paymentBill->id,
                'date' => date('Y-m-d'),
                'recpit_no' => $this->paymentService->recpitIncrementId('myMD','OPD-receipt',8),
                'status' => 'Paid'
            ]);
            $this->paymentService->setRequest($request->except('phone','clinic_id','start_date','end_date','registration_no','category_id','card_type','remarks','payment_modes','amounts','payment_list','upi_mode'));
            $payment = $this->paymentService->add();
            // payment details
            
            if (isset($request->payment_modes)) {
                foreach ($request->payment_modes as $key => $row) {
                    if ($row == 'upi') {
                        $payment_details = $request->payment_list[$key].' '.$request->upi_mode;
                    }
                    else {
                        $payment_details = $request->payment_list[$key];
                    }
                    $request['payment_id'] = $payment->id;
                    $request['payment_mode'] = $row;
                    $request['amount'] = $request->amounts[$key];
                    $request['payment_details'] = $payment_details;
                    $total_amount += $request->amounts[$key];
                    $this->paymentDetailService->setRequest($request->except('patient_id','status','remarks','payment_modes','amounts','payment_list','upi_mode'));
                    $this->paymentDetailService->add();
                    // dd($this->paymentDetailService);
                }
            }
            // reward points
            $percentage = $this->rewardService->getPercentage(1);
            $reward_point_credit = $this->paymentBillService->rewardCalculation($total_amount,$percentage);
            $request['openning_points'] = $this->rewardService->rewardPoints($phone);
            // reward points debit
            if($request->reward_points_final > 0){
                $reward = [
                    'phone_no' => $request->reward_phone_no,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $request->reward_points_final*-1,
                    'credit_debit' => 2,
                    'is_redeem' => 2,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // reward points credit
            if($total_amount > 0){
                $reward = [
                    'phone_no' => $phone,
                    'date' => date('Y-m-d H:i:s'),
                    'type' => $pay_type,
                    'point' => $reward_point_credit,
                    'credit_debit' => 1,
                    'is_redeem' => 1,
                    'bill_id' => $bill_id,
                    'created_by' => $this->createdBy()
                ];
                $this->rewardService->setRequest($reward);
                $this->rewardService->add();
            }
            // payment table reward point update
            $request['amount'] = $total_amount;
            // $request['discount'] = $total_discount;
            // $request['gross_total'] = ($total_amount+$total_discount);
            $request['redeem_points'] = $request->reward_points_final == '' ? 0 : $request->reward_points_final;
            $request['closing_points'] = $this->rewardService->rewardPoints($phone);
            $payment = $this->paymentService->findById($payment->id);
            $this->paymentService->setRequest($request->except(
                'bill_id','date','remarks','recpit_no','status',
                'patient_id','remarks','payment_modes',
                'amounts','payment_list','upi_mode'
            ));
            $this->paymentService->update();
        }
        // payment bill table amount update
        $request['discount'] = $total_discount;
        $request['total_amount'] = $total_amount;
        $request['paid_amount'] = $total_amount;
        $request['due_amount'] = 0;
        $paymentBill = $this->paymentBillService->findById($paymentBill->id);
        $this->paymentBillService->setRequest($request->except(
            'bill_show_id','type','service_id','patient_id','status',
            'patient_id','phone','clinic_id','start_date','end_date',
            'registration_no','card_type','remarks','payment_modes',
            'amounts','payment_list','upi_mode'
        ));
        $this->paymentBillService->update();

        return $reward_point_credit;
    }
    public function viewBill($id,Request $request)
    {
        $appointment = $this->appointmentService->findById($id);
        $earned_points = 0;
        if ($appointment) {
            $billPayment = $appointment->paymentBill;
            // dd($billPayment);
            $bill_id = $billPayment->bill_show_id;
            // dd($billPayment->payments->sum('discount'));
            
            $earned_points = DB::table('reward_points')
                ->where([
                    'bill_id' => $bill_id,
                    'type' => 'OPD',
                    'credit_debit' => 1
                ])->value('point');
        }
        $data = [
            'appointment' => $appointment,
            'doctor_dtl' => $appointment->userDoctors,
            'clinic_dtl' => $appointment->clinics,
            'patient_dtl' => $appointment->patients,
            'patient_membership' => $this->appointmentService->activeMembership($appointment->patient_id),
            'visit_price' => $appointment->schedules->visit_price ?? 0,
            'bill_id' => $bill_id,
            'billPayment' => $billPayment,
            'reward_payment_dtl' => $billPayment->payments->first(),
            'earned_points' => $earned_points ? $earned_points : 0,
            'createdBy' => $appointment->createdBy->username,
        ];
        // dd($data['billPayment']->payments);
        $pdf = PDF::loadView('appointment::appointment.pdf.viewBill', $data);
        $pdf = $pdf->setPaper('a4', 'portrait');
        return $pdf->stream('bill.pdf');
    }
    public function createVital($id,Request $request)
    {
        try {
            $vital_id = $request->vital_id;
            $vital = [];
            $prescription = [];
            if ($request->vital_id) {
                $vital = $this->vitalsService->findById($request->vital_id);
                $prescription = $this->prescriptionService->findByOtherId('appointment_id',$id);
                if (!$vital) {
                    $this->response['success']    = false;
                    $this->response['message']  = 'Vital not found!';
                    return response()->json($this->response);
                }
            }
            // dd($prescription,empty($prescription));
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            // $this->response['data']     = $vital;
            $data = $vital;
            $list = [
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'chief_complaints' => !empty($prescription) ? $prescription->chief_complaints : '',
                'speciality' => $appointment->userDoctors->appointmentDoctors->specialitys->speciality
            ];

            // dd($appointment->doctors->specialitys->speciality);
            $this->response['form'] = view('appointment::appointment.api.vitalForm',compact('vital_id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }

    public function addVitalPost(VitalsRequest $request)
    {
        try {
            $request->validated();
            // dd($request);
            $blood_group = DB::table('patients')->where('id',$request->patient_id)->value('bloodgroup');
            $request->merge([
                'created_by' => $this->createdBy(),
                'blood_pressure' => $request->blood_pressureUp.'/'.$request->blood_pressureDwn,
                'blood_group' => $blood_group,
            ]);
            $this->vitalsService->setRequest($request->except('blood_pressureUp','blood_pressureDwn','chief_complaints'));
            $this->vitalsService->add();
            if ($request->chief_complaints) {
                $this->prescriptionService->setRequest($request->except('blood_pressure','blood_group','blood_pressureUp','blood_pressureDwn','heart_rate','temperature','spo2','height','weight','bmi','head_circumference'));
                $this->prescriptionService->add();
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Vital has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function updateVitalPost($id,VitalsRequest $request)
    {
        try {
            $request->validated();
            $Vital = $this->vitalsService->findById($id);
            if (!$Vital) {
                $this->response['success']  = false;
                $this->response['message']  = 'Vital not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'blood_pressure' => $request->blood_pressureUp.'/'.$request->blood_pressureDwn,
            ]);
            $this->vitalsService->setRequest($request->except('blood_pressureUp','blood_pressureDwn','chief_complaints'));
            $this->vitalsService->update();

            $prescription = $this->prescriptionService->findByOtherId('appointment_id',$request->appointment_id);

            $this->prescriptionService->setRequest($request->except('blood_pressure','blood_pressureUp','blood_pressureDwn','heart_rate','temperature','spo2','height','weight','bmi','head_circumference'));
            if (!$prescription) {
                if ($request->chief_complaints) {
                    // dd($prescription);
                    $this->prescriptionService->add();
                }
            }
            else {
                $this->prescriptionService->update();
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Vital has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function createAssignDoctor($id,Request $request)
    {
        try {
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $data = [];
            $list = [
                'appointment' => $appointment,
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'doctor_list' => $this->appointmentService->getSlotsAssignDoctor()
            ];
            // dd($list);
            // dd($appointment->doctors->specialitys->speciality);
            $this->response['form'] = view('appointment::appointment.api.createAssignDoctor',compact('data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addAssignDoctor(AssignDoctorRequest $request)
    {
        try {
            $request->validated();
            $appointment = $this->appointmentService->findById($request->appointment_id);
            $schedule = $this->scheduleService->findById($request->schedule_id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }

            $bill_id = $this->paymentBillService->billingIncrementId($appointment->clinic_id,'OPD',8);
            $unique_queue_number = $this->appointmentService->queueIncrementId($schedule->id);
            // dd($unique_bill_id);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'payment_status' => 'paid',
                'unique_queue_number' => $unique_queue_number,
                'doctor_id' => $schedule->doctor_id,
                'clinic_id' => $schedule->clinic_id,
                'date' => $schedule->date,
                'time_slot' => $schedule->s_time.' To '.$schedule->e_time,//11:00 AM To 01.00 PM
                'status' => 4
            ]);
            $this->appointmentService->setRequest($request->except('patient_id','appointment_id'));
            $this->appointmentService->update();

            $pay_type = config('billing.types.1');
            $bill_amount = $appointment->userDoctors->appointmentDoctors->visit_price;
            // dd($bill_amount);
            $check_payment_data = DB::table('payment_bill_master')->where(['type' => 'OPD','service_id' => $appointment->id])->select('id')->get();
            $reward_point_credit = 0;
            if (count($check_payment_data) == 0) {
                $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$bill_amount,0,$appointment->id,$appointment->patient_id,$appointment->patient_phone);
            }

            $this->response['success']  = true;
            $this->response['message']  = 'Doctor has been Assigned successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function createAssignDoctorVideo($id,Request $request)
    {
        try {
            $appointment = $this->appointmentService->findById($id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $data = [];
            $list = [
                'appointment' => $appointment,
                'appointment_id' => $appointment->id,
                'patient_id' => $appointment->patient_id,
                'doctor_list' => $this->appointmentService->getSlotsAssignDoctor()
            ];
            // dd($list);
            // dd($appointment->doctors->specialitys->speciality);
            $this->response['form'] = view('appointment::appointment.api.createAssignDoctorVideo',compact('data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addAssignDoctorVideo(AssignDoctorVideoRequest $request)
    {
        try {
            $request->validated();
            $appointment = $this->appointmentService->findById($request->appointment_id);
            $schedule = $this->scheduleService->findById($request->schedule_id);
            if (!$appointment) {
                $this->response['success']    = false;
                $this->response['message']  = 'Appointment not found!';
                return response()->json($this->response);
            }
            $start_time = date('Y-m-d H:i:s');
            $meeting_arr = [
                "agenda" => 'Doctor Consultation',
                "topic" => 'Consultation: '.$appointment->patients->name.' with Dr. '.$schedule->users->username.' on '.date('M j, Y', strtotime($start_time)),
                "type" => 2, // 1 => instant, 2 => scheduled, 3 => recurring with no fixed time, 8 => recurring with fixed time
                "duration" => 60, // in minutes
                "timezone" => 'Asia/Kolkata', // set your timezone
                "password" => '123456',
                "start_time" => $start_time, // set your start time
                // "template_id" => 'set your template id', // set your template id  Ex: "Dv4YdINdTk+Z5RToadh5ug==" from https://marketplace.zoom.us/docs/api-reference/zoom-api/meetings/meetingtemplates
                "pre_schedule" => false,  // set true if you want to create a pre-scheduled meeting
                // "schedule_for" => 'set your schedule for profile email ', // set your schedule for
                "settings" => [
                    'join_before_host' => false, // if you want to join before host set true otherwise set false
                    'host_video' => false, // if you want to start video when host join set true otherwise set false
                    'participant_video' => false, // if you want to start video when participants join set true otherwise set false
                    'mute_upon_entry' => false, // if you want to mute participants when they join the meeting set true otherwise set false
                    'waiting_room' => false, // if you want to use waiting room for participants set true otherwise set false
                    'audio' => 'both', // values are 'both', 'telephony', 'voip'. default is both.
                    'auto_recording' => 'none', // values are 'none', 'local', 'cloud'. default is none.
                    'approval_type' => 0, // 0 => Automatically Approve, 1 => Manually Approve, 2 => No Registration Required
                ],

            ];
            $meetings = $this->zoomService->createMeeting($meeting_arr);
            // dd($meetings);
            if (!isset($meetings['data']['id'])) {
                $this->response['success']    = false;
                $this->response['message']  = 'Something went wrong in zoom!';
                return response()->json($this->response);
            }
            $meeting_dtl = AppointmentVideoConsult::create([
                'id' => isset($meetings['data']['id']) ? $meetings['data']['id'] : null,
                'uuid' => isset($meetings['data']['uuid']) ? $meetings['data']['uuid'] : null,
                'host_id' => isset($meetings['data']['host_id']) ? $meetings['data']['host_id'] : null,
                'host_email' => isset($meetings['data']['host_email']) ? $meetings['data']['host_email'] : null,
                'agenda' => isset($meetings['data']['agenda']) ? $meetings['data']['agenda'] : null,
                'topic' => isset($meetings['data']['topic']) ? $meetings['data']['topic'] : null,
                'type' => isset($meetings['data']['type']) ? $meetings['data']['type'] : null,
                'start_time' => isset($meetings['data']['start_time']) ? $meetings['data']['start_time'] : null,
                'duration' => isset($meetings['data']['duration']) ? $meetings['data']['duration'] : null,
                'start_url' => isset($meetings['data']['start_url']) ? $meetings['data']['start_url'] : null,
                'join_url' => isset($meetings['data']['join_url']) ? $meetings['data']['join_url'] : null,
                'password' => isset($meetings['data']['encrypted_password']) ? $meetings['data']['encrypted_password'] : null,
                'created_by' => $this->createdBy()
            ]);
            // $all_meeting = $this->zoomService->getAllMeeting();
            // dd($meetings);
            $bill_id = $this->paymentBillService->billingIncrementId($appointment->clinic_id,'OPD',8);
            $unique_queue_number = $this->appointmentService->queueIncrementId($schedule->id);
            // dd($unique_bill_id);
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'payment_status' => 'paid',
                'unique_queue_number' => $unique_queue_number,
                'doctor_id' => $schedule->doctor_id,
                'clinic_id' => $schedule->clinic_id,
                'date' => $schedule->date,
                'time_slot' => $schedule->s_time.' To '.$schedule->e_time,//11:00 AM To 01.00 PM
                'status' => 4,
                'meeting_id' => $meeting_dtl->id
            ]);
            $this->appointmentService->setRequest($request->except('patient_id','appointment_id'));
            $this->appointmentService->update();

            $pay_type = config('billing.types.1');
            $bill_amount = $appointment->userDoctors->appointmentDoctors->visit_price;
            // dd($bill_amount);
            $check_payment_data = DB::table('payment_bill_master')->where(['type' => 'OPD','service_id' => $appointment->id])->select('id')->get();
            $reward_point_credit = 0;
            if (count($check_payment_data) == 0) {
                $reward_point_credit = $this->paymentWithReward($request,$bill_id,$pay_type,$bill_amount,0,$appointment->id,$appointment->patient_id,$appointment->patient_phone);
            }

            $this->response['success']  = true;
            $this->response['message']  = 'Doctor has been Assigned successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listCaptureVitals(Request $request)
    {
        try {
            $filter = $request['filter'];
            $role = $this->getUserRole();
            switch ($role->id) {
                case 6:
                    $created_by = auth()->user()->id;
                    break;
                default:
                    $created_by = null;
                    break;
            }
            if($created_by){
                $filter['created_by'] =  [
                    'type' => 'eq',
                    'value' => $created_by
                ];
            }
            $filter['status'] =  [
                'type' => 'eq',
                'value' => 2
            ];
            $request->merge([
                'filter' => $filter
            ]);
            // $request['join'] = [
            //     'patients' => [
            //         'reletion' => [
            //             'prefix' => 'patient_id',
            //             'suffix' => 'id'
            //         ]
            //     ]
            // ];
            $request['with'] = [
                'clinics' => 'id,clinic_name',
                'patients.parent' => 'id,name,phone',
            ];
            // array_push($this->vitalsService->columns,'patients.name as patient_name','patients.phone as patient_phone');
            $this->vitalsService->setRequest($request);
            $this->vitalsService->findAll();
            $this->response['success']  = true;
            $data = $this->vitalsService->getRows();
            // dd($data['rows'][0]['patients']);
            $permissionPage = $this->getPermissionList();
            // dd($status_list);
            $this->response['tbody'] = view('appointment::appointment.api.listCaptureVitals',compact('data','permissionPage'))->render();
            $this->response['tfoot'] = $this->vitalsService->paginationCustom();
            $this->response['headerAction'] = view('appointment::appointment.api.headerActionCaptureVitals',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function listFamilyCaptureVitals(Request $request)
    {
        $this->response['success']  = true;
        $phone = $request->phone;
        $this->response['data'] = [];
        $list = [
            'patients' => $this->appointmentService->getFamilys($phone)
        ];
        // dd($list);
        $parent_id = $this->appointmentService->getPatientID($phone);
        if (!isset($parent_id)) {
            $parent_id = 0;
        }
        // dd($phone,$parent_id,$list);
        $html = '<tr>';
            $html .= '<td colspan="5" class="border-0">';
            if (count($list['patients']) > 0) {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(0,'.$parent_id.','.$request->phone.')">Add Member</button>';
            }
            else {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(1,'.$parent_id.','.$request->phone.')">Add Patient</button>';
            }
            $html .= '</td>';
        $html .= '</tr>';
        // dd($list['patients'],$list['patients'][0]->membershipRegistrations[0]->memberships);
        $this->response['tbody'] = view('appointment::appointment.api.listFamilyCaptureVitals',compact('list','parent_id','phone'))->render();
        $this->response['tfoot'] = $html;
        return response()->json($this->response);
    }
    public function createCaptureVital(Request $request)
    {
        try {
            $role = $this->getUserRole();
            switch ($role->id) {
                case 6:
                    $user_clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                default:
                    $user_clinic_id = null;
                    break;
            }
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $this->response['success']  = true;
            $patient = $this->appointmentService->findPatient($patient_id);
            $service = $patient->membershipRegistrations[0]->memberships->name ?? '';
            $data = [
                'patient_detail' => $patient,
                'phone' => $phone
            ];
            $list = [
                'clinic_list' => $this->appointmentService->allClinics()->toArray(),
                'user_clinic_id' => $user_clinic_id,
            ];
            $permissionPage = $this->getPermissionList();
            $this->response['form'] = view('appointment::appointment.api.createCaptureVital',compact('patient_id','phone','data','list','service','permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function addCaptureVital(CaptureVitalsRequest $request)
    {
        try {
            $blood_pressure = $request->blood_pressureUp.'/'.$request->blood_pressureDwn;
            $request->merge([
                'created_by' => $this->createdBy(),
                'blood_pressure' => $blood_pressure,
                'status' => 2
            ]);
            $this->vitalsService->setRequest($request->except('blood_pressureUp','blood_pressureDwn'));
            $this->vitalsService->add();
            // sms send
            $this->smsMsgService->setFlowId('686b722ad6fc05392a0a2132');
            $this->smsMsgService->sendSmsToRecipients($request->patient_phone);
            $variable = [
                'date' => date('d-m-Y'),
                'var2' => ($request->weight ? 'Weight:' . $request->weight . 'kg , ' : ''),
                'var3' => ($blood_pressure ? 'BP:' . $blood_pressure . 'mmHg ,' : ''),
                'var4' => ($request->heart_rate ? 'Heart Rate:' . $request->heart_rate . 'bpm , ' : ''),
                'var5' => ($request->spo2 ? 'SpO2:' . $request->spo2 . '%' : ''),
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'OPD',
                'campaign_name' => 'Vital captured',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['message']  = 'Vital has been created successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
}
