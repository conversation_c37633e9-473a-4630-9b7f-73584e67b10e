<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\LeadGeneration;
use DB;
use Carbon\Carbon;

class LeadGenerationReportService extends ApplicationDefaultService
{
    public $entity;
    
    public $columns = [
        'lead_generations.id',
        'lead_generations.patient_id',
        'lead_generations.datasource',
        'lead_generations.source',
        'lead_generations.status',
        'lead_generations.created_by',
        'lead_generations.modified_by',
        'lead_generations.deleted_by',
        'lead_generations.created_at',
        'lead_generations.updated_at',
        'lead_generations.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'datasource',
        'source',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(LeadGeneration $entity){
        $this->entity =$entity;
    }

    
}
