
<?php $__env->startSection('title'); ?>
    <?php echo e(config('diagnostic.title', 'order')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
    <style>
        /* coupon styling */
        .copon-wrap .btn-check+.btn {
            border-style: dashed !important;
            overflow: hidden;
        }

        .copon-wrap .btn-check:checked+.btn {
            background-color: #f9f9f9 !important;
            border-color: #f00 !important;
            border-style: solid !important;
        }

        .copon-wrap .btn-check:checked+.btn h3 {
            color: var(--bs-primary) !important;
        }

        .copon-wrap .btn-check:checked+.btn .offer {
            background-color: var(--bs-primary) !important;
        }


        @media only screen and (max-width: 767px) {
            .labl-hgt {
                min-height: 43px;
                display: flex;
                align-items: center;
            }
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <?php if($id): ?>
            <div class="card">
                <div class="card-header border-0 d-flex flex-wrap justify-content-between">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Patient Edit</h5>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="<?php echo e(route('diagnostic.addForm', ['id' => $id])); ?>" title="Refresh">
                            <button type="button" class="btn btn-sm bg-primary text-white ">Refresh
                                <svg id="fi_8391685" height="14" style="margin-left: 4px;" viewBox="0 0 32 32"
                                    width="14" fill="#fff" xmlns="http://www.w3.org/2000/svg" data-name="Layer 1">
                                    <path
                                        d="m16 32a16 16 0 1 1 11.733-26.878 1 1 0 1 1 -1.466 1.359 14 14 0 1 0 3.733 9.519 1 1 0 0 1 2 0 16.019 16.019 0 0 1 -16 16z">
                                    </path>
                                    <path d="m27 7a1 1 0 0 1 -1-1v-5a1 1 0 0 1 2 0v5a1 1 0 0 1 -1 1z"></path>
                                    <path d="m27 7h-5a1 1 0 0 1 0-2h5a1 1 0 0 1 0 2z"></path>
                                </svg>
                            </button>
                        </a>
                        <a href="<?php echo e(route('diagnostic.index')); ?>" title="Back">
                            <button type="button" class="btn btn-link p-0 fw-bold" data-bs-toggle="modal"
                                data-bs-target="#exampleModalCenteredScrollable">
                                <svg fill="none" style="margin-top: -4px;" xmlns="http://www.w3.org/2000/svg"
                                    width="20" height="20" viewBox="0 0 24 24">
                                    <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Back
                            </button>
                        </a>
                    </div>
                </div>
                <div class="card-body placeholder-glow" id="data-edit-patient">
                    <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4, 4, 4, 4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        <?php else: ?>
            <div class="card  mb-3">
                <div
                    class="card-header border-bottom py-2 px-3 px-md-4 align-items-center d-flex justify-content-between rounded-3">
                    <div class="header-title">
                        <h5 class="h5 mb-0">
                            <?php echo e(request('s_id') ? 'Add Diagnostic' : 'Search Patient'); ?>

                        </h5>
                    </div>
                    <div>
                        <a href="<?php echo e(route('diagnostic.index')); ?>">
                            <button type="button" class="btn btn-link p-0 fw-bold" data-bs-toggle="modal"
                                data-bs-target="#exampleModalCenteredScrollable">
                                <svg fill="none" style="margin-top: -4px;" xmlns="http://www.w3.org/2000/svg"
                                    width="20" height="20" viewBox="0 0 24 24">
                                    <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5"
                                        stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                                Back
                            </button>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <div class="">
            <?php if(!$id && !request('s_id')): ?>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="">
                                    <!-- <div
                                        class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                                        <div class="header-title">
                                            <h5 class="h5 mb-0">Search Patient</h5>
                                        </div>
                                    </div> -->

                                    <div class="row justify-content-center">
                                        <div class="form-group col-lg-6 col-md-6 col-12">
                                            <div class="row row-cols-auto align-items-center justify-content-center">
                                                <label for="exampleInputEmail1"
                                                    class="fw-bold col-sm-12 form-label p-0 text-center">Search with
                                                    Patient
                                                    Name or Phone No</label>
                                                <div class="col-12 col-sm-12">
                                                    <input type="text" class="form-control" name="search_patient"
                                                        id="search_patient" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-1 justify-content-center mb-5">
                                            <a href="<?php echo e(route('diagnostic.index')); ?>">
                                                <button type="button" class="btn btn-gray"
                                                    data-bs-dismiss="modal">Close</button>
                                            </a>

                                            <button onclick="searchPatient()" type="button" name="button"
                                                class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Search</button>
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-12">
                                            <div class="Table-custom-padding1 table-responsive">
                                                <table id="data-list-family"
                                                    class="table table-sm datatable_desc placeholder-glow"
                                                    data-toggle="data-table" style="display: none;">
                                                    <thead>
                                                        <tr>
                                                            <th>
                                                                Name
                                                            </th>
                                                            <th>
                                                                Gender
                                                            </th>
                                                            <th>
                                                                Membership
                                                            </th>
                                                            <th>
                                                                Action
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php echo $__env->make('admin.custom.loading', [
                                                            'td' => 4,
                                                            'action' => 1,
                                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <td colspan="5" class="border-0">
                                                                <button
                                                                    class="btn btn-primary d-flex gap-1 align-items-center justify-content-center placeholder"
                                                                    type="button"></button>
                                                            </td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="card-body placeholder-glow data-add" id="data-add-patient"
                                    style="display: none;">
                                    <?php echo $__env->make('admin.custom.loadingForm', [
                                        'fields' => [4, 4, 4, 4, 4, 4, 4],
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </div>
                                <div class="card-body placeholder-glow data-add" id="data-add-member"
                                    style="display: none;">
                                    <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body placeholder-glow p-0" id="data-add-edit" style="display: none">

                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body placeholder-glow p-0" id="data-add-edit">
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="package-details-modal" tabindex="-1"
        aria-labelledby="exampleModalCenteredScrollableTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Package Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="package-details">

                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="uncommon-test-modal" tabindex="-1"
        aria-labelledby="exampleModalCenteredScrollableTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Uncommon Test List</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="uncommon-test">

                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="addUncommonTest()">Add Test</button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        <?php if(!$id && !request('s_id')): ?>
            $(document).ready(function() {
                refreshToken();
            });
        <?php endif; ?>
        let redirectUrlDiagnostic = "<?php echo e(route('diagnostic.index')); ?>";
        $(document).ready(function() {
            $(document).on("submit", "#data-add-member #submitForm", function() { // for add member
                event.preventDefault(); // Prevent normal form submission
                var form = $(this); // The form that was submitted
                setRedirectUrl(redirectUrlDiagnostic);
                setFormEntity(form);
                addUpdateForm();
            });
            $(document).on("submit", "#data-add-edit #submitForm", function() { // for edit patient
                event.preventDefault(); // Prevent normal form submission
                var form = $(this); // The form that was submitted
                setRedirectUrl(redirectUrlDiagnostic);
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function typeCollectionChange(params) {
            $('.CV_div').hide();
            $('.HC_div').hide();
            $('.home-collection-charges').hide();

            if (params == 'CV') {
                $('.CV_div').show();
            } else if (params == 'HC') {
                $('.CV_div').show();
                $('.HC_div').show();
            }
        }

        function doctorTypeChange(params) {
            $('.mymdDoctorDiv').hide();
            $('.otherDoctorDiv').hide();
            if (params == 1) {
                $('.mymdDoctorDiv').show();
            } else if (params == 2) {
                $('.otherDoctorDiv').show();
            }
        }

        function getTestList(params) {
            let datajson = {
                "test_id": params
            };
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('diagnostic.url') . 'testItemList'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                // beforeSend: function() {
                //     // Show the loading GIF
                //     $('#data-list-family').show();
                // },
                success: function(data) {
                    // console.log(data.pkg_test_list, 777777777777);
                    let check = checkPackageToTest(data.pkg_test_list);
                    let checkPackage = 0;
                    if (check == 0) {
                        checkPackage = checkPackageToPackage(data.pkg_test_list, data.data.package_test_list);
                    }
                    if (check == 0 && checkPackage == 0) {
                        $('#test_ids').val(null).trigger('change');
                        $('.testItemDiv').append(data.form);
                        $('#test_ids option[value="' + params + '"]').prop('disabled', true);
                        if (data.pkg_test_list.length > 0) {
                            $.each(data.data.package_test_list, function(index, value) {
                                $('#test_ids option[value="' + index + '"]').prop('disabled', true);
                            });
                        }
                        calculateCharges();
                    }
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }

        function addUncommonTest() {
            $('#uncommon-test-modal').modal('hide');
            let checkedTests = $(".uncommon_test_id:checked").map(function() {
                return $(this).val();
            }).get();
            $.each(checkedTests, function(key, val) {
                getTestList(val);
            });
            // console.log(checkedTests);
        }

        function checkPackageToTest(pkg_test_list) {
            $(".test_name_css").css('background-color', '');
            // console.log(pkg_test_list, $(".item_id"));
            if (pkg_test_list.length == 0 || $(".item_id").length == 0) {
                return 0;
            }
            let check = 0;
            $(".item_id").each(function() {
                let test_id = $(this).val();
                let itdose_testid = $("#itdose_testid" + test_id).val();
                // Check if the value exists in the array
                if (pkg_test_list.includes(itdose_testid)) {
                    $("#test_name" + test_id).css('background-color', '#f89ca0');
                    check++;
                } else {
                    $("#test_name" + test_id).css('background-color', 'none');
                }
            });
            if (check > 0) {
                alert(
                    'The package you selected includes a test that has already been selected. Please deselect the test first.'
                );
            }
            // console.log(check,itdose_testid,9999999);

            return check;
        }

        function checkPackageToPackage(pkg_test_list, associate_pkg_test_list) {
            if (pkg_test_list.length == 0 || $(".item_id").length == 0) {
                return 0;
            }
            let check = 0;
            let uncommonElements = associate_pkg_test_list;
            $(".item_id").each(function() {
                let test_id = $(this).val();
                let test_list_pkg = $("#test_list_" + test_id).val();
                if (test_list_pkg) {
                    test_list_pkg = JSON.parse(test_list_pkg);
                    uncommonElements = findUncommonElements(uncommonElements, test_list_pkg);
                }
            });
            // console.log(Object.keys(uncommonElements).length, Object.keys(associate_pkg_test_list).length)
            if (Object.keys(uncommonElements).length < Object.keys(associate_pkg_test_list).length) {
                let html_list = '<ul>';
                $.each(uncommonElements, function(key, val) {
                    html_list += '<li><input type="checkbox" class="uncommon_test_id" id="uncommon_test_id_' + key +
                        '" value="' + key + '" checked /> <lable for="uncommon_test_id_' + key + '">' + val +
                        '</lable></li>';
                });
                $('#uncommon-test').html(html_list);
                $('#uncommon-test-modal').modal('show');
                check++;
            }
            return check;
        }
        // Function to find uncommon elements
        function findUncommonElements(obj1, obj2) {
            const uncommon = {};

            // Check for keys in obj1 not in obj2
            $.each(obj1, function(key, value) {
                if (!obj2.hasOwnProperty(key)) {
                    uncommon[key] = value;
                }
            });

            // Check for keys in obj2 not in obj1
            // $.each(obj2, function(key, value) {
            //     if (!obj1.hasOwnProperty(key)) {
            //         uncommon[key] = value;
            //     }
            // });

            return uncommon;
        }

        function removeTest(id) {
            var pkg_test_list = JSON.parse($('#test_list_' + id).val());
            $('#test_row-' + id).remove();
            // item deselect
            $('#test_ids option[value="' + id + '"]').prop('disabled', false);
            // for package test deselect
            $.each(pkg_test_list, function(index, value) {
                if (index != '') {
                    $('#test_ids option[value="' + index + '"]').prop('disabled', false);
                }
            });
            calculateCharges();
        }

        function packageTestList(id) {
            let list = $('#test_list_' + id).val();
            let html_list = '';
            $.each(JSON.parse(list), function(key, val) {
                html_list += val + ', ';
                // console.log(key + val);
            });
            // console.log(html_list);
            $('#package-details').html(html_list);
            $('#package-details-modal').modal('show');
        }

        function homeCollectionRangeChange(params) {
            if ($(".item_id").length == 0) {
                swal("", "Please select atleast one test.", "error");
                // $(params).prop('checked', false);
                $(params).val('');
                return false;
            }
            let id = params.value;
            let charge = $('#home_collection_range option[value="' + id + '"]').attr('data-charge');
            console.log(charge);
            $('#home_collection_charge').val(charge);
            // $('.home-collection-charges').show();
            calculateCharges();
        }
        $(document).on("change", "#clinic_id", function(event) {
            calculateCharges();
        });
        let offer_arr = '';
        let offer_not_avil = JSON.parse("<?php echo json_encode($list['offer_not_avil']); ?>");
        let offerIndex = '';

        function offerDiscount(params) {
            // console.log(params);
            if ($(".item_id").length == 0) {
                swal("", "Please select atleast one test.", "error");
                $('.buttonactive').prop('checked', false);
                return false;
            }
            if (JSON.parse(params).offered_id == 19) { // Minimum amount should be 250 or more for avail SSS campaign.
                if (Number($("#sub_total").val()) < 250) {
                    swal("", "Minimum amount should be 250 or more for avail SSS campaign.", "error");
                    $('.buttonactive').prop('checked', false);
                    return false;
                }
            }
            if ($('input[name="offered_id"]:checked').attr('id') == offerIndex) {
                offer_arr = '';
                offerIndex = '';
                $('.buttonactive').prop('checked', false);
            } else {
                offerIndex = $('input[name="offered_id"]:checked').attr('id');
                offer_arr = JSON.parse(params);
            }
            // console.log(offer_arr);
            calculateCharges();
        }
        let edit_id = '<?php echo e($id ?? ''); ?>';

        function calculateCharges() {
            let sub_total = 0;
            let discount_offer = 0;
            let reward_points = Number($("#reward_points_final").val());
            let payable_amount = 0;
            // console.log(reward_points);
            // item price
            let cam_item_id = [];
            $(".item_id").each(function() {
                cam_item_id.push($(this).val());
            });
            $(".item_id").each(function() {
                // console.log($(this).val());
                let test_id = $(this).val();
                let test_price = Number($("#test_price" + test_id).val());
                let is_package = Number($("#is_package" + test_id).val());
                let discount = 0;
                if (offer_arr != '') {
                    // membership and employee discount
                    if (!offer_not_avil.includes(Number(test_id)) || offer_arr.test_list.includes(
                        test_id)) { // depertment wise offer not available || same department but avil membership then avil
                        if (offer_arr.test_list.length > 0 && offer_arr.test_list.includes(test_id)) {
                            // console.log(offer_arr.test_list.length, offer_arr.test_list.includes(test_id));
                            discount = Number(test_price * 100 / 100);
                            $("#offered_type").val(offer_arr.offered_type);
                        } else {
                            let clinic_id = $("#clinic_id").val();
                            console.log(clinic_id, offer_arr.clinic_list.length, offer_arr.clinic_list.includes(
                                Number(clinic_id)));
                            if ((offer_arr.clinic_list.length == 0 && offer_arr.offered_id != 1) || (clinic_id &&
                                    offer_arr.clinic_list.length > 0 && offer_arr.clinic_list.includes(Number(
                                        clinic_id)))) {
                                if (offer_arr.rate_type == 0) {
                                    discount = Number(test_price * offer_arr.rate / 100);
                                } else {
                                    discount = Number(offer_arr.rate);
                                }
                                $("#offered_type").val(offer_arr.offered_type);
                            }
                        }
                    }
                    // campaign discount
                    if (offer_arr.test_list.length > 0 && !offer_arr.test_list.includes(test_id)) {
                        // console.log(offer_arr, test_id,1111,$("#sub_total").val());
                        $.each(offer_arr.test_list, function(key, val) {
                            if (is_package == val.test_type) {
                                if (val.discount_for == 1) { // all memdatory tests
                                    // console.log('c 0',val.test_ids, cam_item_id);
                                    if (val.test_ids.every(item => cam_item_id.includes(item)) && val
                                        .test_ids.includes(test_id)) {
                                        // console.log('c 1');
                                        discount = val.rate_type == 1 ? Number(test_price * val.rate /
                                            100) : discount = Number(val.rate);
                                    }
                                } else {
                                    // console.log('c 1',val,is_package);
                                    if (val?.test_ids?.[0] !== undefined && val.test_ids[0] !== null && val
                                        .test_ids[0] == 'all') {
                                        // console.log('c 2');//test_type
                                        discount = val.rate_type == 1 ? Number(test_price * val.rate /
                                            100) : discount = Number(val.rate);
                                    } else if (val.test_ids.includes(test_id)) {
                                        // console.log('c 3');
                                        discount = val.rate_type == 1 ? Number(test_price * val.rate /
                                            100) : discount = Number(val.rate);
                                    }
                                }
                            }
                            $("#offered_type").val(offer_arr.offered_type);
                        });
                        // console.log(offer_arr.test_list,offer_arr.test_list.length);
                    }
                }
                $("#discount" + test_id).val(discount);
                let price = test_price - discount;
                Number($("#price" + test_id).val(price));
                sub_total += test_price;
                discount_offer += discount;
            });
            // console.log($("#home_collection_range").val());
            // home collection charges
            if ($("#type_of_collection").val() == 'HC' && $("#home_collection_range").val()) {
                let quantity = $('#home_collection_quantity').val();
                let id = $("#home_collection_range").val();
                let charge = quantity * Number($('#home_collection_range option[value="' + id + '"]').attr('data-charge'));
                if (offer_arr != '' && offer_arr.hc_list != '') {
                    // console.log(offer_arr.hc_list,'wrgfuygriuhiuh');
                    if (Number.isInteger(offer_arr.hc_list.discount)) {
                        charge -= offer_arr.hc_list.discount;
                    } else {
                        if (offer_arr.hc_list.discount == 'free') {
                            charge = 0;
                        }
                    }
                }
                if (charge < 0) {
                    charge = 0;
                }
                $('#home_collection_charge').val(charge);
                sub_total += charge;
            }
            payable_amount = sub_total - (discount_offer + reward_points);
            $("#sub_total").val(sub_total);
            $("#discount_offer").val(discount_offer);
            $("#payable_amount").val(payable_amount);
            $('#total_due').val(payable_amount);
            // discount_offer_div
            if (discount_offer > 0) {
                $("#discount_offer_div").show();
            } else {
                $("#discount_offer_div").hide();
            }
            if (payable_amount > 0) {
                $('#reddem_patientPhone').prop('disabled', false);
            }
            // console.log('sgvwuvhuw',payable_amount);
            if (payable_amount == 0 || edit_id != '') {
                $('#submitBtn').prop('disabled', false);
            } else {
                $('#submitBtn').prop('disabled', true);
            }
        }

        // reward point
        let reward_phone_no = '';
        let opening_point = 0;
        let redeem_points = 0;

        function checkRedeemPoints(params, stat) {
            opening_point = Number($('#opening_rdPoints').val());
            redeem_points = Number($(params).val());
            let payable_amount = Number($('#payable_amount').val());
            if (redeem_points > opening_point || redeem_points > payable_amount) {
                alert('Please enter valid points');
                $(params).val('');
                redeem_points = 0;
                if (stat == 1) {
                    $('#rdPoints_otherNo').text(opening_point.toFixed(2));
                } else {
                    $('#opening_rdPoints_text').text(opening_point.toFixed(2));
                }
                return false;
            }
            if (stat == 1) {
                $('#rdPoints_otherNo').text((opening_point - redeem_points).toFixed(2));
            } else {
                $('#opening_rdPoints_text').text((opening_point - redeem_points).toFixed(2));
            }
            return true;
            // console.log(params);
        }

        function resendOTP() {
            $("#reddem_patientPhone").click();
        }

        function resendOTPOther() {
            $("#reddem_otherPhone").click();
        }

        function sendOtp(params, stat) {
            $('#otp').prop('disabled', false);
            $('#otp_otherNo').prop('disabled', false);
            $('.resendOTP').prop('disabled', true);
            $('.resendOTP_other').prop('disabled', true);
            reward_phone_no = $('#reward_phone_no').val();
            const validationErrors = validateRewardPoint(redeem_points);
            if (validationErrors) {
                alert(validationErrors.message);
                return false;
            }
            // redeem_points = Number($('#redeem_points').val());
            // console.log(reward_phone_no, redeem_points);
            if (redeem_points == '') {
                alert('Please enter points');
                return false;
            }
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'sendOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "reward_phone_no": reward_phone_no,
                    "type": "DG",
                    "redeem_points": redeem_points,
                    "opening_point": opening_point
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // console.log(data.otp_expire);
                    // Store data
                    localStorage.setItem('device_check', data.otp_expire);
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    let durationInSeconds = data.otp_expire * 60;
                    if (stat == 1) {
                        $('#rewardRedemptionPoint_other').text(redeem_points.toFixed(2));
                        $('.rewardRedemption_other').show();
                        $('#redeempoints_div_other').hide();
                        $('#otpdiv_other').show();
                        startOTPTimer(durationInSeconds, 'timerOtpExpire_other', 'resendOTP_other',
                            'otp_otherNo');
                    } else {
                        $('#rewardRedemptionPoint').text(redeem_points.toFixed(2));
                        $('.rewardRedemption').show();
                        $('#redeempoints_div').hide();
                        $('#otpdiv').show();
                        startOTPTimer(durationInSeconds, 'timerOtpExpire', 'resendOTP',
                            'otp');
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }

        function checkOTP(params, stat) {
            // Retrieve data
            let device_check = localStorage.getItem('device_check');

            console.log(device_check);
            if (device_check === null) {
                alert('OTP has been sent to this device. Please verify the OTP on the same device.');
                return false;
            }
            // console.log(localStorage.getItem('device_check'));
            let otp_user = 0;
            if (stat == 1) {
                otp_user = $('#otp_otherNo').val();
            } else {
                otp_user = $('#otp').val();
            }
            // console.log(otp_user,$('#otp').val());
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'verifyOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "reward_phone_no": reward_phone_no,
                    "type": "DG",
                    "redeem_points": redeem_points,
                    "opening_point": opening_point,
                    "otp": otp_user
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    if (data.verify == true) {
                        $('#reward_points_final').val(redeem_points);
                        let payable_amount = Number($('#payable_amount').val()) - redeem_points;
                        redeemPoints(payable_amount);
                        $('#actaulRdPointsDiv').show();
                        if (stat == 1) {
                            $('#otpdiv_other').hide();
                            $('#error_msg_other').hide();
                        } else {
                            $('#otpdiv').hide();
                            $('#error_msg').hide();
                        }
                        // Remove data
                        localStorage.removeItem('device_check');
                    } else {
                        if (stat == 1) {
                            $('#error_msg_other').show();
                        } else {
                            $('#error_msg').show();
                        }
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }

        function redeemPoints(payable_amount) {
            $('#payable_amount').val(payable_amount);
            $('#total_due').val(payable_amount);
            paidCalculation();
        }

        function cancelownphno() {
            console.log(redeem_points);
            let payable_amount = Number($('#payable_amount').val()) + Number($('#reward_points_final').val());
            redeemPoints(payable_amount);
            redeem_points = 0;
            console.log(opening_point);
            $('#opening_rdPoints_text').text(opening_point.toFixed(2));
            $('#redeem_points').val('');
            $('.rewardRedemption').hide();
            $('#redeempoints_div').show();
            $('#otp').val('');
            $('#otpdiv').hide();
            $('#reward_points_final').val(0);
            $('#actaulRdPointsDiv').hide();
        }
        // checkOtherPhno
        function checkOtherPhno(patient_phone) {
            // console.log($('.card_type_check').length);
            if ($('.card_type_check').length == 0) {
                console.log('no card');
            }
            opening_point = 0;
            $('#opening_rdPoints').val(0);
            cancelownphno();
            $('#rdPoints_otherNo').text('0.00');
            $('#otherno_for_rdpoints').text('');
            $('#reward_redemption_other').text('');
            $('#other_phone_no').val('');
            $('#redeem_points_other_no').val('');
            $('#otp_otherNo').val('');
            $('#redeempoints_otherNo_div').show();
            $('#othPhnoDiv').show();
            $('#ownPhnoDiv').hide();
            $('.rewardOtherShow').hide();
            $('#reward_phone_no').val(patient_phone);
        }

        function checkOwnPhno(patient_phone, opening) {
            opening_point = Number(opening);
            $('#opening_rdPoints').val(opening_point);
            cancelothphno();
            cancelownphno();
            $('#ownPhnoDiv').show();
            $('#othPhnoDiv').hide();
            $('#reward_phone_no').val(patient_phone);
        }

        function checkRewardPointsOther(params) {
            let other_phone_no = $('#other_phone_no').val();
            // console.log(other_phone_no, jwtToken);
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('reward.reward_url') . 'getRewardPoints'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "other_phone_no": other_phone_no
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    console.log(data);

                    $(params).prop('disabled', false);
                    $(params).html(btn_text);

                    $('#otherno_for_rdpoints').text(data.phone_no);
                    $('#reward_phone_no').val(data.phone_no);
                    $('#rdPoints_otherNo').text(Number(data.points).toFixed(2));
                    $('#otherphno').show();
                    $('#redeempoints_otherNo_div').hide();
                    $('#redeempoints_div_other').show();
                    $('#opening_rdPoints').val(Number(data.points));
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }

        function cancelothphno() {
            console.log(redeem_points);
            let payable_amount = Number($('#payable_amount').val()) + redeem_points;
            redeemPoints(payable_amount);
            redeem_points = 0;
            // not done
            $('#rdPoints_otherNo').text(opening_point.toFixed(2));
            $('#redeem_points_other_no').val('');
            $('.rewardRedemption_other').hide();
            $('#redeempoints_div_other').show();
            $('#otp_otherNo').val('');
            $('#otpdiv_other').hide();
            $('#reward_points_final').val(0);
            $('#actaulRdPointsDiv').hide();
        }
        // end reward point
        function searchPatient() {
            let phone = $('#search_patient').val();
            if (phone == '') {
                alert('Please enter phone number');
                return false;
            }
            const validationErrors = validatePhoneNumber(phone);
            if (Object.keys(validationErrors).length > 0) {
                alert(validationErrors.phone);
                return false;
            }
            $('#data-add-edit').html('');
            $('.data-add').hide();
            let datajson = {
                "phone": phone
            };
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('diagnostic.url') . 'listFamily'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#data-list-family').show();
                },
                success: function(data) {
                    $('#data-list-family tbody').html(data.tbody);
                    $('#data-list-family tfoot').html(data.tfoot);
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }

        function showForm(stat, parent_id, phone) {
            $('.data-add').hide();
            let redirectUrl = "<?php echo e(route('diagnostic.addForm')); ?>";
            setRedirectUrl(redirectUrl);
            if (stat == 0) {
                let ht_id = '#data-add-member';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'addFamily/'); ?>" + parent_id;
                setListUrl(url); // api url for show table
            } else {
                let ht_id = '#data-add-patient';
                setId(ht_id); // set show table id
                let url = "<?php echo e(config('patient.url') . 'create'); ?>" + "?parent_id=" + parent_id + "&phone=" + phone;
                setListUrl(url); // api url for show table
            }
            $(ht_id).show();
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }

        async function selectPatient(patient_id, phone, service) {
            try {
                // console.log(patient_id, phone, service);
                // return false;
                offer_arr = '';
                $('.data-add').hide();
                setRedirectUrl(redirectUrlDiagnostic);
                let ht_id = '#data-add-edit';
                $(ht_id).show();
                setId(ht_id); // set show table id
                let url = "<?php echo e($id ? config('diagnostic.url') . 'edit/' . $id : config('diagnostic.url') . 'create'); ?>";
                setListUrl(url); // api url for show table
                let method = 'POST';
                setMethod(method);
                let filter = {
                    "patient_id": patient_id,
                    "phone": phone,
                    "service": service,
                    "s_id": "<?php echo e(request('s_id') ?? null); ?>",
                    "type": "<?php echo e(request('type') ?? null); ?>"
                };
                setFilter(filter); // set filter [where, pagination, sort]
                const customWait = await getForm(); // get list data with pagination and filter
                return customWait; // Return the result of the promise
            } catch (error) {
                console.error("Exception in selectPatient:", error);
                return false;
            }
        }
        <?php if($id): ?>
            editDiagnostic();
            async function editDiagnostic() {
                const customWait = await selectPatient(0, 0);
                // console.log(customWait);
                if (customWait) {
                    await getFormPatient();
                }
            }
            //patient_id, phone, service
            // patient form
            async function getFormPatient() {
                let url_patient = "<?php echo e($id && $patient_id ? config('patient.url') . 'edit/' . $patient_id : ''); ?>";
                // console.log(ht_id,url,JSON.stringify(filter),jwtToken);
                if (!jwtToken) {
                    redirectToSwal(loginUrl);
                    return false;
                }
                let response = await $.ajax({
                    type: 'GET',
                    url: url_patient,
                    crossDomain: true,
                    dataType: 'json',
                    cache: false, // Corrected from 'catch' to 'cache'
                    contentType: "application/json",
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                    },
                    data: JSON.stringify({}), // Convert data object to JSON string
                    processData: false, // Prevent jQuery from automatically transforming the data into a query string
                    success: function(data) {
                        // console.log(data);
                        $('#data-edit-patient').html(data.form); // Form HTML
                        $('.select2-multpl-custom1').select2();
                    },
                    error: function(response) {
                        console.error(response); // Log the error response for debugging
                        // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                    }
                });
                return response;
            }
            $(document).ready(function() {
                $(document).on("submit", "#data-edit-patient #submitForm", function(event) {
                    event.preventDefault(); // Prevent normal form submission
                    var form = $(this); // The form that was submitted
                    setRedirectUrl("");
                    // Append work_id=1 to the form data
                    let sample_id = '<?php echo e($id); ?>';
                    let fields = {
                        "sample_id": sample_id
                    };
                    appendExtraField(fields);
                    setFormEntity(form);
                    addUpdateForm();
                });
            });
        <?php endif; ?>
        <?php if(request('s_id')): ?>
            selectPatient(0, 0); //patient_id, phone, service
        <?php endif; ?>
        function redirectStoreRecord(patient_id, phone, service) {
            localStorage.removeItem('redirect_url_data');
            const redirectUrlData = '?pid=' + patient_id + '&phone=' + phone + '&service=' + service;
            localStorage.setItem('redirect_url_data', redirectUrlData);
        }
    </script>
    <?php if(null !== request('pid')): ?>
        <script>
            var pid = "<?php echo e(request('pid')); ?>";
            var phone = "<?php echo e(request('phone')); ?>";
            var service = "<?php echo e(request('service')); ?>";
            $('#search_patient').val(phone);
            searchPatient();
            selectPatient(pid, phone, service);
            const targetElement = document.getElementById("data-add-edit");
            targetElement.scrollIntoView({
                behavior: "smooth",
                block: "center"
            });
        </script>
    <?php endif; ?>
    <?php echo $__env->make('billing::billing.js.payment', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <script>
        $(document).on("keyup", ".amount_class", function() {
            let total = parseFloat($("#total_amount").val());
            let due = parseFloat($("#payable_amount").val());
            let percentage = $("#min_payment_percentage").val();
            percentage = percentage ? percentage : 100;
            if (total >= ((due * percentage) / 100).toFixed(2)) {
                $('#status').val(1);
            } else {
                $('#status').val(0);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/diagnostic/addWithPhlebo.blade.php ENDPATH**/ ?>