<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Doctor extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'id',
        'user_id',
        'img_url',
        'itdose_doctorid',
        'secret_key',
        'doctor_slug',
        'address',
        'profile',
        'registration_no',
        'docor_mail_verification_frontend',
        'doctor_visit',
        'visit_price',
        'degree',
        'description',
        'featured_doctor',
        'doctor_type',
        'speciality',
        'key_procedure_performed',
        'status',
        'doctor_esign',
        'data_source',
        'manage_bill',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointments::class, 'doctor_id', 'id');
    }
    public function specialitys(): BelongsTo
    {
        return $this->belongsTo(Speciality::class, 'speciality', 'id');
    }
    public function schedules(): HasMany
    {
        return $this->hasMany(Schedules::class, 'doctor_id', 'id');
    }
    public function doctorType(): BelongsTo
    {
        return $this->belongsTo(DoctorType::class, 'doctor_type', 'id');
    }
    public function sampleCollection(): HasMany
    {
        return $this->hasMany(SampleCollection::class, 'doctor_id', 'id');
    }
}
