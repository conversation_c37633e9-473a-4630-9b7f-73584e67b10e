<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Diagnostic\Http\Controllers\DiagnosticTestController;
use Modules\Diagnostic\Http\Controllers\DiagnosticController;
use Modules\Diagnostic\Http\Controllers\SampleCollectionController;
use Mo<PERSON>les\Diagnostic\Http\Controllers\SampleHandoverController;
use Mo<PERSON>les\Diagnostic\Http\Controllers\SampleSegregationController;
use Modules\Diagnostic\Http\Controllers\EstimationController;
use Modules\Diagnostic\Http\Controllers\LedgerController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('diagnostic')->group(function () {
    Route::get('/test', [DiagnosticTestController::class, 'index'])->name('diagnostic.test.index');
    Route::get('/test/add', [DiagnosticTestController::class, 'addForm'])->name('diagnostic.test.addForm');
    Route::get('/', [DiagnosticController::class, 'index'])->name('diagnostic.index');
    Route::get('/lab-test-status/{id}', [DiagnosticController::class, 'labTestStatus'])->name('diagnostic.labTestStatus');
    Route::get('/add', [DiagnosticController::class, 'addForm'])->name('diagnostic.addForm');
    Route::get('/phlebo-assign/{id}', [DiagnosticController::class, 'phleboAssign'])->name('diagnostic.phleboAssign');
    Route::get('/payment-settlement', [DiagnosticController::class, 'paymentSettlement'])->name('diagnostic.paymentSettlement');
    Route::get('/refund-settlement', [DiagnosticController::class, 'refundSettlement'])->name('diagnostic.refundSettlement');
    Route::get('/sample-status', [DiagnosticController::class, 'indexSampleStatus'])->name('diagnostic.sampleStatus.index');
    Route::get('/sample-collection', [SampleCollectionController::class, 'index'])->name('diagnostic.sampleCollection.index');
    Route::get('/sample-collection-individual', [SampleCollectionController::class, 'indexIndividual'])->name('diagnostic.sampleCollection.indexIndividual');
    Route::get('/home-collection', [SampleHandoverController::class, 'indexHomeCollection'])->name('diagnostic.homeCollection.index');
    Route::get('/sample-recollection', [SampleCollectionController::class, 'indexRecollection'])->name('diagnostic.sampleCollection.indexRecollection');
    Route::get('/barcode-edit', [SampleCollectionController::class, 'indexBarcodeEdit'])->name('diagnostic.barcodeEdit.index');
});
Route::prefix('diagnostic/sample-handover')->group(function () {
    Route::get('/{id}', [SampleHandoverController::class, 'index'])->name('diagnostic.sampleHandover.index');  
});
Route::get('diagnostic-invoice/{id}', [DiagnosticController::class, 'viewInvoice'])->name('diagnostic.viewInvoice');
Route::get('diagnostic-trf/{id}', [DiagnosticController::class, 'viewTRF'])->name('diagnostic.viewTRF');

Route::prefix('diagnostic/sample-segregation')->group(function () {
    Route::get('/', [SampleSegregationController::class, 'index'])->name('diagnostic.sampleSegregation.index');
    Route::get('/add', [SampleSegregationController::class, 'addForm'])->name('diagnostic.sampleSegregation.addForm');    
});
Route::get('/sample-transfer/pdf/{batchno}', [SampleSegregationController::class, 'sampleTransferPdf'])->name('diagnostic.sampleSegregation.sampleTransferPdf');

Route::prefix('diagnostic/estimation')->group(function () {
    Route::get('/', [EstimationController::class, 'index'])->name('diagnostic.estimation.index');
    Route::get('/add', [EstimationController::class, 'addForm'])->name('diagnostic.estimation.addForm');
    Route::get('/phlebo-assign/{id}', [EstimationController::class, 'phleboAssign'])->name('diagnostic.estimation.phleboAssign');
});
Route::get('diagnostic/estimation-invoice/{id}', [EstimationController::class, 'viewInvoice'])->name('diagnostic.estimation.viewInvoice');

Route::prefix('diagnostic/ledger')->group(function () {
    Route::get('/', [LedgerController::class, 'index'])->name('diagnostic.ledger.index');
    Route::get('/receipt-wise', [LedgerController::class, 'indexReceiptWise'])->name('diagnostic.ledger.indexReceiptWise');
});