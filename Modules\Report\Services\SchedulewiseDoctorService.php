<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;

use Modules\Report\Models\Schedule;
use Modules\Report\Models\District;
use Modules\Report\Models\User;
use Modules\Report\Models\Clinic;
use Modules\Report\Models\Holiday;
use Modules\Report\Models\DoctorType;
use DB;

class SchedulewiseDoctorService extends ApplicationDefaultService
{
    

    public $entity;
    public $entityDistrict;
    public $entityDoctor;
    public $entityClinic;
    public $entityDoctorType;
    
    public $columns = [
        'id',
        'doctor_id',
        'weekday',
        'date',
        's_time',
        'e_time',
        's_time_key',
        'duration',
        'clinic_id',
        'visit_price',
        'show_visit_price',
        'time_in',
        'time_out',
        'is_time',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'doctor_id',
        'weekday',
        'date',
        's_time',
        'e_time',
        's_time_key',
        'duration',
        'clinic_id',
        'visit_price',
        'show_visit_price',
        'time_in',
        'time_out',
        'is_time',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    
    public function __construct(Schedule $entity,District $entityDistrict,User $entityDoctor,Clinic $entityClinic,DoctorType $entityDoctorType) {
        //dd($entity);
        $this->entity =$entity;
        $this->entityDistrict =$entityDistrict;
        $this->entityDoctor =$entityDoctor;
        $this->entityClinic =$entityClinic;
        $this->entityDoctorType = $entityDoctorType;
    }



 public function allSchedules($doctor_id,$date=null,$clinic_id){
        // dd($doctor_id,$clinic_id);
        $this->entity = $this->entity->select($this->columns)
            ->where('status',1)
            ->where('doctor_id',$doctor_id);
        if ($date) {
            $this->entity = $this->entity->where('date',$date);
        }
        if ($clinic_id) {
            $this->entity = $this->entity->where('clinic_id',$clinic_id);
        }
        $this->entity = $this->entity->with('clinics:id,clinic_name');
        $this->entity = $this->entity->get();
        return $this->entity;
    }
    
    public function allDistricts(){
        $this->entityDistrict = $this->entityDistrict->select('id','district_name')
            ->where('status',1)
            ->get();
        return $this->entityDistrict;
    }
    public function allDoctors(){
        // $this->entityDoctor = $this->entityDoctor->select('id','user_id','img_url','doctor_type','speciality')
        //     ->where('status',1)
        //     ->with('user:id,username')
        //     ->get();
        $this->entityDoctor = $this->entityDoctor->select('id','ip_address','username','email','phone','status','created_by','modified_by','deleted_by','created_at','updated_at','deleted_at');
        
        $this->entityDoctor = $this->entityDoctor->get();
        // dd($user->getRoleNames()->toArray());//$user->getRoleNames()->toArray()
        return $this->entityDoctor;
    }


    public function allDoctorTypes(){
        $this->entityDoctorType = $this->entityDoctorType->select('id','title','price')
            ->where('status',1)
            ->get();
        return $this->entityDoctorType;
    }
    public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name','clinic_slug','district_id')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function getHolidays($clinic_id){
        $entityHoliday = Holiday::select('id','date')
            ->where('status',1)
            ->where('clinic_id',$clinic_id)
            ->pluck('date','id')->toArray();
        return $entityHoliday;
    }
    
    
    
}
