<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Diagnostic\Http\Controllers\DiagnosticTestController;
use Modules\Diagnostic\Http\Controllers\DiagnosticController;
use Modules\Diagnostic\Http\Controllers\SampleCollectionController;
use Mo<PERSON>les\Diagnostic\Http\Controllers\SampleHandoverController;
use Mo<PERSON>les\Diagnostic\Http\Controllers\SampleSegregationController;
use Modules\Diagnostic\Http\Controllers\EstimationController;
use Modules\Diagnostic\Http\Controllers\LedgerController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/
Route::middleware(['auth:api'])->prefix('diagnosticTest')->group(function () {
    Route::middleware(['permission:view_diagnostic_test'])->post('/list', [DiagnosticTestController::class, 'list']);
    Route::middleware(['permission:create_diagnostic_test'])->get('/create', [DiagnosticTestController::class, 'create']);
    Route::middleware(['permission:create_diagnostic_test'])->post('/add', [DiagnosticTestController::class, 'add']);
    Route::middleware(['permission:edit_diagnostic_test'])->post('/update/{id}', [DiagnosticTestController::class, 'update']);
    Route::middleware(['permission:edit_diagnostic_test'])->get('/edit/{id}', [DiagnosticTestController::class, 'edit']);
    Route::middleware(['permission:info_diagnostic_test'])->get('/detail/{id}', [DiagnosticTestController::class, 'detail']);
    Route::middleware(['permission:change_status_diagnostic_test'])->post('/updateStatus/{id}', [DiagnosticTestController::class, 'updateStatus']);
    Route::middleware(['permission:delete_diagnostic_test'])->get('/delete/{id}', [DiagnosticTestController::class, 'delete']);
});

Route::middleware(['auth:api'])->prefix('diagnostic')->group(function () {
    Route::middleware(['permission:view_diagnostic'])->post('/list', [DiagnosticController::class, 'list']);
    Route::middleware(['permission:view_diagnostic'])->post('/labTestMaster', [DiagnosticController::class, 'labTestMaster']);
    Route::middleware(['permission:view_diagnostic'])->post('/labTestList', [DiagnosticController::class, 'labTestList']);
    Route::middleware(['permission:create_diagnostic'])->post('/create', [DiagnosticController::class, 'create']);
    Route::middleware(['permission:create_with_phlebo_diagnostic'])->post('/createWithPhlebo', [DiagnosticController::class, 'createWithPhlebo']);
    Route::middleware(['permission:create_with_phlebo_diagnostic'])->post('/getPhleboWithAssignCount', [DiagnosticController::class, 'getPhleboWithAssignCount']);
    Route::middleware(['permission:create_dispute_diagnostic'])->post('/createDispute', [DiagnosticController::class, 'createDispute']);
    Route::middleware(['permission:create_diagnostic|create_with_phlebo_diagnostic'])->post('/listFamily', [DiagnosticController::class, 'listFamily']);
    Route::middleware(['permission:create_diagnostic|create_with_phlebo_diagnostic'])->post('/testItemList', [DiagnosticController::class, 'testItemList']);
    Route::middleware(['permission:create_diagnostic'])->post('/add', [DiagnosticController::class, 'add']);
    Route::middleware(['permission:create_with_phlebo_diagnostic'])->post('/addWithPhlebo', [DiagnosticController::class, 'addWithPhlebo']);
    // Route::middleware(['permission:create_dispute_diagnostic'])->post('/addDispute', [DiagnosticController::class, 'addDispute']);
    Route::middleware(['permission:edit_diagnostic'])->post('/update/{id}', [DiagnosticController::class, 'update']);
    Route::middleware(['permission:edit_diagnostic'])->post('/edit/{id}', [DiagnosticController::class, 'edit']);
    // Route::middleware(['permission:info_diagnostic_test'])->get('/detail/{id}', [DiagnosticController::class, 'detail']);
    Route::middleware(['permission:change_status_diagnostic'])->post('/updateStatus/{id}', [DiagnosticController::class, 'updateStatus']);
    // Route::middleware(['permission:delete_diagnostic_test'])->get('/delete/{id}', [DiagnosticController::class, 'delete']);
    Route::middleware(['permission:phlebo_assign_diagnostic'])->get('/editAssignPhlebo/{id}', [DiagnosticController::class, 'editAssignPhlebo']);
    Route::middleware(['permission:phlebo_assign_diagnostic'])->post('/updateAssignPhlebo/{id}', [DiagnosticController::class, 'updateAssignPhlebo']);
    Route::middleware(['permission:payment_settlement_diagnostic'])->post('/paymentSettlementForm/{id}', [DiagnosticController::class, 'paymentSettlementForm']);
    Route::middleware(['permission:payment_settlement_diagnostic'])->post('/updatePaymentSettlement/{id}', [DiagnosticController::class, 'updatePaymentSettlement']);
    Route::middleware(['permission:refund_settlement_diagnostic'])->post('/refundSettlementForm/{id}', [DiagnosticController::class, 'refundSettlementForm']);
    Route::middleware(['permission:refund_settlement_diagnostic'])->post('/updateRefundSettlement/{id}', [DiagnosticController::class, 'updateRefundSettlement']);
    Route::middleware(['permission:upload_prescription_diagnostic'])->post('/uploadPrescription', [DiagnosticController::class, 'uploadPrescription']);
    Route::middleware(['permission:view_diagnostic_phlebo_dashboard|view_home_collection_diagnostic'])->post('/listPhleboDashboard', [DiagnosticController::class, 'listPhleboDashboard']);
    Route::middleware(['permission:view_diagnostic_sample_status'])->post('/listSampleStatus', [DiagnosticController::class, 'listSampleStatus']);
});

Route::prefix('diagnosticSampleCollection')->group(function () {
    Route::middleware(['permission:view_sample_collection_diagnostic'])->post('/list', [SampleCollectionController::class, 'list']);
    Route::middleware(['permission:view_sample_collection_diagnostic'])->get('/formCollection/{id}', [SampleCollectionController::class, 'formCollection']);
    Route::middleware(['permission:view_sample_collection_diagnostic'])->post('/patientSignatureCollection', [SampleCollectionController::class, 'patientSignatureCollection']);
    Route::middleware(['permission:view_sample_collection_diagnostic'])->post('/addSampleCollection/{id}', [SampleCollectionController::class, 'addSampleCollection']);

    Route::middleware(['permission:view_sample_recollection_diagnostic'])->post('/listRecollection', [SampleCollectionController::class, 'listRecollection']);
    Route::middleware(['permission:view_sample_recollection_diagnostic'])->get('/formRecollection/{id}', [SampleCollectionController::class, 'formRecollection']);
    Route::middleware(['permission:view_sample_recollection_diagnostic'])->post('/addSampleRecollection/{id}', [SampleCollectionController::class, 'addSampleRecollection']);

    Route::middleware(['permission:edit_barcode_diagnostic'])->post('/listBarcodeEdit', [SampleCollectionController::class, 'listBarcodeEdit']);
    Route::middleware(['permission:edit_barcode_diagnostic'])->get('/formBarcodeEdit/{id}', [SampleCollectionController::class, 'formBarcodeEdit']);
    Route::middleware(['permission:edit_barcode_diagnostic'])->post('/addBarcodeEdit/{id}', [SampleCollectionController::class, 'addBarcodeEdit']);
});

Route::middleware(['auth:api'])->prefix('diagnosticSampleHandover')->group(function () {
    Route::middleware(['permission:view_diagnostic_phlebo_dashboard|view_home_collection_diagnostic'])->get('/create/{id}', [SampleHandoverController::class, 'create']);
    Route::middleware(['permission:view_diagnostic_phlebo_dashboard|view_home_collection_diagnostic'])->post('/getUsers', [SampleHandoverController::class, 'getUsers']);
    Route::middleware(['permission:view_diagnostic_phlebo_dashboard|view_home_collection_diagnostic'])->post('/add/{id}', [SampleHandoverController::class, 'add']);
});

Route::middleware(['auth:api'])->prefix('diagnosticSampleSegregation')->group(function () {
    Route::middleware(['permission:view_sample_s_t_diagnostic'])->post('/list', [SampleSegregationController::class, 'list']);
    Route::middleware(['permission:create_sample_s_t_diagnostic'])->post('/listTest', [SampleSegregationController::class, 'listTest']);
    Route::middleware(['permission:create_sample_s_t_diagnostic'])->post('/create', [SampleSegregationController::class, 'create']);
    Route::middleware(['permission:create_sample_s_t_diagnostic'])->post('/add', [SampleSegregationController::class, 'add']);
    Route::middleware(['permission:info_sample_s_t_diagnostic'])->get('/detail/{id}', [SampleSegregationController::class, 'detail']);
});

Route::middleware(['auth:api'])->prefix('diagnosticEstimation')->group(function () {
    Route::middleware(['permission:view_estimation_diagnostic'])->post('/list', [EstimationController::class, 'list']);
    Route::middleware(['permission:create_estimation_diagnostic'])->post('/create', [EstimationController::class, 'create']);
    Route::middleware(['permission:create_estimation_diagnostic'])->post('/listFamily', [EstimationController::class, 'listFamily']);
    Route::middleware(['permission:create_estimation_diagnostic'])->post('/testItemList', [EstimationController::class, 'testItemList']);
    Route::middleware(['permission:create_estimation_diagnostic'])->post('/add', [EstimationController::class, 'add']);
    Route::middleware(['permission:phlebo_assign_estimation_diagnostic'])->get('/editAssignPhlebo/{id}', [EstimationController::class, 'editAssignPhlebo']);
    Route::middleware(['permission:phlebo_assign_estimation_diagnostic'])->post('/updateAssignPhlebo/{id}', [EstimationController::class, 'updateAssignPhlebo']);
});

Route::middleware(['auth:api'])->prefix('diagnosticLedger')->group(function () {
    Route::middleware(['permission:ledger_diagnostic'])->post('/list', [LedgerController::class, 'list']);
    Route::middleware(['permission:ledger_diagnostic'])->post('/listReceiptWise', [LedgerController::class, 'listReceiptWise']);
});

Route::post('/itdose/updateLabStatus', [SampleCollectionController::class, 'updateLabStatusItdose']);
Route::post('/itdose/updateLabReport', [SampleCollectionController::class, 'updateLabReportItdose']);
