<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('reviews_survey_results')) {
            Schema::create('reviews_survey_results', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('survey_id')->nullable();
                $table->text('url_slag')->nullable();
                $table->unsignedInteger('patient_id')->nullable();
                $table->tinyInteger('source_type')->comment('Default=>0,Appointment=>1,Pharmacy=>2,Diagnostic=>3')->default('0');
                $table->unsignedInteger('source_id')->nullable()->comment('Source id based on source type');
                $table->tinyInteger('status')->comment('0:inactive,1:active')->default('1');
                $table->unsignedMediumInteger('created_by')->nullable();
                $table->unsignedMediumInteger('modified_by')->nullable();
                $table->unsignedMediumInteger('deleted_by')->nullable();
                $table->timestamps();
                $table->timestamp('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews_survey_results');
    }
};
