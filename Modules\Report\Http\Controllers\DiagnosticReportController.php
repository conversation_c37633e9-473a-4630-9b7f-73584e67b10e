<?php

namespace Modules\Report\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\DiagnosticReportService;
use Modules\Report\Services\DiagnosticBreakupBillService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use App\Exports\DiagnosticReportExportxls;
use App\Exports\DiagnosticInvestigationReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class DiagnosticReportController extends Controller
{
    private $diagnosticReportService;
    private $diagnosticBreakupBillService;
    public function __construct(
        DiagnosticReportService $diagnosticReportService,
        DiagnosticBreakupBillService $diagnosticBreakupBillService,
    ) {
        $this->diagnosticReportService = $diagnosticReportService;
        $this->diagnosticBreakupBillService = $diagnosticBreakupBillService;
    }
    public function indexDiagnosticReport(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticReportService->allClinics()->toArray(),
        ];
        return view('report::diagnostic.index', compact('data'));
    }

    public function diagnostic(Request $request)
    {
        try {

            $filter = $request['filter'];
            $request->merge([
                'filter' => $filter
            ]);
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ],
                'clinics' => [
                    'reletion' => [
                        'prefix' => 'clinic_id',
                        'suffix' => 'id'
                    ]
                ],

            ];

            $request['with'] = [
                'userDoctors' => 'id,username as doctor_name',
                'tests' => 'id,sample_collection_id,item_id',
                'createdBy' => 'id,username',
                'collectedBy' => 'username as sin_created_by_name',
                'paymentBillReward.payments' => 'id',
                'paymentBillRefund.payments' => 'id',
            ];

            $request['withFunc'] = [
                'paymentBill as bill_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'bill_amount'
                ],
                'paymentBill as discount_sum' => [
                    'method' => 'withSum',
                    'column' => 'discount'
                ],
                'paymentBill as total_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'total_amount'
                ],
                'paymentBill as paid_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'paid_amount'
                ],
                'paymentBill as due_amount_sum' => [
                    'method' => 'withSum',
                    'column' => 'due_amount'
                ],
                'diagnosticBillHC as home_collection_sum' => [
                    'method' => 'withSum',
                    'column' => 'net_amount'
                ],

            ];
            array_push(
                $this->diagnosticReportService->columns,
                'patients.name as patient_name',
                'patients.phone as patient_mobile',
                'patients.sex as patient_gender',
                'patients.birthdate as patient_birthdate',
                'clinics.clinic_name as clinic_name',
            );
            $this->diagnosticReportService->setRequest($request);
            $this->diagnosticReportService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticReportService->getRows();
            $status_list = config('report.diagnostic_status_list');
            $visit_type = config('report.visit_type');
            $diagnostic_docotor_type = config('report.diagnostic_doctor_type');
            $diagnostic_status_list = config('report.diagnostic_status_list');
            $this->response['tbody'] = view('report::diagnostic.api.list', compact('data', 'status_list', 'visit_type', 'diagnostic_docotor_type', 'diagnostic_status_list'))->render();
            $this->response['tfoot'] = $this->diagnosticReportService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function diagnosticReportExport(Request $request)
    {
        try {
            $this->response['req']  = $request->except('pagination');
            $this->response['url']  = route('report.diagnostic.exportLink', ['req' => $request->except('pagination')]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function diagnosticReportExportLink()
    {
        $request = request('req');
        $request['join'] = [
            'patients' => [
                'reletion' => [
                    'prefix' => 'patient_id',
                    'suffix' => 'id'
                ]
            ],
            'clinics' => [
                'reletion' => [
                    'prefix' => 'clinic_id',
                    'suffix' => 'id'
                ]
            ],

        ];

        $request['with'] = [
            'userDoctors' => 'id,username as doctor_name',
            'tests' => 'id,sample_collection_id,item_id',
            'createdBy' => 'id,username',
            'collectedBy' => 'username as sin_created_by_name',
            'paymentBillReward.payments' => 'id',
            'paymentBillRefund.payments' => 'id',
        ];

        $request['withFunc'] = [
            'paymentBill as bill_amount_sum' => [
                'method' => 'withSum',
                'column' => 'bill_amount'
            ],
            'paymentBill as discount_sum' => [
                'method' => 'withSum',
                'column' => 'discount'
            ],
            'paymentBill as total_amount_sum' => [
                'method' => 'withSum',
                'column' => 'total_amount'
            ],
            'paymentBill as paid_amount_sum' => [
                'method' => 'withSum',
                'column' => 'paid_amount'
            ],
            'paymentBill as due_amount_sum' => [
                'method' => 'withSum',
                'column' => 'due_amount'
            ],
            'diagnosticBillHC as home_collection_sum' => [
                'method' => 'withSum',
                'column' => 'net_amount'
            ],

        ];
        array_push(
            $this->diagnosticReportService->columns,
            'patients.name as patient_name',
            'patients.phone as patient_mobile',
            'patients.sex as patient_gender',
            'patients.birthdate as patient_birthdate',
            'clinics.clinic_name as clinic_name',
        );

        $this->diagnosticReportService->setRequest($request);

        $reportData  = $this->diagnosticReportService->findAll()->entity->get()->toArray();
        $list = [
            'status_list' => config('report.diagnostic_status_list'),
            'visit_type' => config('report.visit_type'),
            'diagnostic_docotor_type' => config('report.diagnostic_doctor_type'),
            'diagnostic_status_list' => config('report.diagnostic_status_list'),
        ];
        $data = Excel::download(new DiagnosticReportExportxls($reportData, $list), 'diagnosticworkerid.xlsx');
        return $data;
    }

    //Investigation Wise Report
    public function indexDiagnosticReportInvestigationWise(Request $request)
    {
        $data = [
            'clinic_list' => $this->diagnosticBreakupBillService->allClinics()->toArray(),
        ];
        return view('report::diagnostic.diagnosticInvestigationWiseIndex', compact('data'));
    }

    public function diagnosticInvestigationWise(Request $request)
    {
        try {

            $filter = $request['filter'];
            $request['with'] = [
                'sampleCollectionbreakup' => 'id,patient_id,clinic_id,doctor_id,unique_id,offered_id,date_of_collection,type_of_collection,appointment_type,data_source,created_by,created_at,doctortype,doctor_name,status',
                'sampleCollectionbreakup.userDoctors' => 'id,username as doctor_name',
                'test' => 'id,test_name,department_id',
            ];
            $this->diagnosticBreakupBillService->setRequest($request);
            $this->diagnosticBreakupBillService->findAll();
            $this->response['success']  = true;
            $data = $this->diagnosticBreakupBillService->getRows();
            $status_list = config('report.diagnostic_status_list');
            $visit_type = config('report.visit_type');
            $diagnostic_docotor_type = config('report.diagnostic_doctor_type');
            $diagnostic_status_list = config('report.diagnostic_status_list');
            $this->response['tbody'] = view('report::diagnostic.api.investigatationWiseList', compact('data', 'status_list', 'visit_type', 'diagnostic_docotor_type', 'diagnostic_status_list'))->render();
            $this->response['tfoot'] = $this->diagnosticReportService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function diagnosticInvestigationReportExport(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.diagnosticreportinvestigation.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function diagnosticReportInvestigationExportLink()
    {
        $request = request('req');
        $request['with'] = [
            'sampleCollectionbreakup' => 'id,patient_id,clinic_id,doctor_id,unique_id,offered_id,date_of_collection,type_of_collection,appointment_type,data_source,created_by,created_at,doctortype,doctor_name,status',
            'sampleCollectionbreakup.userDoctors' => 'id,username as doctor_name',
            'test' => 'id,test_name,department_id',
        ];
        $this->diagnosticBreakupBillService->setRequest($request);
        $this->diagnosticBreakupBillService->findAll();
        $this->response['success']  = true;
        $data = $this->diagnosticBreakupBillService->getRows();
        $status_list = config('report.diagnostic_status_list');
        $visit_type = config('report.visit_type');
        $diagnostic_docotor_type = config('report.diagnostic_doctor_type');
        $diagnostic_status_list = config('report.diagnostic_status_list');
        // dd($data);
        $data = Excel::download(new DiagnosticInvestigationReportExport($data, $status_list, $visit_type, $diagnostic_docotor_type, $diagnostic_status_list), 'diagnostic-workerid.xlsx');
        return $data;
    }
}
