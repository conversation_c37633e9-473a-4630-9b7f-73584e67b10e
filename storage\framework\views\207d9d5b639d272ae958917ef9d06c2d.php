<?php $__env->startSection('title'); ?>
    <?php echo e(config('diagnostic.ledger_title', 'Ledger')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
    <style>
        .tooltip1 {
            display: block;
            position: relative;
        }

        .tooltip1 .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #626262;
            color: #fff;
            border-radius: 6px;
            padding: 0px;
            position: absolute;
            z-index: 100;
            top: 10%;
            right: 27px;
            font-size: 11px;
            padding-right: 0;
            text-align: start;
        }

        .tooltip1 .tooltiptext::after {
            content: "";
            position: absolute;
            top: 15px;
            right: -10px;
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent transparent #343434;
        }

        .tooltip1:hover .tooltiptext {
            visibility: visible;
        }

        .icon-wrap {
            /* width: 83px;
                                                text-align: center; */
            width: 100%;
            text-align: center;
            display: flex;
            flex-wrap: nowrap;
            flex-direction: row !important;
            gap: 5px;
            align-items: center !important;
        }

        @media only screen and (max-width:767px) {

            .icon-wrap {
                width: auto;
            }

        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-md-12">
        <div class="card mb-3">
            <div class="card-body d-lg-flex  justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">WorkorderID Wise Ledger</h4>
                </div>
                <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                    <div class="d-flex flex-md-nowrap flex-wrap gap-2 ">
                        <div class="form-group mb-0 w-100">
                            <select name="date_of_collection" id="date_type"
                                class="select2-multpl-custom1 form-select search-date-range" data-style="py-0">
                                <option value="<?php echo e(date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d') ? 'selected' : ''); ?>>Today</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-1 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-1 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Yesterday
                                </option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Past
                                    7
                                    Days</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Past
                                    15
                                    Days</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Past
                                    30
                                    Days</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Past
                                    60
                                    Days</option>
                                <option value="<?php echo e(date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d')); ?>"
                                    <?php echo e(request('d_type') == date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                    Past
                                    90
                                    Days</option>
                            </select>
                        </div>
                        <div class="form-group mb-0 w-100">
                            <input type="text" name="date_of_collection" id="date_range"
                                placeholder="Please select a date range"
                                class="form-control form-control-sm flatpickr-input search-date-range active"
                                readonly="readonly" value="<?php echo e(request('d_range')); ?>">
                        </div>
                        <div class="form-group mb-0 w-100">
                            <select name="clinic_id" id="clinic_id" class="select2-multpl-custom1 form-select search-change"
                                data-style="py-0">
                                <option value="">Filter By Clinic</option>
                                <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($row['id']); ?>">
                                        <?php echo e($row['clinic_name']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <a href="<?php echo e(route('diagnostic.ledger.index')); ?>">
                        <button type="button" class="btn btn-sm btn-primary">Reset</button>
                    </a>
                    <a href="<?php echo e(route('diagnostic.ledger.indexReceiptWise')); ?>">
                        <button type="button" class="btn btn-sm btn-primary">Receipt Wise</button>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm px-1">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>
                    <div class="col-md-10 ps-md-5">
                        <div class="row justify-content-end">
                            <div class="col-md-4 col-6 px-1 mb-1 mb-md-0">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="1,2">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    SL No
                                </th>
                                <th>
                                    Visit Date
                                </th>
                                <th>
                                    Workorder-ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Collection Date
                                </th>
                                <th>
                                    Clinic
                                </th>
                                <th>
                                    Patient
                                </th>
                                <th>
                                    Billed Amt(₹)
                                </th>
                                <th>
                                    Discount(₹)
                                </th>
                                <th>
                                    redemption
                                </th>
                                <th>
                                    Net Amt(₹)
                                </th>
                                <th>
                                    Paid(₹)
                                </th>
                                <th>
                                    Due(₹)
                                </th>
                                <th>
                                    Created By
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo $__env->make('admin.custom.loading', ['td' => 14, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tbody>
                        <tfoot>
                            <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Diagnostic Info</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            // minDate: "today"
        });
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('diagnostic.ledger_url') . 'list'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                // "id",
                // "unique_id",
                // "patients.name",
                // "patient_phone",
                // "clinics.clinic_name",
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    "date_of_collection": {
                        "type": "eq",
                        "value": "<?php echo e(date('Y-m-d')); ?>"
                    }
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });

        function updateDiagnosticStatus(url, method, parent) {
            // console.log(url,method);
            if (!jwtToken) {
                redirectToSwal(loginUrl);
                return false;
            }
            // let field = $(parent).attr('name');
            let status = $(parent).val();
            // console.log(field, status);
            // return false;
            $.ajax({
                type: method,
                url: url,
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    status: status
                }), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                success: function(data) {
                    // console.log(data);
                    if (data.success == true) {
                        successAlertSwal(data);
                    } else {
                        errorAlertSwal(data);
                    }
                    getList();
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/ledger/index.blade.php ENDPATH**/ ?>