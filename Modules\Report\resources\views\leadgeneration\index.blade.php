@extends('admin.layouts.app')
@section('title')
    {{ config('report.title', 'Medicine Order Report') }}
@endsection
@section('meta')
@endsection
@section('style')
@endsection
@section('content')
    <div class="conatiner-fluid content-inner p-3">
        <div class="row">
            <!-----Report filter starts here-------->
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body   justify-content-between align-items-center">
                        <div class="header-title mb-3">
                            <h4 class="card-title mb-3 mb-md-0">Lead Generation Report</h4>
                        </div>
                        <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                            <div class="d-flex flex-lg-nowrap flex-wrap gap-2 ">
                                <div class="form-group mb-0 w-100">
                                    <label class="mb-1 d-flex gap-2 align-items-center">
                                        <span>Show</span>
                                        <select id="perPageCount" class="form-select form-select-sm px-1 search-change"
                                            style="min-width: 80px">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>entries</span>
                                    </label>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select-sm search-change" data-style="py-0"
                                        id="doctor_type" name="doctor_type" style="width: 100%; min-width:200px;">
                                        <option value="">Select Data Source</option>
                                        @foreach ($data['data_source_list'] as $id => $label)
                                            <option value="{{ $id }}">{{ $label }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <input type="text" name="date" id="date_range"
                                        placeholder="Please select a date range"
                                        class="form-control form-control-sm flatpickr-input active search-date-range"
                                        readonly="readonly">
                                </div>
                            </div>
                            <input type="search" class="form-control form-control-sm search_change" value=""
                                placeholder="Search by Name or Phone No" data-index="0,1">
                            <button type="button" class="btn btn-sm btn-primary"
                                onclick="searchNamePhone()">Filter</button>
                            <a href=""><button type="button" class="btn btn-sm btn-primary">Reset</button></a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV(this)">Export</button>
                        </div>
                    </div>
                </div>
            </div>
            <!------Report filter ends here-------->
        </div>
        <!------Report box ends here-------->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="Table-custom-padding1 table-responsive ">
                        <table id="data-list" class="table table-sm table-sm0 table-striped table-hover w-100 dataTable "
                            data-page-length='25'>
                            <thead>
                                <tr>
                                    <th class="word-wrap wht-space-custom">SL No</th>
                                    <th class="word-wrap wht-space-custom">Created At</th>
                                    <th class="word-wrap wht-space-custom">Patient Name </th>
                                    <th class="word-wrap wht-space-custom">Phone</th>
                                    <th class="word-wrap wht-space-custom">Gender</th>
                                    <th class="word-wrap wht-space-custom">Age</th>
                                    <th class="word-wrap wht-space-custom">Remarks</th>
                                    <th class="word-wrap wht-space-custom">Created By</th>
                                    <th class="word-wrap wht-space-custom">Latitude/longitude</th>
                                    <th class="word-wrap wht-space-custom">Location</th>
                                    <th class="word-wrap wht-space-custom">Clinic</th>
                                </tr>
                            </thead>
                            <tbody>
                                @include('admin.custom.loading', ['td' => 12, 'action' => 0])
                            </tbody>
                            <tfoot>
                                @include('admin.custom.loadingPagination')
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre loading data ends here -->
@endsection

@push('scripts')
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });

        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id

            let url = "{{ config('report.url') . 'leadgeneration' }}";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            /* let sortCollumns = [
                 "id",
                 "patients.name",
                 "phone",
                 "registration_no"
             ];
             setSortCollumns(sortCollumns);*/
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {

                "filter": {

                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 10,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });



        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "{{ config('report.url') . 'medicineorderexport' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
@endpush
