<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderMedicine extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'patient_id',
        'patient_phone',
        'type_of_collection',
        'clinic_id',
        'building_no',
        'full_address',
        'landmark',
        'city',
        'pincode',
        'prescription_upload',
        'data_source',
        'source',
        'remarks',
        'latitude',
        'longitude',
        'total_amount',
        'reject_remarks',
        'is_accept',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $dates = ['deleted_at'];

    public function orderChildMedicines(): HasMany
    {
        return $this->hasMany(OrderChildMedicine::class, 'order_id', 'id');
    }
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class, 'clinic_id', 'id');
    }
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id');
    }

    public function createdBy(): BelongsTo
{
    return $this->belongsTo(User::class, 'created_by', 'id');
}
}
