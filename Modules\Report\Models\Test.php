<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Test extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'itdose_testid',
        'test_name',
        'test_price',
        'test_code',
        'sample_type_id',
        'sample_type',
        'delivery_date',
        'department_id',
        'sub_parameter_name',
        'method',
        'test_information',
        'sample_quantity',
        'package_test',
        'is_package',
        'is_inhouse',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function departments(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }
    public function packageTest($test_ids)
    {
        return $this->whereIn('itdose_testid', json_decode($test_ids))->pluck('test_name', 'id')->toArray();
    }
    public function sampleCollection()
    {
        return $this->hasMany(SampleCollection::class, 'test_id', 'id');
    }
}
