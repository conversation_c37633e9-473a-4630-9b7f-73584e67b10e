<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td>
            <a href="javascript:void(0);" onclick="findSample('<?php echo e($row['id']); ?>')"> <?php echo e($row['unique_id']); ?></a>
        </td>
        <td><?php echo e($row['date_of_collection']); ?></td>
        <td>
            <?php echo e($row['patient_name']); ?>

            <br>
            <?php echo e($row['patient_phone']); ?>

        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php if($clinic_id): ?>
    <script>
        // let user_clinic_id = "<?php echo e($clinic_id); ?>";
        $('#clinic_id').val(String("<?php echo e($clinic_id); ?>")).trigger('change.select2');
        $('#clinic_id').prop('disabled', true);
    </script>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/barcodeEdit/api/list.blade.php ENDPATH**/ ?>