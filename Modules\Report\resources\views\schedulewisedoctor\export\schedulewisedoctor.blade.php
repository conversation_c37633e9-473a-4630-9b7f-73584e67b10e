<!DOCTYPE html>
<html>

<head>
    <title>Export Data</title>
    <meta charset="utf-8">
</head>

<body>
    <table>
        <thead>
            <tr>						
               
                <th>
                   Doctor
                </th>
                <th>
                    Speciality
                </th>
                <th>
                    Doctor Type
                </th>
                <th>
                    Clinic
                </th>
                <th>
                    Date
                </th>
                <th>
                    Scheduled Time
                </th>
                <th>
                    Time In
                </th>
                <th>
                   Time Out
                </th>
                <th>
                   Time Spent
                </th>
                <th>
                    Patient	
                </th>

               
                
            </tr>
        </thead>
        <tbody>
          
         

 @if (!empty($data))
               @foreach ($data as $row)
@php
        $timeIn = isset($row['time_in']) ? strtotime($row['time_in']) : null;
        $timeOut = isset($row['time_out']) ? strtotime($row['time_out']) : null;

        $timeSpent = '';
        if ($timeIn && $timeOut) {
            $diffInSeconds = $timeOut - $timeIn;
            $hours = floor($diffInSeconds / 3600);
            $minutes = floor(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $timeSpent = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    @endphp
     <tr>
         <td>{{ $row['users']['username'] ?? '' }}</td>
         <td>{{ $row['patients']['name'] ?? 'Speciality' }}</td>
        <td>{{ $row['users']['schedule_doctors']['doctor_type']['title'] ?? '' }}</td>
         <td>{{ $row['clinics']['clinic_name'] ?? '' }}</td>
         <td>{{ $row['date'] ?? '' }}</td>
         <td>{{ $row['s_time'] }} to {{ $row['e_time'] }}</td>
         <td>{{ $row['time_in'] ?? '' }}</td>
         <td>{{ $row['time_out'] ?? '' }}</td>

          <td>{{ $timeSpent }}</td>
        <td>{{ $row['completed_appointments_count'] ?? 0 }}</td> 
        
     </tr>
@endforeach

@endif
        </tbody>
    </table>
</body>

</html>
