<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\SampleCollectionEstimation;
use Modules\Report\Models\SampleCollection;
use Modules\Report\Models\Clinic;
use Modules\Report\Models\Doctor;
use Modules\Report\Models\Patient;
use Modules\Report\Models\MembershipRegistration;
use Modules\Report\Models\Membership;
use Modules\Report\Models\Test;
use Modules\Report\Models\LabTimingMaster;
use Modules\Report\Models\SampleHomecollectionCharge;
use DB;
use Carbon\Carbon;

class EstimationService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;
    public $entityDoctor;
    public $entityPatient;
    public $entityTest;
    public $columns = [
        'id',
        'patient_id',
        'full_address',
        'land_mark',
        'city',
        'pincode',
        'test_id',
        'collection_id',
        'clinic_id',
        'sub_total',
        'discount',
        'gross_total',
        'assign_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'test_id',
        'collection_id',
        'clinic_id',
        'sub_total',
        'discount',
        'gross_total',
        'assign_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(SampleCollectionEstimation $entity,Clinic $entityClinic, Doctor $entityDoctor, Test $entityTest, Patient $entityPatient) {
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
        $this->entityDoctor =$entityDoctor;
        $this->entityTest =$entityTest;
        $this->entityPatient =$entityPatient;
    }
    public function allSampleCollectionEstimations(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function allDoctors(){
        $this->entityDoctor = $this->entityDoctor->select('id','user_id','doctor_type')
            ->where('status',1)
            ->with('user:id,username')
            ->get();
        return $this->entityDoctor;
    }
    public function allTests(){
        $this->entityTest = $this->entityTest->select('*')
            ->where('status',1)
            ->get();
        return $this->entityTest;
    }
    public function getPatientID($phone){
        $patient_id = Patient::where('phone',$phone)->value('id');
        return $patient_id;
    }
    public function getFamilys($phone){
        $family_id = $this->entityPatient->where('phone',$phone)->value('family_id');
        $this->entityPatient = $this->entityPatient->select('id','family_id','name','sex')
            ->where('family_id',$family_id)
            ->with('membershipRegistrations')
            ->get();
        return $this->entityPatient;
    }
    public function findPatient($patient_id){
        $this->entityPatient = $this->entityPatient->find($patient_id);
        return $this->entityPatient;
    }
    public function activeMembership($patient_id){
        $data = MembershipRegistration::where('patient_id',$patient_id)->select('card_type','end_date','category_id','clinic_id','registration_no');
        $data = $data->where('status',3);
        $data = $data->where('category_id',1);
        $data = $data->with('memberships:id,name');
        $data = $data->with('clinics:id,clinic_name');
        $data = $data->get()->toArray();
        return $data;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            SampleCollectionEstimation::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
            DB::table('temp_increment_migrations')
                ->where('table_name', 'sample_collection_estimations')
                ->increment('count');
        });
    }
}
