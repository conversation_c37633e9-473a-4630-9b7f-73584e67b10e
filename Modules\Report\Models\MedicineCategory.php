<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MedicineCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'medicine_categorys';
    protected $fillable = [
        'id',
        'category',
        'description',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $dates = ['deleted_at'];
    public function medicines(): HasMany
    {
        return $this->hasMany(Medicine::class, 'category', 'id');
    }
}
