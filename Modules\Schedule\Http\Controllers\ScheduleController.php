<?php

namespace Modules\Schedule\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Schedule\Http\Requests\ScheduleRequest;
use Modules\Schedule\Services\ScheduleService;
use Modules\Users\Services\UserService;
use Illuminate\Support\Str;
use App\Traits\DBConnection;
use Modules\Schedule\Models\Doctor;
use Modules\Schedule\Models\DoctorType;
use DB;
use Carbon\Carbon;
use App\Exports\ScheduleExport;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Helpers\Helper;
use App\Services\ExportCSVService;

class ScheduleController extends Controller
{
    use DBConnection;
    private $scheduleService;
    private $userService;
    private $queryBuilder;

    public function __construct(ScheduleService $scheduleService, UserService $userService)
    {
        $this->setMymdDBConnection();
        $this->scheduleService = $scheduleService;
        $this->userService = $userService;
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->scheduleService->allClinics()->toArray()
        ];
        return view('schedule::schedule.index',compact('data'));
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $data = [];
        return view('schedule::schedule.add',compact('id','data'));
    }
    public function editForm(Request $request)
    {
        $data = [
            'doctor_list' => $this->userService->allUserWithRole('Doctor'),
            'clinic_list' => $this->scheduleService->allClinics()->toArray(),
            'time_list' => config('schedule.times')
        ];
        // dd($data['doctor_list']);
        return view('schedule::schedule.edit',compact('data'));
    }
    public function list(Request $request)
    {
        try {
            $role = $this->getUserRole();
            switch($role->id) {
                case 4:
                    $clinic_id = auth()->user()->centerManager->clinic_id;
                    break;
                case 5:
                    $clinic_id = auth()->user()->receptionist->clinic_id;
                    break;
                case 6:
                    $clinic_id = auth()->user()->nurse->clinic_id;
                    break;
                default:
                    $clinic_id = null;
                    break;
            }
            $filter = $request['filter'];
            if ($clinic_id) {
                $filter['clinic_id'] =  [
                    'type' => 'eq',
                    'value' => $clinic_id
                ];
            }
            $request->merge([
                'filter' => $filter
            ]);
            $request['with'] = [
                'clinics' => 'id,clinic_name',
                'users' => 'id,username'
            ];
            $this->scheduleService->setRequest($request);
            $this->scheduleService->findAll();
            $this->response['success']  = true;
            $data = $this->scheduleService->getRows();
            // $data = $this->scheduleService->collectionRows();
            // dd($data);
            $permissionPage = $this->getPermissionList();
            $this->response['tbody'] = view('schedule::schedule.api.list',compact('data','permissionPage','clinic_id'))->render();
            $this->response['tfoot'] = $this->scheduleService->paginationCustom();
            $this->response['headerAction'] = view('schedule::schedule.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function create(Request $request)
    {
        $role = $this->getUserRole();
        switch($role->id) {
            case 6:
                $clinic_id = auth()->user()->nurse->clinic_id;
                break;
            default:
                $clinic_id = null;
                break;
        }
        $this->response['success']  = true;
        $this->response['data']     = [];
        $id = null;
        $data = [];
        $district_id = null;
        $clinics = $this->scheduleService->allClinics();
        if($clinic_id){
            $clinics = $clinics->where('id',$clinic_id);
            $district_id = $clinics->value('district_id');
        }
        $districts = $this->scheduleService->allDistricts();
        if($district_id){
            $districts = $districts->where('id',$district_id);
        }
        $list = [
            'districts' => $districts,
            'clinics' => $clinics,
            'doctors' => $this->userService->allUserWithRole('Doctor'),
            'weekday_list' => config('schedule.weekdays'),
            'time_list' => config('schedule.times')
        ];
        // dd($user);//->getRoleNames()->toArray()
        $this->response['form'] = view('schedule::schedule.api.addEdit',compact('id','data','list'))->render();
        $this->response['list'] = $list;
        $this->response['calendar'] = true;
        return response()->json($this->response);
    }
    public function calendar(Request $request)
    {
        $this->response['success']  = true;
        $visit_price = $request->doctor_id ? (Doctor::where('user_id',$request->doctor_id)->value('visit_price') ?? '') : '';
        $doctor_type = $request->doctor_id ? (Doctor::where('user_id',$request->doctor_id)->value('doctor_type') ?? '') : '';

        $this->response['data']     = [
            'show_visit_price' => $visit_price,
            'visit_price' => $doctor_type == 2 ? (DoctorType::where('id',$doctor_type)->value('price') ?? 0) : $visit_price
        ];
        $list = [
            'holidays' => $this->scheduleService->getHolidays($request->clinic_id),
            'schedules' => $request->doctor_id && $request->clinic_id ? $this->scheduleService->allSchedules($request->doctor_id,null,$request->clinic_id) : [],
        ];
        // dd($this->response['data']);
        $dates = [];
        foreach ($list['holidays'] as $key => $row) {
            $arr = [
                'title' => '<span class="clinic_holiday">Closed</span>',
                'start' => $row,
                'url' => '',
            ];
            array_push($dates,$arr);
        }
        foreach ($list['schedules'] as $key => $row) {
            $arr = [
                'title' => '<span class="clinic_name_classs"></span> <span class="date_time_class">'.$row->s_time.' To '.$row->e_time.'</span>',
                'start' => $row->date,
                'url' => '',
            ];
            array_push($dates,$arr);
        }
        // dd($dates);
        $this->response['calendar'] = true;
        $this->response['dates'] = $dates;
        return response()->json($this->response);
    }
    public function add(ScheduleRequest $request)
    {
        try {
            $request->validated();
            $timeSchedule = [];
            $this->response['errors'] = [];
            // error handling for start and end time
            foreach ($request->s_times as $key => $value) {
                if (!isset($value) || $value == null) {
                    $this->response['errors']['s_times'] = ['Please select start time'];
                }
                if (!isset($request->e_times[$key]) || $request->e_times[$key] == null) {
                    $this->response['errors']['e_times'] = ['Please select end time'];
                }
                if(isset($value) && isset($request->e_times[$key])){
                    $s_time = explode("-",$value);
                    $e_time = explode("-",$request->e_times[$key]);
                    $timeSchedule[$s_time[1]]['s_time'] = $s_time[0];
                    $timeSchedule[$e_time[1]]['e_time'] = $e_time[0];
                }
            }
            // error handling for weekdays
            if(isset($request->weekdays)){
                foreach ($request->weekdays as $key1 => $day) {
                    $weekday = explode("-",$day);
                    $timeSchedule[$weekday[1]]['weekday'][$key1] = $weekday[0];
                }
            }
            foreach ($timeSchedule as $key => $err) {
                if (!isset($err['weekday'])) {
                    $this->response['errors']['weekdays'] = ['Please select atleast one Day'];
                    break;
                }
            }
            if (count($this->response['errors']) > 0) {
                $this->response['massage']  = 'Validation Error';
                return response()->json($this->response,422);
            }
            $schedule_dt = [];
            for ($i = strtotime($request->start_date); $i <= strtotime($request->end_date); $i = strtotime('+1 day', $i)) {
                $date = date('Y-m-d', $i);
                $date_day = date('l', strtotime($date));
                foreach ($timeSchedule as $key => $row) {
                    if (in_array($date_day, $row['weekday'])) {
                        array_push($schedule_dt,$date);
                    }
                }
            }
            $holiday_list = DB::table('holidays')->whereIn('date', $schedule_dt)->pluck('date')->toArray();
            
            // dd($holiday_list);
            $startDate = $request->start_date; // Start date
            $endDate = $request->end_date;   // End date
            $selectedDay = [];
            $cnt_check_slot = 0;
            for ($i = strtotime($startDate); $i <= strtotime($endDate); $i = strtotime('+1 day', $i)) {
                $date = date('Y-m-d', $i);
                $date_day = date('l', strtotime($date));
                foreach ($timeSchedule as $key => $row) {
                    $s_time = Carbon::createFromFormat('h:i a', config('schedule.times.'.$row['s_time']));
                    $e_time = Carbon::createFromFormat('h:i a', config('schedule.times.'.$row['e_time']));
                    $check_schedule_st = DB::table('time_schedules')->where([
                        'doctor_id' => $request->doctor_id,
                        'date' => $date,
                        'status' => 1
                    ])
                    ->select('date','s_time','e_time')->get();
                    $check_time = false;
                    foreach ($check_schedule_st as $checkTime) {
                        $c_s_time = Carbon::createFromFormat('h:i a', $checkTime->s_time);
                        $c_e_time = Carbon::createFromFormat('h:i a', $checkTime->e_time);
                        if ($c_s_time > $s_time && $c_s_time < $e_time) {
                            $check_time = true;
                            $cnt_check_slot++;
                        }
                        elseif ($c_e_time > $s_time && $c_s_time < $e_time) {
                            $check_time = true;
                            $cnt_check_slot++;
                        }
                    }
                    // dd($check_time,$s_time,$e_time);
                    if (in_array($date_day, $row['weekday']) && !in_array($date, $holiday_list) && !$check_time) {
                        $duration = $row['e_time'] - $row['s_time'];
                        // dd($date,$date_day,config('schedule.times.'.$row['s_time']),config('schedule.times.'.$row['e_time']),$duration);
                        $request['created_by'] = $this->createdBy();
                        $request['status'] = 1;
                        $request['weekday'] = $date_day;
                        $request['date'] = $date;
                        $request['s_time'] = config('schedule.times.'.$row['s_time']);
                        $request['e_time'] = config('schedule.times.'.$row['e_time']);
                        $request['duration'] = $duration;
                        $this->scheduleService->setRequest($request->except('start_date','end_date','s_times','e_times','weekdays'));
                        $this->scheduleService->add();
                    }
                }
            }
            // dd($request->all());
            $this->response['success']  = true;
            if ($cnt_check_slot > 0) {
                $message = 'Some time slots were already scheduled on selected dates, so those were skipped. Remaining slots created successfully.';
            }
            elseif (count($holiday_list) > 0) {
                $message = 'Schedule has been created successfully. Holidays have been excluded ('. implode(",",$holiday_list) .').';
            }
            else {
                $message  = 'Schedule has been created successfully!';
            }
            $this->response['message'] = $message;
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function updateTimeInOut($id,Request $request)
    {
        try {
            $speciality = $this->scheduleService->findById($id);
            if (!$speciality) {
                $this->response['success']  = false;
                $this->response['message']  = 'Speciality not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $curTime = $this->getCurrentDateTime();
            if ($request->is_time == 1) {
                $request->merge([
                    'time_in' => $curTime
                ]);
            }
            else{
                $request->merge([
                    'time_out' => $curTime
                ]);
            }

            $this->scheduleService->setRequest($request);
            $this->scheduleService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'Schedule has been status updated successfully!';
            $this->response['data']     = [
                'id' => $id,
                'is_time' => $request->is_time,
                'curTime' => $curTime
            ];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function delete($id,Request $request)
    {
        try {
            $schedule = $this->scheduleService->findById($id);
            if (!$schedule) {
                $this->response['success']  = false;
                $this->response['message']  = 'schedule not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->scheduleService->setRequest($request);
            $this->scheduleService->update();
            $this->response['success']  = true;
            $this->response['message']  = 'schedule has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
                return response()->json($this->response);
        }
    }
    public function listModify(Request $request)
    {
        try {
            $this->scheduleService->setRequest($request);
            $data = $this->scheduleService->findSchedules();
            $this->response['success']  = true;
            // dd($data[0]['id']);
            $permissionPage = $this->getPermissionList();
            $this->response['tbody'] = view('schedule::schedule.api.listModify',compact('data','permissionPage'))->render();
            // $this->response['tfoot'] = $this->scheduleService->paginationCustom();
            // $this->response['headerAction'] = view('schedule::schedule.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function modifySchedule(Request $request)
    {
        try {
            if (!isset($request->schedule_id)) {
                $this->response['success']  = false;
                $this->response['message']  = 'At least select one Schedule!';
                return response()->json($this->response);
            }
            // dd($request->all(),$request->schedule_id);
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $except = [];
            if ($request->btn == 'Active') {
                $request['status'] = 1;
                $succes_msg = 'Schedules has been Actived successfully!';
            }
            elseif ($request->btn == 'Inactive') {
                $request['status'] = 0;
                $succes_msg = 'Schedules has been Inactived successfully!';
            }
            elseif ($request->btn == 'Update') {
                if ($request->s_times == '') {
                    $this->response['success']  = false;
                    $this->response['message']  = 'Start time is required';
                    return response()->json($this->response);
                }
                if ($request->e_times == '') {
                    $this->response['success']  = false;
                    $this->response['message']  = 'End time is required';
                    return response()->json($this->response);
                }
                // dd($request->all());
                $request['s_time'] = $request->s_times;
                $request['e_time'] = $request->e_times;
                $succes_msg = 'Schedules has been Updated successfully!';
            }
            // dd($request->except(implode(",",$except)));
            foreach ($request->schedule_id as $key => $row) {
                $schedule = $this->scheduleService->findById($row);
                if (!$schedule) {
                    $this->response['success']  = false;
                    $this->response['message']  = 'schedule not found!';
                    return response()->json($this->response);
                }
                // dd($schedule->date,$schedule->s_time,$schedule->e_time,$schedule->clinics->clinic_name);
                if (count($schedule->appointments) > 0) {
                    $this->response['success']  = false;
                    $this->response['message']  = 'The doctor already has an appointment for the '.$schedule->date.' time slot from '.$schedule->s_time.' to '.$schedule->e_time.' at the '.$schedule->clinics->clinic_name.' clinic. Therefore, this schedule cannot be deactivated.';
                    return response()->json($this->response);
                }
            }
            foreach ($request->schedule_id as $key => $row) {
                $schedule = $this->scheduleService->findById($row);
                //Update Active Inactive
                $this->scheduleService->setRequest($request->except('schedule_id','s_times','e_times','btn'));
                $this->scheduleService->update();
            }
            $this->response['success']  = true;
            $this->response['message']  = $succes_msg;
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function export(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('schedule.exportLink',['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function exportLink()
    {
        $request = request('req');
        $request['with'] = [
            'clinics' => 'id,clinic_name',
            'users' => 'id,username'
        ];
        $request['pagination'] = [];
        // dd($request);
        $this->scheduleService->setRequest($request);
        $entity = $this->scheduleService->findAll()->entity;
        
        $header = [
            'SL No',
            'Doctor',
            'Doctor Type',
            'Clinic',
            'Date',
            'Scheduled Time',
            'Time In',
            'Time Out',
        ];
        $body = [
            '{id}',
            '{users.username}',
            '{users.schedule_doctors.doctor_type.title}',
            '{clinics.clinic_name}',
            '{date}',
            '{s_time} to {e_time}',
            '{time_in}',
            '{time_out}'
        ];
        $fileName = 'doctor-appointment-schedule.csv';
        $exportCSVService = new ExportCSVService($entity,$header,$body,$fileName);
        return $exportCSVService->export();
        // // dd(request('req'));
        // $request = request('req');
        // $request['with'] = [
        //     'clinics' => 'id,clinic_name',
        //     'users' => 'id,username'
        // ];
        // $request['pagination'] = [];
        // // dd($request);
        // $this->scheduleService->setRequest($request);
        // $data = $this->scheduleService->findAll()->entity->get()->toArray();
        // // dd($data);
        // $data = Excel::download(new ScheduleExport($data), 'doctor-appointment-schedule.xlsx');
        // return $data;
    }
    public function dataMigrate()
    {
        $increment_migration = DB::table('temp_increment_migrations')->where('table_name','time_schedules')->select('count','limit')->first();
        $all_content = $this->dbConnectionInstance->table('time_schedule')->count();
        // dd($all_content);
        $chunkSize = $increment_migration->limit;
        $chunkCount = ceil($all_content / $chunkSize);

        // for ($i = 0; $i <= $chunkCount; $i++) {
            $startId = $increment_migration->count * $chunkSize;//$i * $chunkSize;
            if ($startId > $all_content) {
                dd("completed migration");
            }
            $this->queryBuilder = $this->dbConnectionInstance->table('time_schedule')
                ->orderBy('id')
                ->offset($startId)
                ->limit($all_content)->get();
            $chunk = $this->queryBuilder->chunk($chunkSize);
            foreach ($chunk as $chunk_contents) {
                foreach ($chunk_contents as $content) {
                    $doctor_id = $this->dbConnectionInstance->table('doctor')->where('id', $content->doctor)->value('ion_user_id');
                    $timestamp = date('Y-m-d H:i:s');
                    switch ($content->status) {
                        case '0':
                            $status = 1;
                            break;
                        case '1':
                            $status = 0;
                            break;
                        default:
                            $status = 0;
                            break;
                    }
                    $doctor_type = $this->dbConnectionInstance->table('doctor')->where('id', $content->doctor)->value('doctor_type');
                    $laravel_visit_price = $doctor_type == 1 ? (DB::table('doctor_types')->where('id',2)->value('price') ?? 0) : $content->visit_price;// for specility doctor
                    $contentInsert[] = [
                        'id' => $content->id,
                        'doctor_id' => $doctor_id,
                        'weekday' => $content->weekday,
                        'date' => $content->date,
                        's_time' => $content->s_time,
                        'e_time' => $content->e_time,
                        's_time_key' => $content->s_time_key,
                        'duration' => $content->duration,
                        'clinic_id' => $content->clinic,
                        'visit_price' => $laravel_visit_price,
                        'show_visit_price' => $content->visit_price,
                        'time_in' => $content->time_in,
                        'time_out' => $content->time_out,
                        'is_time' => $content->is_time,
                        'status' => $status,
                        'created_by' => 1,
                        // 'modified_by' => 1,
                        'deleted_by' => $content->status == 3 ? 1 : null,
                        'created_at' => $timestamp,
                        'updated_at' => $timestamp,
                        'deleted_at' => $content->status == 3 ? $timestamp : null
                    ];
                    // dd($contentInsert);
                }
                if (!empty($contentInsert)) {
                    $this->scheduleService->dataMigrate($contentInsert);
                }
            }
            dd("Loop iteration completed");
        // }
    }
}
