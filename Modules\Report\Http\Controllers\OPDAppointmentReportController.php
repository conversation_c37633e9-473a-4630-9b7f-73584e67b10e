<?php

namespace Modules\Report\Http\Controllers;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\OPDAppointmentService;
use App\Exports\OpdAppointmentReportExport;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Schedule\Services\ScheduleService;
use Modules\Users\Services\UserService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use DB;
use PDF;
class OPDAppointmentReportController extends Controller
{
    private $opdappointmentService;
    private $scheduleService;
    private $userService;
    public function __construct(OPDAppointmentService $opdappointmentService, ScheduleService $scheduleService, UserService $userService)
    {
        $this->opdappointmentService = $opdappointmentService;
        $this->scheduleService = $scheduleService;
        $this->userService = $userService;
    }
    public function index(Request $request)
    {
        $data = [
            'clinic_list' => $this->opdappointmentService->allClinics()->toArray(),
        ];
        return view('report::opdappointment.index', compact('data'));
    }
    public function opdAppointment(Request $request)
    {
        try {
            $request['join'] = [

                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $request['with'] = [
                'patientWithMembership' => 'id,name',
                'clinics' => 'id,clinic_name',
                'userDoctors' => 'id,username',
            ];
            array_push(
                $this->opdappointmentService->columns,
                'patients.name as patient_name',
            );
            $this->opdappointmentService->setRequest($request);
            $this->opdappointmentService->findAll();
            $this->response['success']  = true;
            $data = $this->opdappointmentService->getRows();
            $appointmentdataSource = config('report.data_source');
            $statusLabels = config('report.appoint_status');
            $this->response['tbody'] = view('report::opdappointment.api.list', compact('data', 'statusLabels', 'appointmentdataSource'))->render();
            $this->response['tfoot'] = $this->opdappointmentService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function exportOpdappointment(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.opdappointment.exportLink', ['req' => $request->all()]);

            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function opdAppointmentExport()
    {
        $request = request('req');

        $request['join'] = [

            'patients' => [
                'reletion' => [
                    'prefix' => 'patient_id',
                    'suffix' => 'id'
                ]
            ]
        ];
        $request['with'] = [
            'patientWithMembership' => 'id,name',
            'clinics' => 'id,clinic_name',
            'userDoctors' => 'id,username',
        ];
        array_push(
            $this->opdappointmentService->columns,
            'patients.name as patient_name',
        );
        $request['pagination'] = [];
        $this->opdappointmentService->setRequest($request);
        $data = $this->opdappointmentService->findAll()->entity->get()->toArray();
        $data = Excel::download(new OpdAppointmentReportExport($data), 'Appointment-Report.xlsx');
        return $data;
    }
}
