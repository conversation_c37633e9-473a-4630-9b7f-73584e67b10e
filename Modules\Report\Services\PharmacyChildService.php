<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\OrderChildMedicine;
use DB;

class PharmacyChildService extends ApplicationDefaultService
{
    public $entity;

    public $columns = [
        'id',
        'order_id',
        'medicine_id',
        'quantity',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'order_id',
        'medicine_id',
        'quantity',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(OrderChildMedicine $entity) {
        $this->entity =$entity;
    }
    public function allOrderChildMedicines(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            $contentInsert = collect($contentInsert)->map(function ($item) {
                $existingId = OrderChildMedicine::where('order_id',$item['order_id'])
                    ->where('medicine_id', $item['medicine_id'])
                    ->where('quantity', $item['quantity'])
                    ->value('id') ?? null;
                if ($existingId) {
                    $item['id'] = $existingId;
                }
                return $item;
            })->toArray();
            OrderChildMedicine::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
        });
    }
}
