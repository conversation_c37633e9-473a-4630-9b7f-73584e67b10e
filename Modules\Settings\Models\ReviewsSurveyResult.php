<?php

namespace Modules\Settings\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReviewsSurveyResult extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id',
        'survey_id',
        'url_slag',
        'patient_id',
        'source_type',
        'source_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function patient(): BelongsTo
    {
        return $this->belongsTo(Patient::class, 'patient_id', 'id')->with('parent');
    }
    public function reviewsSurveyMaster(): BelongsTo
    {
        return $this->belongsTo(ReviewsSurveyMaster::class, 'survey_id', 'id');
    }
    public function reviewsSurveyResultChild(): HasMany
    {
        return $this->hasMany(ReviewsSurveyResultChild::class, 'result_id', 'id');
    }
}
