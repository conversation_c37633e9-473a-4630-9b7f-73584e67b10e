<?php $__env->startSection('title'); ?>
    <?php echo e(config('appointment.title', 'Appointment')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title"><?php echo e($vital_id ? 'Update' : 'Add'); ?> Patient Vitals</h4>
                </div>
                <div class="header-action">
                    <a href="<?php echo e(route('appointment.index',[($stat == 'all' ? $stat : 'today')])); ?>"
                        class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">
                        Back
                    </a>
                </div>
            </div>
            <div class="card-body placeholder-glow" id="data-add-edit">
                <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [3, 3, 3, 3, 3, 3, 3, 12]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(route('appointment.index',[($stat == 'all' ? $stat : 'today')])); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('appointment.url') . 'createVital/' . $id : ''); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let filter = {
                "vital_id": "<?php echo e($vital_id ? $vital_id : ''); ?>"
            };
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function bmiCalculate() {
            const weight = parseFloat(document.getElementById('weight').value);
            const height = parseFloat(document.getElementById('height').value);

            if (isNaN(weight) || isNaN(height) || weight <= 0 || height <= 0) {
                document.getElementById('bmi').value = 0;
                return;
            }

            // Calculate BMI
            const bmi = (weight / (height * height)) * 10000;
            document.getElementById('bmi').value = bmi.toFixed(2);
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/addVital.blade.php ENDPATH**/ ?>