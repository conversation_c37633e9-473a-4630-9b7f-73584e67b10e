@if (count($list['patients']) > 0)
    @foreach ($list['patients'] as $row)
        <tr>
            <td>{{ $row->name }}</td>

            <td>{{ $row->sex }}</td>
            <td class="abwrap">
                @php
                    $service = '';
                @endphp
                @if (count($row->membershipRegistrations) > 0)
                    @foreach ($row->membershipRegistrations as $row2)
                        @if ($row2->is_renewal == 1 && in_array($row2->status, [3, 4, 6]))
                            <div>{{ $row2->memberships->name }}<br>
                                @if ($row2->status == 3)
                                    @php
                                        $service = $row2->memberships->name;
                                    @endphp
                                    Expiry Date: {{ $row2->end_date }}
                                @elseif ($row2->status == 4)
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Expired</span>
                                @elseif ($row2->status == 6)
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Upcoming</span>
                                @endif
                            </div>
                        @endif
                    @endforeach
                @endif
            </td>
            <td>
                <div class="d-flex flex-nowrap gap-1 justify-content-center">
                    @if (array_intersect(['create_with_phlebo_diagnostic'], $permissionPage))
                        <button class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="selectPatient({{ $row->id }},{{ $phone }},'{{ $service }}','With Phlebo')">Select</button>
                    @else
                        <button class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="selectPatient({{ $row->id }},{{ $phone }},'{{ $service }}','All')">Select</button>
                    @endif
                    
                </div>
            </td>
        </tr>
    @endforeach
@else
    <tr>
        <td colspan="5" class="border-0">
            No patients found
        </td>
    </tr>
@endif
