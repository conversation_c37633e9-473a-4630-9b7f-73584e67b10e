<form class="clearfix" method="post"
    action="<?php echo e($id ? config('users.url') . 'update/' . $id : config('users.url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" name="step" id="step" value="<?php echo e($id ? 2 : 1); ?>">
    <div class="row" id="step-1">
        <div class="form-group col-md-4 selct-modal1">
            <label for="exampleInputEmail1" class="form-label fw-bold">Role:</label>
            <?php if(!$id): ?>
                <select id="role_name" name="role_name" class="form-select select2-multpl-custom1" data-style="py-0">
                    <?php if(count($list['roles']) == 1): ?>
                        <option value="<?php echo e($list['roles'][0]['name']); ?>" selected><?php echo e($list['roles'][0]['name']); ?></option>
                    <?php else: ?>
                        <option value="">Select Role</option>
                        <?php $__currentLoopData = $list['roles']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($row['name']); ?>"><?php echo e($row['name']); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </select>
            <?php else: ?>
                <input type="text" class="form-control form-control-sm" name="role_name"
                    value="<?php echo e($id ? $data['role_name'][0] : ''); ?>" readonly>
            <?php endif; ?>
        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="fname">Name:</label>
            <div class="input-group input-group-sm">
                <input type="text" class="form-control form-control-sm" name="username"
                    value="<?php echo e($id ? $data['user']->username : ''); ?>" placeholder="">
            </div>

        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="add1">Email:</label>
            <input type="email" class="form-control form-control-sm" name="email"
                value="<?php echo e($id ? $data['user']->email : ''); ?>" placeholder="">
        </div>
        <?php if(!$id): ?>
            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Password</label>
                <input type="password" class="form-control form-control-sm" name="password" value=""
                    placeholder="">
            </div>
        <?php endif; ?>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="cname">Phone</label>
            <input type="text" class="form-control form-control-sm" name="phone"
                value="<?php echo e($id ? $data['user']->phone : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="cname">Image Upload</label>
            <div class=" input-group input-group-sm d-flex fs-4">
                <input type="file" class="form-control form-control-sm bg-light text-gray h7" name="user_profile_image"
                    value="" onchange="previewImage(this, 'previewImage')">
                <div id="previewImage" class="d-inline-flex align-items-center justify-content-center bg-light">
                     <?php if($id && $data['user']->profile_image != ''): ?>
                        <img src="<?php echo e(Storage::disk('gcs')->url($data['user']->profile_image)); ?>" alt="Preview"
                            style="height: 36px;width: auto;">
                    <?php endif; ?>
                </div>
            </div>
            <?php if($id && $data['user']->profile_image != ''): ?>
                <span class="h9"><?php echo e(basename($data['user']->profile_image)); ?></span>
            <?php endif; ?>
        </div>
        <?php if(!$id): ?>
            <div class="form-group col-md-12">
                <button type="button" id="next" class="btn btn-primary text-white">Next</button>
            </div>
        <?php endif; ?>
    </div>
    <div id="step-2" style="<?php echo e($id ? '' : 'display:none'); ?>">
        
        <div class="row all-role" id="doctor"
            style="<?php echo e($id ? ($data['prefix'] == 'doctor' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Doctor Type:</label>
                <select id="doctor_doctor_type" name="doctor_doctor_type" class="select2-multpl-custom1 form-select"
                    data-style="py-0" onchange="doctorTypePrice(this.value)">
                    <option value="">Select Type</option>
                    <?php $__currentLoopData = $list['doctor_type_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>" data-price="<?php echo e($row['price']); ?>"
                            <?php echo e($id ? (($data['doctor']->doctor_type ?? '') == $row['id'] ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['title']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="form-group col-md-12">
                <label class="form-label fw-bold" for="add2">Address</label>
                <textarea class="form-control form-control-sm" id="doctor_address" name="doctor_address" value="" rows="1"
                    cols="10"><?php echo e($id ? $data['doctor']->address ?? '' : ''); ?></textarea>
            </div>

            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Visit Price</label>
                <input type="text" class="form-control form-control-sm" name="doctor_visit_price" id="doctor_visit_price"
                    value="<?php echo e($id ? $data['doctor']->visit_price ?? '' : ''); ?>" placeholder="">
            </div>
            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Field of Expertise</label>
                <input type="text" class="form-control form-control-sm" name="doctor_profile"
                    value="<?php echo e($id ? $data['doctor']->profile ?? '' : ''); ?>" placeholder="">
            </div>

            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Key Procedure Performed</label>
                <input type="text" class="form-control form-control-sm" name="doctor_key_procedure_performed"
                    value="<?php echo e($id ? $data['doctor']->key_procedure_performed ?? '' : ''); ?>" placeholder="">
            </div>

            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Full Qualification</label>
                <input type="text" class="form-control form-control-sm" name="doctor_degree"
                    value="<?php echo e($id ? $data['doctor']->degree ?? '' : ''); ?>" placeholder="">
            </div>
            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Speciality:</label>
                <select id="doctor_speciality" name="doctor_speciality" class="select2-multpl-custom1 form-select"
                    data-style="py-0">
                    <option value="">Select Type</option>
                    <?php $__currentLoopData = $list['speciality_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? (($data['doctor']->speciality ?? '') == $row['id'] ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['speciality']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <div class="form-group col-md-6">
                <label class="form-label fw-bold" for="cname">Registration No.</label>
                <input type="text" class="form-control form-control-sm" name="doctor_registration_no"
                    value="<?php echo e($id ? $data['doctor']->registration_no ?? '' : ''); ?>" placeholder="">
            </div>

            <div class="form-group col-md-6">
                <label class="form-label fw-bold">Featured Doctor:</label>
                <input class="form-check-input" value="0" type="radio" name="doctor_featured_doctor"
                    id="flexRadioDefault1"
                    <?php echo e($id ? (($data['doctor']->featured_doctor ?? '') == 0 ? 'checked' : '') : 'checked'); ?>>
                <label class="form-check-label" for="flexRadioDefault1">Yes</label>

                <input class="form-check-input" value="1" type="radio" name="doctor_featured_doctor"
                    id="flexRadioDefault2"
                    <?php echo e($id ? (($data['doctor']->featured_doctor ?? '') == 1 ? 'checked' : '') : ''); ?>>
                <label class="form-check-label" for="flexRadioDefault2">No</label>

            </div>
            <div class="form-group col-md-12">
                <label class="form-label fw-bold" for="cname">Brief Profile Information</label>
                <textarea class="form-control form-control-sm" name="doctor_description" rows="1" cols="10"><?php echo e($id ? $data['doctor']->description ?? '' : ''); ?></textarea>
            </div>
            
            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="cname">Upload E-Signature</label>
                <div class=" input-group input-group-sm d-flex fs-4">
                    <input type="file" class="form-control form-control-sm bg-light text-gray h7" name="doctor_signature"
                        value="" onchange="previewImage(this, 'previewImageDoctor')">
                    <div id="previewImageDoctor" class="d-inline-flex align-items-center justify-content-center bg-light">
                        <?php if($id && isset($data['doctor']->doctor_esign)): ?>
                            <img src="<?php echo e(Storage::disk('gcs')->url($data['doctor']->doctor_esign)); ?>" alt="Preview"
                                style="height: 36px;width: auto;">
                        <?php endif; ?>
                    </div>
                </div>
                <?php if($id && isset($data['doctor']->doctor_esign)): ?>
                    <span class="h9"><?php echo e(basename($data['doctor']->doctor_esign)); ?></span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="row all-role" id="cluster-user"
            style="<?php echo e($id ? ($data['prefix'] == 'cluster-user' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-12">
                <label class="form-label fw-bold" for="add2">Address</label>
                <textarea class="form-control form-control-sm" id="cluster_user_address" name="cluster_user_address" value=""
                    rows="1" cols="10"><?php echo e($id ? $data['clusterUser']->address ?? '' : ''); ?></textarea>
            </div>

            <div class="form-group col-md-4 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="doc_type1" name="cluster_user_clinic_id[]" class="form-select select2-multpl-custom1"
                    data-style="py-0" multiple>
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? (!empty($data['clusterUser']) && in_array($row['id'], json_decode($data['clusterUser']->clinic_id)) ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="center-manager"
            style="<?php echo e($id ? ($data['prefix'] == 'center-manager' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-4 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="center_manager_clinic_id" name="center_manager_clinic_id"
                    class="form-select select2-multpl-custom1" data-style="py-0">
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($row['id'] == ($data['centerManager']->clinic_id ?? '') ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="receptionist"
            style="<?php echo e($id ? ($data['prefix'] == 'receptionist' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-12">
                <label class="form-label fw-bold" for="add2">Address</label>
                <textarea class="form-control form-control-sm" id="receptionist_address" name="receptionist_address" value=""
                    rows="1" cols="10"><?php echo e($id ? $data['receptionist']->address ?? '' : ''); ?></textarea>
            </div>
            <div class="form-group col-md-4 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="receptionist_clinic_id" name="receptionist_clinic_id"
                    class="form-select select2-multpl-custom1" data-style="py-0">
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($row['id'] == ($data['receptionist']->clinic_id ?? '') ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="nurse"
            style="<?php echo e($id ? ($data['prefix'] == 'nurse' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-12">
                <label class="form-label fw-bold" for="add2">Address</label>
                <textarea class="form-control form-control-sm" id="nurse_address" name="nurse_address" value=""
                    rows="1" cols="10"><?php echo e($id ? $data['nurse']->address ?? '' : ''); ?></textarea>
            </div>
            <div class="form-group col-md-4 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="nurse_clinic_id" name="nurse_clinic_id" class="form-select select2-multpl-custom1"
                    data-style="py-0">
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($row['id'] == ($data['nurse']->clinic_id ?? '') ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="phlebotomist"
            style="<?php echo e($id ? ($data['prefix'] == 'phlebotomist' ? '' : 'display:none') : ''); ?>">
            <div class="col-md-12 my-2">
                <h6 class="h6 fw-bold">Personal Details</h6>
            </div>
            <div class="form-group col-md-3 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="phlebotomist_clinic_id" name="phlebotomist_clinic_id" class="form-select select2-multpl-custom1"
                    data-style="py-0">
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($row['id'] == ($data['phlebotomist']->clinic_id ?? '') ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Gender *</label>

                <select id="phlebotomist_gender" name="phlebotomist_gender" class="form-control form-control-sm">
                    <option value="M"
                        <?php echo e($id ? (($data['phlebotomist']->gender ?? '') == 'M' ? 'selected' : '') : ''); ?>>Male</option>
                    <option value="F"
                        <?php echo e($id ? (($data['phlebotomist']->gender ?? '') == 'F' ? 'selected' : '') : ''); ?>>Female
                    </option>
                    <option value="O"
                        <?php echo e($id ? (($data['phlebotomist']->gender ?? '') == 'O' ? 'selected' : '') : ''); ?>>Others
                    </option>
                </select>
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Date of Birth</label>
                <input type="date" class="form-control form-control-sm" id="phlebotomist_dob"
                    name="phlebotomist_dob" value="<?php echo e($id ? $data['phlebotomist']->dob ?? '' : ''); ?>">
            </div>
            <div class="form-group col-md-12">
                <label for="exampleInputAddress" class="fw-bold">Address *</label>
                <textarea class="form-control form-control-sm" id="phlebotomist_address" name="phlebotomist_address" value=""
                    rows="1" cols="10"><?php echo e($id ? $data['phlebotomist']->address ?? '' : ''); ?></textarea>
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">City *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_city"
                    id="phlebotomist_city" value="<?php echo e($id ? $data['phlebotomist']->city ?? '' : ''); ?>">
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Pincode *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_pincode"
                    id="phlebotomist_pincode" value="<?php echo e($id ? $data['phlebotomist']->pincode ?? '' : ''); ?>">
            </div>
            <div class="col-md-12 my-2">
                <h5 class="h6 fw-bold">Vehicle Details</h5>
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Driving License *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_dl_no"
                    id="phlebotomist_dl_no" value="<?php echo e($id ? $data['phlebotomist']->dl_no ?? '' : ''); ?>">
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Vehicle Number *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_vehicle_no"
                    id="phlebotomist_vehicle_no" value="<?php echo e($id ? $data['phlebotomist']->vehicle_no ?? '' : ''); ?>">
            </div>
            <div class="col-md-12 my-2">
                <h6 class="h6 fw-bold">Work Area Details</h6>
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">City *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_working_city"
                    id="phlebotomist_working_city"
                    value="<?php echo e($id ? $data['phlebotomist']->working_city ?? '' : ''); ?>">
            </div>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1" class="fw-bold">Working Area Pincode *</label>
                <input type="text" class="form-control form-control-sm" name="phlebotomist_working_city_pincode"
                    id="phlebotomist_working_city_pincode"
                    value="<?php echo e($id ? $data['phlebotomist']->working_city_pincode ?? '' : ''); ?>">
            </div>
        </div>
        
        <div class="row all-role" id="pharmacist"
            style="<?php echo e($id ? ($data['prefix'] == 'pharmacist' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-4 selct-modal1">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic:</label>
                <select id="pharmacist_clinic_id" name="pharmacist_clinic_id"
                    class="form-select select2-multpl-custom1" data-style="py-0">
                    <option value="">Select Clinic</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($row['id'] == ($data['pharmacist']->clinic_id ?? '') ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="agent"
            style="<?php echo e($id ? ($data['prefix'] == 'agent' ? '' : 'display:none') : ''); ?>">
            <div class="form-group col-md-4">
                <label class="form-label fw-bold" for="add2">Address</label>
                <textarea class="form-control form-control-sm" id="agent_address" name="agent_address" value=""
                    rows="1" cols="10"><?php echo e($id ? $data['agent']->address ?? '' : ''); ?></textarea>
            </div>
            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Designation:</label>
                <select id="agent_designation" name="agent_designation" class="select2-multpl-custom1 form-select"
                    data-style="py-0">
                    <option value="">Select Type</option>
                    <?php $__currentLoopData = $list['designation_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"
                            <?php echo e($id ? (($data['agent']->designation ?? '') == $key ? 'selected' : '') : ''); ?>>
                            <?php echo e($row); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Team:</label>
                <select id="agent_team" name="agent_team" class="select2-multpl-custom1 form-select"
                    data-style="py-0">
                    <option value="">Select Type</option>
                    <?php $__currentLoopData = $list['team_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"
                            <?php echo e($id ? (($data['agent']->team ?? '') == $key ? 'selected' : '') : ''); ?>>
                            <?php echo e($row); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        
        <div class="row all-role" id="other"
            style="<?php echo e($id ? ($data['prefix'] == 'other' ? '' : 'display:none') : ''); ?>">

        </div>
        <div class="row">
            <div class="form-group col-md-12">
                <button type="submit" class="btn btn-primary text-white">Submit</button>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Users\resources/views/user/api/addEdit.blade.php ENDPATH**/ ?>