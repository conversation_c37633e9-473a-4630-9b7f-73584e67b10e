<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Report\Http\Controllers\ReportController;
use Modules\Report\Http\Controllers\MembershipReportController;
use Modules\Report\Http\Controllers\OPDAppointmentReportController;
use Mo<PERSON><PERSON>\Report\Http\Controllers\SchedulewiseDoctorReportController;
use Mo<PERSON>les\Report\Http\Controllers\MedicineOrderReportController;
use Modules\Report\Http\Controllers\LeadGenerationReportController;
use Modules\Report\Http\Controllers\DiagnosticReportController;
use Modules\Report\Http\Controllers\SampleCollectionReportController;
/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:api'])->prefix('report')->group(function () {
    Route::middleware(['permission:view_otc_utilization'])->post('/otcUtilization', [ReportController::class, 'otcUtilization']);
    Route::middleware(['permission:view_otc_utilization'])->post('/exportOtcUtilization', [ReportController::class, 'exportOtcUtilization']);

    //Membership
    Route::middleware(['permission:view_membership_report'])->post('/membership', [MembershipReportController::class, 'membership']);
    Route::middleware(['permission:view_membership_report'])->post('/exportMembership', [MembershipReportController::class, 'exportMembership']);

    //opdAppointment


    Route::middleware(['permission:view_appointment_report'])->post('/opdappointment', [OPDAppointmentReportController::class, 'opdAppointment']);
    Route::middleware(['permission:view_appointment_report'])->post('/opdappointmentexport', [OPDAppointmentReportController::class, 'opdAppointmentExport']);


    //schedulewisedoctor
    Route::middleware(['permission:view_schedulewisedoctor_report'])->post('/schedulewisedoctor', [SchedulewiseDoctorReportController::class, 'scheduleWiseDoctor']);
    Route::middleware(['permission:view_schedulewisedoctor_report'])->post('/schedulewisedoctorexport', [SchedulewiseDoctorReportController::class, 'scheduleWiseDoctorExport']);

    //medicineorderReport
    Route::middleware(['permission:view_medicineorder_report'])->post('/medicineorder', [MedicineOrderReportController::class, 'medicineOrder']);
    Route::middleware(['permission:view_medicineorder_report'])->post('/medicineorderexport', [MedicineOrderReportController::class, 'MedicineOrderExport']);

    //leadgeneration
    Route::middleware(['permission:lead_generation_report'])->post('/leadgeneration', [LeadGenerationReportController::class, 'LeadGeneration']);
    #Route::middleware(['permission:lead_generation_report'])->post('/exportLeadgeneration', [ReportController::class, 'exportLeadgeneration']);

    //diagnostic
    Route::middleware(['permission:view_diagnostic_report'])->post('/diagnostic', [DiagnosticReportController::class, 'diagnostic']);
    Route::middleware(['permission:view_diagnostic_report'])->post('/diagnosticreportexport', [DiagnosticReportController::class, 'diagnosticReportExport']);

    //diagnostic investigation wise
    Route::middleware(['permission:view_diagnostic_report'])->post('/diagnosticinvestigationwise', [DiagnosticReportController::class, 'diagnosticInvestigationWise']);
    Route::middleware(['permission:view_diagnostic_report'])->post('/diagnosticinvestigationreportexport', [DiagnosticReportController::class, 'diagnosticInvestigationReportExport']);

    // sample collection report
    Route::middleware(['permission:view_sample_collection_report'])->post('/samplecollectionreport', [SampleCollectionReportController::class, 'sampleCollectionReport']);
    // Route::middleware(['permission:view_sample_collection_report'])->post('/samplecollectionreportexport', [SampleCollectionReportController::class, 'samplecollectionreportexport']);
    Route::middleware(['permission:view_sample_collection_report'])->post('/samplecollectionreportexport', [SampleCollectionReportController::class, 'sampleCollectionReportExport']);
});
