<?php $__env->startSection('title'); ?>
    <?php echo e(config('membership.title_otc', 'Otc Balance')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card mb-3">
                            <div class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                                <div class="header-title">
                                    <h5 class="h5 mb-0">Search Patient</h5>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row justify-content-center">
                                    <div class="form-group col-lg-6 col-md-6 col-12">
                                        <div class="row row-cols-auto align-items-center justify-content-center">
                                            <label for="exampleInputEmail1"
                                                class="fw-bold col-sm-12 form-label p-0 text-center">Search with Patient
                                                Name or Phone No</label>
                                            <div class="col-12 col-sm-12">
                                                <input type="text" class="form-control" name="search_patient"
                                                    id="search_patient" placeholder="Search">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-1 justify-content-center mb-5">
                                        <a href="<?php echo e(route('otcBalance.index')); ?>">
                                            <button type="button" class="btn btn-gray"
                                                data-bs-dismiss="modal">Close</button>
                                        </a>

                                        <button onclick="searchPatient()" type="button" name="button"
                                            class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Search</button>
                                    </div>
                                    <div class="col-lg-12 col-md-12 col-12 Table-custom-padding1 table-responsive">
                                        <table class="table table-sm " id="data-list-family" style="display: none">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Start Date</th>
                                                    <th>End Date</th>
                                                    <th>Purchased in current cycle (₹)</th>
                                                    <th>Current cycle balance (₹)</th>
                                                    <th>Saved in Current Cycle (₹)</th>
                                                    <th>Next Cycle Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                            </tbody>


                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12 col-md-12 col-12 Table-custom-padding1 table-responsive " id="data-list-invoice"
                style="display: none">

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            refreshToken();
            
            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function searchPatient() {
            let phone = $('#search_patient').val();
            if (phone == '') {
                alert('Please enter phone number');
                return false;
            }
            const validationErrors = validatePhoneNumber(phone);            
            if (Object.keys(validationErrors).length > 0) {
                alert(validationErrors.phone);
                return false;
            }
            let datajson = {
                "phone": phone
            };
            $.ajax({
                type: "POST",
                url: "<?php echo e(config('membership.url_otc') . 'listFamily'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#data-list-family').show();
                    $('#data-list-invoice').show();
                },
                success: function(data) {
                    console.log(data);
                    $('#data-list-family tbody').html(data.tbody);
                    $('#data-list-invoice').html(data.utilization);
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }
        let patient_phone = 0;

        function sendOtp(params) {
            patient_phone = $('#patient_phone').val();
            console.log(patient_phone);
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('membership.url_otc') . 'sendOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "patient_phone": patient_phone,
                    "type": "OTC Balance"
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    localStorage.setItem('device_check', data.otp_expire);
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    $('#sendOtp').hide();
                    $('#checkOtp').show();
                    $('#otpdiv').show();
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }

        function checkOTP(params) {
            let membership_id = $('#membership_id').val();
            // Retrieve data
            let device_check = localStorage.getItem('device_check');

            console.log(device_check);
            if (device_check === null) {
                alert('OTP has been sent to this device. Please verify the OTP on the same device.');
                return false;
            }
            // console.log(localStorage.getItem('device_check'));
            let otp_user = $('#otp').val();
            // console.log(otp_user,$('#otp').val());
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('membership.url_otc') . 'verifyOtp'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "patient_phone": patient_phone,
                    "type": "OTC Balance",
                    "otp": otp_user,
                    "membership_id": membership_id
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    if (data.verify == true) {
                        $('#error_msg').hide();
                        // Remove data
                        localStorage.removeItem('device_check');
                        swal({
                            title: "",
                            text: "Token generated successfully. Your token No is <strong class='text-primary fw-bold d-block h4 mt-2'>" +
                                data.token_no + "</strong>",
                            type: "success",
                            html: true,
                            showCancelButton: false,
                            confirmButtonColor: '#e04848',
                            cancelButtonText: "No, cancel it!",
                            confirmButtonText: 'Yes, Continue',
                            // closeOnConfirm: false,
                            // closeOnCancel: false
                        }, function(isConfirm) {
                            if (isConfirm) {
                                location.reload();
                            }
                        });
                    } else {
                        $('#error_msg').show();
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Membership\resources/views/otcBalance/add.blade.php ENDPATH**/ ?>