<div class="card mb-3">
    <div class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
        <div class="header-title">
            <h5 class="h5 mb-0"> Utilization of limit (<?php echo e($list['per_cycle_total']); ?>) </h5>
        </div>
    </div>
    <div class="card-body">
        <div class="Table-custom-padding1 table-responsive">
            <table class="table table-sm ">
                <thead>
                    <tr>
                        <th>Invoice Date</th>
                        <th>Invoice No</th>
                        <th>Customer Name</th>
                        <th>Document Type</th>
                        <th>Amount (₹)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(count($list['OTCUtilization']) > 0): ?>
                        <?php $__currentLoopData = $list['OTCUtilization']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($row->invoice_no); ?></td>
                                <td><?php echo e($row->invoice_date); ?></td>
                                <td><?php echo e($row->customer_name); ?></td>
                                <td><?php echo e($row->invoice_type == 'S' ? 'Sales' : 'Ruterns'); ?></td>
                                <td><?php echo e($row->pharmacy_amount); ?></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="border-0"></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="border-0">No Records Found</td>
                        </tr>
                        <tr>
                            <td colspan="5" class="border-0"></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="6">
                            <div class="row justify-content-end">
                                <div class="col-md-4">
                                    <table class="table table-sm border-top">
                                        <tbody>
                                            <tr>
                                                <td class="bg-light w-50 text-center">Total</td>
                                                <td class="text-center fw-bold py-1">
                                                    <?php echo e(number_format($list['OTCUtilization_total'], 2)); ?></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>


                            <div id="otpdiv" class="d-flex justify-content-end mb-3"
                                style="display: none !important">
                                <div class="row align-items-center" style="max-width: 330px;">

                                    <label for="exampleInputEmail1" class="col-12 col-md-3"><span class="h6">Enter
                                            Otp</span></label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control form-control-sm border-gray"
                                            id="otp" placeholder="Enter OTP" value="">
                                    </div>

                                </div>
                            </div>

                            <div class="d-flex flex-nowrap gap-1 justify-content-end">
                                <button onclick="sendOtp(this)" id="sendOtp" type="button"
                                    class="btn btn-sm btn-primary gap-1 py-2 px-3 align-items-center"
                                    <?php echo e($list['OTCUtilization_total'] >= $list['per_cycle_total'] || count($list['OTCList']) == 0 ? 'disabled' : ''); ?>>Send
                                    OTP
                                </button>
                                <button onclick="checkOTP(this)" id="checkOtp" type="button"
                                    class="btn btn-sm btn-primary gap-1 py-2 px-3 align-items-center"
                                    style="display: none !important">
                                    Verify OTP
                                </button>
                            </div>
                            <h6 class="h6 mt-3 text-primary small text-center" id="error_msg" style="display: none;">
                                Please Enter Correct OTP
                            </h6>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Membership\resources/views/otcBalance/api/listUtilization.blade.php ENDPATH**/ ?>