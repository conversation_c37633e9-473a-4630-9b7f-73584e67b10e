<?php
    $tot_bill_amount = 0;
    $tot_discount = 0;
    $tot_payment_bill_reward = 0;
    $tot_total_amount = 0;
    $tot_paid_amount = 0;
    $tot_due_amount = 0;
?>
<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        $tot_bill_amount += $row['bill_amount_sum'];
        $tot_discount += $row['discount_sum'];
        $tot_payment_bill_reward += $row['payment_bill_reward']['redeem_point_sum'] ?? 0;
        $tot_total_amount += $row['total_amount_sum'];
        $tot_paid_amount += $row['paid_amount_sum'];
        $tot_due_amount += $row['due_amount_sum'];
    ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at']))); ?></td>
        <td>
            <?php echo e($row['unique_id']); ?>

        </td>
        <td><?php echo e($row['date_of_collection']); ?></td>
        <td><?php echo e($row['clinic_name']); ?></td>
        <td>
            <?php echo e($row['patient_name']); ?>

            <br>
            <?php echo e(Helper::maskPhoneNumber($row['patient_phone'])); ?>

        </td>
        <td><?php echo e($row['bill_amount_sum']); ?></td>
        <td>
            <?php echo e($row['discount_sum']); ?>

        </td>
        <td>
            <?php echo e($row['payment_bill_reward']['redeem_point_sum'] ?? 0); ?>

        </td>
        <td>
            <?php echo e($row['total_amount_sum']); ?>

        </td>
        <td>
            <?php echo e($row['paid_amount_sum']); ?>

        </td>
        <td>
            <?php echo e($row['due_amount_sum']); ?>

        </td>
        <td>
            <?php echo e($row['created_by']['username'] ?? ''); ?>

        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<tr>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_bill_amount); ?></span>
    </th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_discount); ?></span>
    </th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_payment_bill_reward); ?></span>
    </th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_total_amount); ?></span>
    </th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_paid_amount); ?></span>
    </th>
    <th>
        <span class="dt-column-title">₹ <?php echo e($tot_due_amount); ?></span>
    </th>
    <th></th>
</tr>
<?php if($clinic_id): ?>
    <script>
        // let user_clinic_id = "<?php echo e($clinic_id); ?>";
        $('#clinic_id').val(String("<?php echo e($clinic_id); ?>")).trigger('change.select2');
        $('#clinic_id').prop('disabled', true);
    </script>
<?php endif; ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/ledger/api/list.blade.php ENDPATH**/ ?>