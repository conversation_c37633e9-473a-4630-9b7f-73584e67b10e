<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Speciality extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'specialitys';

    protected $fillable = [
        'id',
        'speciality',
        'speciality_slug',
        'speciality_image',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function doctors(): HasMany
    {
        return $this->hasMany(Doctor::class, 'speciality', 'id');
    }
}
