<?php

namespace Modules\Report\Http\Controllers;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\LeadGenerationReportService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use App\Exports\medicineOrderReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class LeadGenerationReportController extends Controller
{
    private $leadGenerationReportService;
   
    public function __construct(LeadGenerationReportService $leadGenerationReportService)
    {
        $this->leadGenerationReportService = $leadGenerationReportService;
    }
    public function indexLeadGeneration(Request $request)
    {
        $data = [
            //'clinic_list' => $this->leadGenerationReportService->allClinics()->toArray(),
            'data_source_list' => config('report.lead_datasource_list'),
        ];
        //  dd($data);
        return view('report::leadgeneration.index', compact('data'));
    }

    public function LeadGeneration(Request $request)
    {
        try {
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ]
            
            ];
            $request['with'] = [
                'createdBy' => 'id,username',
            ];
            $filter = $request['filter'];
            array_push($this->leadGenerationReportService->columns, 'patients.name as patient_name', 'patients.age as patient_age', 'patients.sex as patient_sex');
            $this->leadGenerationReportService->setRequest($request);
            $this->leadGenerationReportService->findAll();
            $this->response['success']  = true;
            $data = $this->leadGenerationReportService->getRows();
            $status_list = Arr::except(config('report.status_list'), []);
            $this->response['tbody'] = view('report::leadgeneration.api.list', compact('data', 'status_list'))->render();
            $this->response['tfoot'] = $this->leadGenerationReportService->paginationCustom();

            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function medicineorderexport(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.medicineorder.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function medicineorderexportLink()
    {
        // dd(request('req'));
        $request = request('req');
        $request['join'] = [
            'patients' => [
                'reletion' => [
                    'prefix' => 'patient_id',
                    'suffix' => 'id'
                ]
            ],
            'clinics' => [
                'reletion' => [
                    'prefix' => 'clinic_id',
                    'suffix' => 'id'
                ]
            ]
        ];
        $request['with'] = [
            'orderChildMedicines' => 'id,order_id,medicine_id,quantity',
            'orderChildMedicines.medicine' => 'id,name',
            'createdBy' => 'id,username',


        ];

         array_push($this->medicineOrderReportService->columns, 'patients.name as patient_name', 'clinics.clinic_name as clinic_name');
            $this->medicineOrderReportService->setRequest($request);
            $this->medicineOrderReportService->findAll();
            $this->response['success']  = true;
            $data = $this->medicineOrderReportService->getRows();
            $status_list = Arr::except(config('report.status_list'), []);
        // dd($data);
        $data = Excel::download(new medicineOrderReportExport($data, $status_list), 'medicine-order.xlsx');
        return $data;
    }
}
