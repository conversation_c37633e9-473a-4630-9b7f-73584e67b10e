@foreach ($data['rows'] as $row)
    @php
        $timeIn = isset($row['time_in']) ? strtotime($row['time_in']) : null;
        $timeOut = isset($row['time_out']) ? strtotime($row['time_out']) : null;

        $timeSpent = '';
        if ($timeIn && $timeOut) {
            $diffInSeconds = $timeOut - $timeIn;
            $hours = floor($diffInSeconds / 3600);
            $minutes = floor(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $timeSpent = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
    @endphp
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row['users']['doctor_name'] ?? '' }}</td>
        <td>{{ $row['users']['schedule_doctors']['specialitys']['speciality'] ?? '' }}</td>
        <td>{{ $row['users']['schedule_doctors']['doctor_type']['title'] ?? '' }}</td>
        <td>{{ $row['clinics']['clinic_name'] ?? '' }}</td>
        <td>{{ $row['date'] ?? '' }}</td>
        @if ($report_type == 1)
            <td>{{ $row['s_time'] }} to {{ $row['e_time'] }}</td>
            <td>{{ $row['time_in'] ?? '' }}</td>
            <td>{{ $row['time_out'] ?? '' }}</td>
        @endif
        <td>{{ $timeSpent }}</td>
        <td>{{ $row['completed_appointments_count'] ?? 0 }}</td>
    </tr>
@endforeach
