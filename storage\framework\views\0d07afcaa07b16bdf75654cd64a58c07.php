<?php $__env->startSection('title'); ?>
    <?php echo e(config('report.title', 'Membership Report')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="conatiner-fluid content-inner p-3">
        <div class="row">
            <!-----Report filter starts here-------->
            <div class="col-md-12">
                <div class="card mb-3">
                    <div class="card-body   justify-content-between align-items-center">
                        <div class="header-title mb-3">
                            <h4 class="card-title mb-3 mb-md-0">Schedule Wise Doctor Report</h4>
                        </div>
                        <div class="d-flex justify-content-end align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                            <div class="d-flex flex-lg-nowrap flex-wrap gap-2 ">
                                <div class="form-group mb-0 w-100">
                                    <label class="mb-1 d-flex gap-1 align-items-center">
                                        <span>Show</span>
                                        <select id="perPageCount" class="form-select form-select-sm px-1">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                        <span>entries</span>
                                    </label>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select
                                        class="select2-multpl-custom1 js-states form-select form-select-sm search-change"
                                        style="width: 100%;" onchange="reportTypeChange(this)">
                                        <option value="" selected="" disabled="">Report Type</option>
                                        <option value="1" <?php echo e(request('report_type') == 1 ? 'selected' : ''); ?>>Schedule Wise</option>
                                        <option value="2" <?php echo e(request('report_type') == 2 ? 'selected' : ''); ?>>Date Range Wise</option>
                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select-sm search-change" data-style="py-0"
                                        id="doctor_type" name="doctor_type" style="width: 100%; min-width:200px;">
                                        <option value="">Select Doctor Type</option>
                                        <?php $__currentLoopData = $data['doctor_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row['id']); ?>"><?php echo e($row['title']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select class="select2-multpl-custom1 form-select-sm search-change" data-style="py-0"
                                        id="doctor_id" name="doctor_id" style="width: 100%; min-width:200px;">
                                        <option value="">Select Doctor</option>
                                        <?php $__currentLoopData = $data['doctor_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row['id']); ?>"><?php echo e($row['username']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <select name="clinic_id" class="select2-multpl-custom1 form-select-sm search-change"
                                        data-style="py-0" style="width: 100%; min-width:200px;">
                                        <option value="">Filter By Clinic</option>
                                        <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="form-group mb-0 w-100">
                                    <input type="text" name="date" id="date_range"
                                        placeholder="Please select a date range"
                                        class="form-control form-control-sm flatpickr-input active search-date-range"
                                        readonly="readonly">
                                </div>
                            </div>
                            <a href="<?php echo e(route('report.sechedulewisedoctor.index')); ?>"><button type="button" class="btn btn-sm btn-primary">Reset</button></a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV(this)">Export</button>

                        </div>
                    </div>

                </div>
            </div>
            <!------Report filter ends here-------->
        </div>
        <!------Report box ends here-------->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="Table-custom-padding1 table-responsive ">
                        <table id="data-list" class="table table-sm table-sm0 table-striped table-hover w-100 dataTable "
                            data-page-length='25'>
                            <thead>
                                <tr>
                                    <th class="word-wrap wht-space-custom">SL No</th>
                                    <th class="word-wrap wht-space-custom">Doctor</th>
                                    <th class="word-wrap wht-space-custom">Speciality</th>
                                    <th class="word-wrap wht-space-custom">Doctor Type</th>
                                    <th class="word-wrap wht-space-custom">Clinic</th>
                                    <th class="word-wrap wht-space-custom">Date</th>
                                    <?php if(request('report_type') == 1): ?>
                                        <th class="word-wrap wht-space-custom">Scheduled Time</th>
                                        <th class="word-wrap wht-space-custom">Time In</th>
                                        <th class="word-wrap wht-space-custom">Time Out</th>
                                    <?php endif; ?>
                                    <th class="word-wrap wht-space-custom">Time Spent</th>
                                    <th class="word-wrap wht-space-custom">Patient</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php echo $__env->make('admin.custom.loading', ['td' => 12, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tbody>
                            <tfoot>
                                <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Pre loading data ends here -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function reportTypeChange(params) {
            let report_type = params.value;
            window.location.href = "<?php echo e(route('report.sechedulewisedoctor.index')); ?>?report_type=" + report_type;
        }
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });

        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id

            let url = "<?php echo e(config('report.url') . 'schedulewisedoctor'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            /* let sortCollumns = [
                 "id",
                 "patients.name",
                 "phone",
                 "registration_no"
             ];
             setSortCollumns(sortCollumns);*/
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    "date": {
                        "type": "eq",
                        "value": "<?php echo e(date('Y-m-d')); ?>"
                    },
                    "report_type": {
                        "type": "eq",
                        "value": "<?php echo request('report_type') ?? 1; ?>"
                    }
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 10,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });



        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('report.url') . 'schedulewisedoctorexport'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/schedulewisedoctor/index.blade.php ENDPATH**/ ?>