<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id'] ?? ''); ?></td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at'])) ?? ''); ?></td>
        <td><?php echo e($row['patient_name'] ?? ''); ?></td>
        <td><?php echo e($row['patient_phone'] ?? ''); ?></td>
        
        <td>
            <?php $__currentLoopData = $row['patient_with_membership']['membership_registration_opd'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $membership): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div style="margin-bottom: 5px;">
                     <?php echo e($membership['memberships']['name'] ?? '-'); ?><br>
                     
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </td>
        <td><?php echo e($row['user_doctors']['username'] ?? ''); ?></td>
        <td><?php echo e($row['date'] ?? ''); ?></td>

        <td><?php echo e($row['time_slot'] ?? ''); ?></td>
        <td><?php echo e($row['clinics']['clinic_name'] ?? ''); ?></td>
        <td><?php echo e($statusLabels[$row['status']] ?? ''); ?></td>
         <td><?php echo e($appointmentdataSource[$row['data_source']] ?? ''); ?></td>
        <td><?php echo e($row['executive_name'] ?? ''); ?></td>
       

    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/opdappointment/api/list.blade.php ENDPATH**/ ?>