<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\VerifyMembership;
use Modules\Report\Models\Clinic;
use Modules\Report\Models\User;
use Modules\Report\Models\Membership;
use Modules\Report\Models\MembershipRegistration;
use Modules\Report\Models\Patient;
use DB;

class MembershipService extends ApplicationDefaultService
{
    

    public $entity;
    public $entityClinic;
    public $entityPatient;
    public $entityMembership;
    
    public $columns = [
        'membership_registrations.id',
        'membership_registrations.patient_id',
        'membership_registrations.phone',
        'membership_registrations.clinic_id',
        'membership_registrations.start_date',
        'membership_registrations.end_date',
        'membership_registrations.registration_no',
        'membership_registrations.category_id',
        'membership_registrations.card_type',
        'membership_registrations.registration_date',
        'membership_registrations.smart_card',
        'membership_registrations.data_source',
        'membership_registrations.source',
        'membership_registrations.pincode',
        'membership_registrations.remarks',
        'membership_registrations.is_renewal',
        'membership_registrations.status',
        'membership_registrations.created_by',
        'membership_registrations.modified_by',
        'membership_registrations.deleted_by',
        'membership_registrations.created_at',
        'membership_registrations.updated_at',
        'membership_registrations.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'phone',
        'clinic_id',
        'start_date',
        'end_date',
        'registration_no',
        'category_id',
        'card_type',
        'registration_date',
        'smart_card',
        'data_source',
        'source',
        'pincode',
        'remarks',
        'is_renewal',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // public function __construct(VerifyMembership $entity, Clinic $entityClinic, membership $entityMembership)
    // {
    //     $this->entity = $entity;
    //     $this->entityClinic = $entityClinic;
    //     $this->entityMembership = $entityMembership;
    // }

    public function __construct(MembershipRegistration $entity,Clinic $entityClinic,Membership $entityMembership){
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
       
        $this->entityMembership =$entityMembership;
       // $this->entityAppointment =$entityAppointment;
      //  $this->entityRewardPoint = $entityRewardPoint;
    //  dd($this->entity);
    }




    public function allVerifyMemberships()
    {
        $this->entity = $this->entity->select($this->columns)
            ->where('status', 1)
            ->get();
        return $this->entity;
    }
    public function allClinics()
    {
        $this->entityClinic = $this->entityClinic->select('id', 'clinic_name')
            ->where('status', 1)
            ->get();
        return $this->entityClinic;
    }

    public function allMembers()
    {
        $this->entityMembership = $this->entityMembership->select('id', 'name')
            ->where('status', 1)
            ->get();
        return $this->entityMembership;
    }
    public function dataSourcecount($data_source)
    {
        $dataSourcecount = MembershipRegistration::where('data_source',$data_source);
            if (isset($this->request['filter']) && !empty($this->request['filter'])) {
                foreach ($this->request['filter'] as $column => $option) {
                    if (!empty($option['value'])) {
                        switch ($option['type']) {
                            case 'eq':
                                if (is_array($option['value']))
                                    $dataSourcecount = $dataSourcecount->whereIn($column, $option['value']);
                                else
                                    $dataSourcecount = $dataSourcecount->where($column, $option['value']);
                                break;
                            case 'like':
                                $dataSourcecount = $dataSourcecount->where($column, 'like', '%' . $option['value'] . '%');
                                break;
                            case 'gt':
                                $dataSourcecount = $dataSourcecount->where($column, '>', $option['value']);
                                break;
                            case 'lt':
                                $dataSourcecount = $dataSourcecount->where($column, '<', $option['value']);
                                break;
                            case 'gteq':
                                $dataSourcecount = $dataSourcecount->where($column, '>=', $option['value']);
                                break;
                            case 'lteq':
                                $dataSourcecount = $dataSourcecount->where($column, '<=', $option['value']);
                                break;
                            case 'noteq':
                                $dataSourcecount = $dataSourcecount->where($column, '!=', $option['value']);
                                break;
                            case 'betweenDate':
                                $dataSourcecount = $dataSourcecount->whereBetween($column, $option['value']);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            $dataSourcecount = $dataSourcecount->count();
        return $dataSourcecount;
    }
}
