<?php

namespace Modules\Report\Http\Controllers;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Report\Services\SampleCollectionReportService; 
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use App\Exports\SampleReportExport;
use Maatwebsite\Excel\Facades\Excel;
use DB;
class SampleCollectionReportController extends Controller
{
    private $sampleCollectionReportService;
    public function __construct(
        SampleCollectionReportService $sampleCollectionReportService,


    ) {
        $this->sampleCollectionReportService = $sampleCollectionReportService;
    }
    public function index(Request $request)
    {

        $data = [
            'clinic_list' => $this->sampleCollectionReportService->allClinics()->toArray(),
        ];

        return view('report::samplecollection.index', compact('data'));
    }

    public function sampleCollectionReport(Request $request)
    {
        try {

            $request['with'] = [
                'sampleCollection' => 'id,clinic_id,patient_id,date_of_collection,type_of_collection,appointment_type',
                'test' => 'id,test_name',
                'sinCreatedBy' => 'id,username as collected_by',
            ];
            $this->sampleCollectionReportService->setRequest($request);
            $this->sampleCollectionReportService->findAll();
            $this->response['success']  = true;
            $data = $this->sampleCollectionReportService->getRows();
            $visit_type = config('report.visit_type');
            $this->response['tbody'] = view('report::samplecollection.api.list', compact('data', 'visit_type'))->render();
            $this->response['tfoot'] = $this->sampleCollectionReportService->paginationCustom();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function sampleCollectionReportExport(Request $request)
    {
        try {
            $this->response['req']  = $request->all();
            $this->response['url']  = route('report.samplecollection.exportLink', ['req' => $request->all()]);
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function sampleCollectionExportLink()
    {
        $request = request('req');
        $request['with'] = [
            'sampleCollection' => 'id,clinic_id,patient_id,date_of_collection,type_of_collection,appointment_type',
            'test' => 'id,test_name',
            'sinCreatedBy' => 'id,username as collected_by',
        ];
        $this->sampleCollectionReportService->setRequest($request);
        $this->sampleCollectionReportService->findAll();
        $this->response['success']  = true;
        $data = $this->sampleCollectionReportService->getRows();
        $visit_type = config('report.visit_type');
        $data = Excel::download(new SampleReportExport($data, $visit_type), 'sample-collection.xlsx');
        return $data;
    }


}
