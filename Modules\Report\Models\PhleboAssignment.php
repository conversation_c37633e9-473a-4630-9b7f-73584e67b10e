<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PhleboAssignment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id',
        'sample_id',
        'role_id',
        'phlebo_id',
        'schedule_time',
        'remarks',
        'test_count',
        'collected_testcount',
        'handover_testcount',
        'actual_date_time_of_collection',
        'temp_of_bag_at_collection',
        'payment_status',
        'amount',
        'mode_of_payment',
        'collection_status',
        'date_time_at_handover',
        'temp_of_bag_at_handover',
        'amount_at_handover',
        'handover_status',
        'collected_by',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function userPhlebotomists(): BelongsTo
    {
        return $this->belongsTo(User::class, 'phlebo_id', 'id');
    }
}
