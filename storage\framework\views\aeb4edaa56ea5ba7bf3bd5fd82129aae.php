<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
     <tr>
         <td><?php echo e($loop->iteration); ?></td>
         <td><?php echo e($row['registration_date'] ?? ''); ?></td>
         <td><?php echo e($row['patients']['name'] ?? ''); ?></td>
         <td><?php echo e($row['phone'] ?? ''); ?></td>
         <td><?php echo e($row['patients']['sex'] ?? ''); ?></td>
         <td><?php echo e(Helper::ageCalculator($row['patients']['birthdate'] ?? '')); ?></td>
          
         <td><?php echo e($row['registration_no'] ?? ''); ?></td>


         <td><?php echo e($statusLabels[$row['status']] ?? ''); ?></td>
         <td><?php echo e($row['end_date'] ?? ''); ?></td>
         <td><?php echo e($row['memberships']['name'] ?? ''); ?></td>

         <td><?php echo e($row['payment_bill']['0']['total_amount'] ?? 0); ?></td>
         <td><?php echo e($row['clinics']['clinic_name'] ?? ''); ?></td> 
         <td><?php echo e($row['data_source'] ?? ''); ?></td>
         
         
         <td><?php echo e($row['created_by']['username'] ?? '-'); ?></td>
         <td><?php echo e(' '); ?></td>
         <td><?php echo e($row['remarks'] ?? ''); ?></td>
     </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/membership/api/list.blade.php ENDPATH**/ ?>