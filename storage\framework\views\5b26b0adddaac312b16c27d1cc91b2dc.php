<?php $__env->startSection('title'); ?>
    <?php echo e(config('notification.channel_title', 'category')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12 preloading">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">Category</h4>
                </div>
                <div class="header-action">
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>
                    <div class="col-md-9">
                        <div class="row justify-content-end">
                            <div class="col-md-4">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="0,1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Name
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo $__env->make('admin.custom.loading', ['td' => 3, 'action' => 3], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tbody>
                        <tfoot>
                            <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Category Info</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('notification.category_url') . 'list'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "category_id",
                "category_name"
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {

                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": perPage,
                    "offset": 0
                },
                "sort": {
                    "category_id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Notification\resources/views/category/index.blade.php ENDPATH**/ ?>