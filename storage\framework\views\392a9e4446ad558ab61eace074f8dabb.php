<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e(date('Y-m-d H:i:s', strtotime($row['created_at'])) ?? ''); ?></td> 
        <td><?php echo e($row['patient_name'] ?? ''); ?></td>
        <td><?php echo e($row['patient_phone'] ?? ''); ?></td>
        <td><?php echo e($row['patient_sex'] ?? ''); ?></td>
        <td><?php echo e($row['patient_age'] ?? ''); ?></td>
        
       
      


        <td></td>
        <td></td>
       

    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/leadgeneration/api/list.blade.php ENDPATH**/ ?>