<?php

namespace Modules\Settings\Services;

use App\Services\ApplicationDefaultService;
use Modules\Settings\Models\ReviewsSurveyMaster;
use DB;

class ReviewSurveyService extends ApplicationDefaultService
{
    public $entity;
    public $columns = [
        'id',
        'survey_title',
        'language',
        'survey_settings_json',
        'url_slag',
        'short_link',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'survey_title',
        'language',
        'survey_settings_json',
        'url_slag',
        'short_link',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(ReviewsSurveyMaster $entity) {
        $this->entity =$entity;
    }
    public function allReviewSurveys(){
        $this->entity = $this->entity->select($this->columns)
            ->where('status',1)
            ->get();
        return $this->entity;
    }
}
