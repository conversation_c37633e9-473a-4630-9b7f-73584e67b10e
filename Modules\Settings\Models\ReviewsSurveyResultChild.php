<?php

namespace Modules\Settings\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReviewsSurveyResultChild extends Model
{
    use SoftDeletes;

    protected $table = 'reviews_survey_result_childs';
    protected $fillable = [
        'id',
        'result_id',
        'question_no',
        'question',
        'answer',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $dates = ['deleted_at'];
    public function reviewsSurveyResult(): BelongsTo
    {
        return $this->belongsTo(ReviewsSurveyResult::class, 'result_id', 'id');
    }
}
