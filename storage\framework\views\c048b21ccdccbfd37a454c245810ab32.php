<form class="clearfix" method="post"
    action="<?php echo e($id ? config('settings.url_review_survey') . 'update/' . $id : config('settings.url_review_survey') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" name="language" id="language" value="<?php echo e($id ? $data['language'] : ''); ?>">
    <input type="hidden" name="survey_title" id="survey_title" value="<?php echo e($id ? $data['survey_title'] : ''); ?>">
    <input type="hidden" name="survey_settings_json" id="survey_settings_json" value="<?php echo e($id ? $data['survey_settings_json'] : ''); ?>">
    <div class="row">
        <div class="form-group col-md-12" id="surveyCreator">

        </div>
        <div class="form-group col-md-12 d-flex justify-content-end">
            <button type="button" name="submit" onclick="submitSurvey()"
                class="btn btn-primary text-white"><?php echo e($id ? 'Update' : 'Save'); ?> Survey</button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<script>
    $('#languageSelector').val('<?php echo e($id ? $data['language'] : ''); ?>');
    survey('<?php echo $id ? $data['survey_settings_json'] : '{}'; ?>');
</script>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Settings\resources/views/reviewSurvey/api/addEdit.blade.php ENDPATH**/ ?>