<form class="clearfix" method="post" action="{{ $id ? config('diagnostic.sc_url') . 'addBarcodeEdit/' . $id : '' }}"
    data-mode="{{ $id ? 'update' : 'add' }}" enctype="multipart/form-data" id="submitForm">
    <div class="border border-2 rounded-2">
        <div class="card-header border-bottom p-3" style="background-color: #fff8f8;">
            <div class="row">
                <!--Form header starts here-->
                <div class="col-md-8 mb-2 mb-md-0">
                    <div class="d-flex flex-wrap align-items-center gap-2">
                        <div class="d-flex align-items-center me-5 pe-5 pe-md-0">
                            <h6 class="h5 fw-bold text-primary">
                                <span>{{ $data['patient_detail']->name }}</span>
                            </h6> &nbsp;
                            <span class="h7" style="margin-top: 4px;">
                                (<span>{{ $data['patient_detail']->sex }}</span>/
                                <span>{{ Helper::ageCalculator($data['patient_detail']->birthdate) }}</span>)
                            </span>
                        </div>

                        <p class="text-dark h7 me-0 mb-0">
                            <svg height="20" fill="#d01337" style="    margin-top: -2px;"
                                enable-background="new 0 0 24 24" id="fi_3596091" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m21.5 21h-19c-1.378 0-2.5-1.122-2.5-2.5v-13c0-1.378 1.122-2.5 2.5-2.5h19c1.378 0 2.5 1.122 2.5 2.5v13c0 1.378-1.122 2.5-2.5 2.5zm-19-17c-.827 0-1.5.673-1.5 1.5v13c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-13c0-.827-.673-1.5-1.5-1.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m7.5 12c-1.378 0-2.5-1.122-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.122 2.5 2.5-1.122 2.5-2.5 2.5zm0-4c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m11.5 17c-.276 0-.5-.224-.5-.5v-1c0-.827-.673-1.5-1.5-1.5h-4c-.827 0-1.5.673-1.5 1.5v1c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-1c0-1.378 1.122-2.5 2.5-2.5h4c1.378 0 2.5 1.122 2.5 2.5v1c0 .276-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 9h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 13h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 17h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                            </svg>
                            <span>{{ $data['diagnostic']->unique_id }}</span>
                        </p>
                        <p class="text-dark h7 me-0 mb-0">
                            <svg height="20" fill="#d01337" style="    margin-top: -2px;"
                                enable-background="new 0 0 24 24" id="fi_3596091" viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m21.5 21h-19c-1.378 0-2.5-1.122-2.5-2.5v-13c0-1.378 1.122-2.5 2.5-2.5h19c1.378 0 2.5 1.122 2.5 2.5v13c0 1.378-1.122 2.5-2.5 2.5zm-19-17c-.827 0-1.5.673-1.5 1.5v13c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-13c0-.827-.673-1.5-1.5-1.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m7.5 12c-1.378 0-2.5-1.122-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.122 2.5 2.5-1.122 2.5-2.5 2.5zm0-4c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m11.5 17c-.276 0-.5-.224-.5-.5v-1c0-.827-.673-1.5-1.5-1.5h-4c-.827 0-1.5.673-1.5 1.5v1c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-1c0-1.378 1.122-2.5 2.5-2.5h4c1.378 0 2.5 1.122 2.5 2.5v1c0 .276-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 9h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 13h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                                <g>
                                    <path
                                        d="m20.5 17h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                    </path>
                                </g>
                            </svg>
                            <span>{{ $data['patient_detail']->uhid_no }}</span>
                        </p>
                        <p class=" text-dark h7 mb-0">
                            <!--<span class="text-dark fw-bold">Phone:</span>-->
                            <svg id="fi_159832" height="15" fill="#d01337" style="    margin-top: -3px;"
                                version="1.1" viewBox="0 0 482.6 482.6" x="0px" xml:space="preserve"
                                xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
                                <g>
                                    <path d="M98.339,320.8c47.6,56.9,104.9,101.7,170.3,133.4c24.9,11.8,58.2,25.8,95.3,28.2c2.3,0.1,4.5,0.2,6.8,0.2
c24.9,0,44.9-8.6,61.2-26.3c0.1-0.1,0.3-0.3,0.4-0.5c5.8-7,12.4-13.3,19.3-20c4.7-4.5,9.5-9.2,14.1-14
c21.3-22.2,21.3-50.4-0.2-71.9l-60.1-60.1c-10.2-10.6-22.4-16.2-35.2-16.2c-12.8,0-25.1,5.6-35.6,16.1l-35.8,35.8
c-3.3-1.9-6.7-3.6-9.9-5.2c-4-2-7.7-3.9-11-6c-32.6-20.7-62.2-47.7-90.5-82.4c-14.3-18.1-23.9-33.3-30.6-48.8
c9.4-8.5,18.2-17.4,26.7-26.1c3-3.1,6.1-6.2,9.2-9.3c10.8-10.8,16.6-23.3,16.6-36s-5.7-25.2-16.6-36l-29.8-29.8
c-3.5-3.5-6.8-6.9-10.2-10.4c-6.6-6.8-13.5-13.8-20.3-20.1c-10.3-10.1-22.4-15.4-35.2-15.4c-12.7,0-24.9,5.3-35.6,15.5l-37.4,37.4
c-13.6,13.6-21.3,30.1-22.9,49.2c-1.9,23.9,2.5,49.3,13.9,80C32.739,229.6,59.139,273.7,98.339,320.8z M25.739,104.2
c1.2-13.3,6.3-24.4,15.9-34l37.2-37.2c5.8-5.6,12.2-8.5,18.4-8.5c6.1,0,12.3,2.9,18,8.7c6.7,6.2,13,12.7,19.8,19.6
c3.4,3.5,6.9,7,10.4,10.6l29.8,29.8c6.2,6.2,9.4,12.5,9.4,18.7s-3.2,12.5-9.4,18.7c-3.1,3.1-6.2,6.3-9.3,9.4
c-9.3,9.4-18,18.3-27.6,26.8c-0.2,0.2-0.3,0.3-0.5,0.5c-8.3,8.3-7,16.2-5,22.2c0.1,0.3,0.2,0.5,0.3,0.8
c7.7,18.5,18.4,36.1,35.1,57.1c30,37,61.6,65.7,96.4,87.8c4.3,2.8,8.9,5,13.2,7.2c4,2,7.7,3.9,11,6c0.4,0.2,0.7,0.4,1.1,0.6
c3.3,1.7,6.5,2.5,9.7,2.5c8,0,13.2-5.1,14.9-6.8l37.4-37.4c5.8-5.8,12.1-8.9,18.3-8.9c7.6,0,13.8,4.7,17.7,8.9l60.3,60.2
c12,12,11.9,25-0.3,37.7c-4.2,4.5-8.6,8.8-13.3,13.3c-7,6.8-14.3,13.8-20.9,21.7c-11.5,12.4-25.2,18.2-42.9,18.2
c-1.7,0-3.5-0.1-5.2-0.2c-32.8-2.1-63.3-14.9-86.2-25.8c-62.2-30.1-116.8-72.8-162.1-127c-37.3-44.9-62.4-86.7-79-131.5
C28.039,146.4,24.139,124.3,25.739,104.2z"></path>
                                </g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                                <g></g>
                            </svg> <span>{{ $data['diagnostic']->patient_phone }}</span>
                        </p>
                    </div>
                </div>
                <div class="col-md-4 mb-0 mb-md-0  payment pad_bot">
                    <p
                        class="mb-1 text-gray d-flex justify-content-md-end justify-content-start gap-1 align-items-center">
                        <svg id="fi_833593" height="15" fill="#d01337" style="enable-background:new 0 0 512 512"
                            version="1.1" viewBox="0 0 512 512" x="0px" xml:space="preserve"
                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
                            <g>
                                <g>
                                    <g>
                                        <circle cx="386" cy="210" r="20"></circle>
                                        <path d="M432,40h-26V20c0-11.046-8.954-20-20-20c-11.046,0-20,8.954-20,20v20h-91V20c0-11.046-8.954-20-20-20
c-11.046,0-20,8.954-20,20v20h-90V20c0-11.046-8.954-20-20-20s-20,8.954-20,20v20H80C35.888,40,0,75.888,0,120v312
c0,44.112,35.888,80,80,80h153c11.046,0,20-8.954,20-20c0-11.046-8.954-20-20-20H80c-22.056,0-40-17.944-40-40V120
c0-22.056,17.944-40,40-40h25v20c0,11.046,8.954,20,20,20s20-8.954,20-20V80h90v20c0,11.046,8.954,20,20,20s20-8.954,20-20V80h91
v20c0,11.046,8.954,20,20,20c11.046,0,20-8.954,20-20V80h26c22.056,0,40,17.944,40,40v114c0,11.046,8.954,20,20,20
c11.046,0,20-8.954,20-20V120C512,75.888,476.112,40,432,40z"></path>
                                        <path d="M391,270c-66.72,0-121,54.28-121,121s54.28,121,121,121s121-54.28,121-121S457.72,270,391,270z M391,472
c-44.663,0-81-36.336-81-81s36.337-81,81-81c44.663,0,81,36.336,81,81S435.663,472,391,472z"></path>
                                        <path d="M420,371h-9v-21c0-11.046-8.954-20-20-20c-11.046,0-20,8.954-20,20v41c0,11.046,8.954,20,20,20h29
c11.046,0,20-8.954,20-20C440,379.954,431.046,371,420,371z"></path>
                                        <circle cx="299" cy="210" r="20"></circle>
                                        <circle cx="212" cy="297" r="20"></circle>
                                        <circle cx="125" cy="210" r="20"></circle>
                                        <circle cx="125" cy="297" r="20"></circle>
                                        <circle cx="125" cy="384" r="20"></circle>
                                        <circle cx="212" cy="384" r="20"></circle>
                                        <circle cx="212" cy="210" r="20"></circle>
                                    </g>
                                </g>
                            </g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                        </svg>
                        <input readonly="" type="text" style="width: 83px;"
                            class="form-control pay_in border-0 px-0 py-0 bg-transparent" id="app_date"
                            value="{{ $data['diagnostic']->date_of_collection }}">
                    </p>
                </div>
                <!--Form header ends here-->
            </div>
        </div>
        <div class="row  pb-0 mb-0 d-none d-md-flex">
            <div class="col-md-12 ">
                <div class="row mx-0 border-bottom py-3">
                    <div class="col-md-5 ">
                        <h6 class="h7">Items</h6>
                    </div>
                    <div class="col-md-7 ">
                        <div class="row">
                            <div class="col-md-4 px-1">
                                <h6 class="h7">Sample Type</h6>
                            </div>
                            <div class="col-md-6 px-1">
                                <h6 class="h7">SIN No.</h6>
                            </div>

                            <div class="col-md-2 px-0">
                                <h6 class="h7">Vial Qty.</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mx-0 py-2">
            <div class="col-md-12  payment py-2">
                <input type="hidden" name="sample_id" id="sample_id" value="{{ $data['diagnostic']->id }}">
                <input type="hidden" name="checked_sample_test" id="checked_sample_test" value="">
                @foreach ($data['sampleCollectionBreakupItem'] as $key => $row)
                    <div class="row align-items-center form-check1" style="min-height: 40px;">
                        <div class="col-md-5 mb-2 mb-md-0 d-flex  gap-2">
                            <span>
                                <input type="checkbox"
                                    style="box-sizing: border-box; width: 16px; height: 16px; min-width: auto; min-height: auto; display: block;"
                                    onclick="selectTestCollection(this)" class="test_id form-check-input abcd"
                                    value="{{ $row['id'] }}">
                            </span>
                            <label class="form-check-label" style="white-space: normal; "
                                for="doctor_consultanat_3182">
                                {{ $row['test']['test_name'] }}
                            </label>
                        </div>
                        <div class="col-md-7 mb-2 mb-md-0 " id="append_div_{{ $row['id'] }}"
                            style="display: none;">
                            <div class="row my-0">
                                <div class="col-md-4 col-12 mb-1 mb-md-0 px-1">
                                    @php
                                        $sample_type_arr = explode(',', $row['test']['sample_type']);
                                        $old_barcode = '';
                                        if (
                                            count($sample_type_arr) == 1 &&
                                            isset($data['getBarcodeSampleType'][$sample_type_arr[0]])
                                        ) {
                                            $old_barcode = $data['getBarcodeSampleType'][$sample_type_arr[0]];
                                        }
                                    @endphp
                                    @if (count($sample_type_arr) > 1)
                                        <select class="form-control form-control-sm"
                                            id="sample_type_{{ $row['id'] }}"
                                            onchange="setTestId('{{ $row['id'] }}'); testCalculation();">
                                            <option value="" disabled selected>--Select--</option>
                                            @foreach ($sample_type_arr as $sample_type)
                                                <option value="{{ $sample_type }}">{{ $sample_type }}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <input type="text" class="form-control form-control-sm"
                                            id="sample_type_{{ $row['id'] }}" value="{{ $sample_type_arr[0] }}"
                                            readonly="">
                                    @endif
                                </div>
                                <div class="col-md-6 col-10 mb-1 mb-md-0 px-1">
                                    <div class="d-flex gap-1">
                                        <input type="text" class="form-control form-control-sm barcode"
                                            id="sample_barcode_{{ $row['id'] }}"
                                            onfocusout="setTestId('{{ $row['id'] }}'); testCalculation();"
                                            placeholder="Enter Barcode" value="{{ $old_barcode }}">
                                        <button type="button" class="border-0 bg-transparent"
                                            onclick="qrCode('{{ $row['id'] }}')">
                                            <svg enable-background="new 0 0 53 53" height="30" id="fi_5393325"
                                                viewBox="0 0 53 53" width="30"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <g>
                                                        <g>
                                                            <path
                                                                d="m12.7705078 52h-10.2705078c-.828125 0-1.5-.671875-1.5-1.5v-10.2705078c0-.828125.671875-1.5 1.5-1.5s1.5.671875 1.5 1.5v8.7705078h8.7705078c.828125 0 1.5.671875 1.5 1.5s-.671875 1.5-1.5 1.5z"
                                                                fill="#2c3642"></path>
                                                        </g>
                                                        <g>
                                                            <path
                                                                d="m50.5 14.2705078c-.828125 0-1.5-.671875-1.5-1.5v-8.7705078h-8.7705078c-.828125 0-1.5-.671875-1.5-1.5s.671875-1.5 1.5-1.5h10.2705078c.828125 0 1.5.671875 1.5 1.5v10.2705078c0 .828125-.671875 1.5-1.5 1.5z"
                                                                fill="#2c3642"></path>
                                                        </g>
                                                        <g>
                                                            <path
                                                                d="m50.5 52h-10.2705078c-.828125 0-1.5-.671875-1.5-1.5s.671875-1.5 1.5-1.5h8.7705078v-8.7705078c0-.828125.671875-1.5 1.5-1.5s1.5.671875 1.5 1.5v10.2705078c0 .828125-.671875 1.5-1.5 1.5z"
                                                                fill="#2c3642"></path>
                                                        </g>
                                                        <g>
                                                            <path
                                                                d="m2.5 14.2705078c-.828125 0-1.5-.671875-1.5-1.5v-10.2705078c0-.828125.671875-1.5 1.5-1.5h10.2705078c.828125 0 1.5.671875 1.5 1.5s-.671875 1.5-1.5 1.5h-8.7705078v8.7705078c0 .828125-.671875 1.5-1.5 1.5z"
                                                                fill="#2c3642"></path>
                                                        </g>
                                                    </g>
                                                    <path
                                                        d="m46.4151039 13.8069038.0000267 25.3862228c0 1.1047287-.8890991 2.0002289-1.9938278 2.0000038-3.634346-.0007515-13.1418991-.0035248-35.836956-.0137558-1.1042151-.0004997-1.999434-.8957825-1.9994345-2l-.0000134-25.372467c-.000001-1.1045694.8954301-2.000001 2-2.000001h35.830204c1.1045695-.0000009 2.000001.8954288 2.000001 1.9999972z"
                                                        fill="#ededed"></path>
                                                    <g fill="#2c3642">
                                                        <path d="m27.738 16.785h1.938v19.43h-1.938z">
                                                        </path>
                                                        <path d="m38.214 16.785h1.938v19.43h-1.938z">
                                                        </path>
                                                        <path d="m41.659 16.785h1.954v19.43h-1.954z">
                                                        </path>
                                                        <path d="m14.54 16.785h1.907v19.406h-1.907z">
                                                        </path>
                                                        <path
                                                            d="m19.3087711 16.7851753h4.0762654v19.4298706l-4.0762654-.0242805z">
                                                        </path>
                                                        <path d="m32.614 16.785h4.061v19.43h-4.061z">
                                                        </path>
                                                        <path d="m25.385 16.785h.938v19.43h-.938z">
                                                        </path>
                                                        <path d="m9.387 16.785h4.122v19.406h-4.122z">
                                                        </path>
                                                    </g>
                                                    <path
                                                        d="m52 26.5000153v.0000038c0 .885519-.7178535 1.6033726-1.6033745 1.6033726h-47.7932238c-.8855186 0-1.6033731-.7178535-1.6033731-1.6033726v-.0000038c0-.8855171.7178545-1.6033726 1.6033731-1.6033726h47.7932243c.8855205 0 1.603374.7178554 1.603374 1.6033726z"
                                                        fill="#f34624"></path>
                                                </g>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2 col-2 mb-1 mb-md-0 px-1">
                                    <input type="text" class="form-control form-control-sm"
                                        id="vialqty_{{ $row['id'] }}" value="1" onkeyup="testCalculation()"
                                        placeholder="Vial Qty">
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
                <div id="patientesign" class="border-top mt-2 pt-2">
                    <div class="d-flex justify-content-between">
                        <h5 class=" mb-2 h5">
                            Patient signature
                        </h5>
                        @if (empty($data['diagnostic']->patient_esign))
                            <div class="d-flex align-items-center gap-2">
                                <input type="checkbox" id="patient_esign" value="1"
                                    onchange="drawSignatureAsName(this,'{{ $data['patient_detail']->name }}')">
                                <label for="patient_esign">Signature as patient name</label>
                            </div>
                        @endif
                    </div>
                    @if (empty($data['diagnostic']->patient_esign))
                        <canvas id="signature_pad" name="signature_pad" class="border-gray" height="200"
                            style="max-width:100%; width:100%;"></canvas>
                        <br>
                        <p>
                            <strong>Disclaimer:</strong> 
                            I hereby authorize and consent to MyMdHealthcare Labs to collect, use, process, and share with affiliates and contracted third parties, the information necessary to perform these tests. I/We agree that the remaining specimen can used for Quality Assurance and Research purposes if needed.
                        </p>
                        <div id="signaturebuttondiv" class="d-flex gap-2">
                            <button type="button" name="save_signature" id="save_signature"
                                class="btn btn-sm btn-gray text-white">Save Signature</button>
                            <button type="button" name="clear_signature" id="clear_signature"
                                class="btn btn-sm btn-primary text-white">Clear Signature</button>
                        </div>
                        <div id="result"></div>
                    @else
                        @php
                            $path = $data['diagnostic']->patient_esign;
                            if (!str_starts_with($path, 'https://storage.googleapis.com')) {
                                $path = Storage::disk('gcs')->url($path);
                            }
                        @endphp
                        <img src="{{ $path }}" alt="Patient signature" width="200" style="height:auto;">
                    @endif
                </div>
            </div>

            <div id="myDiv" class="col-md-6"></div>

            <div class="row" id="barcodegeneratorbuttondiv"
                style="{{ empty($data['diagnostic']->patient_esign) ? 'display:none' : '' }}">
                <div class="modal-footer">
                    <button type="submit" name="submit" class="btn btn-primary text-white" id="submitBtn"
                        {{ $id ? '' : 'disabled' }}>Submit</button>
                </div>
                <div class="form-group col-md-12">
                    <div id="errorMessage" class="" style="color: red;"></div>
                </div>
            </div>
        </div>
    </div>
</form>
<script>
    canvas = document.getElementById('signature_pad');
    ctx = canvas.getContext('2d');
    saveButton = document.getElementById('save_signature');
    clearButton = document.getElementById('clear_signature');
    resultDiv = document.getElementById('result');
    drawing = false;

    // Resize the canvas to fill the parent element if needed
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    function drawSignatureAsName(params, name) {
        if (params.checked) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            // ctx.font = "20px Arial";
            ctx.font = "30px 'Caveat'";
            ctx.fillText(name, 10, 50);
        } else {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
    }

    function startDrawing(x, y) {
        drawing = true;
        ctx.beginPath();
        ctx.moveTo(x, y);
    }

    function draw(x, y) {
        if (!drawing) return;

        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.strokeStyle = '#000';

        ctx.lineTo(x, y);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x, y);
    }

    function stopDrawing() {
        drawing = false;
    }

    // Mouse events
    canvas.addEventListener('mousedown', (event) => startDrawing(event.offsetX, event.offsetY));
    canvas.addEventListener('mousemove', (event) => draw(event.offsetX, event.offsetY));
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseleave', stopDrawing);

    // Touch events
    canvas.addEventListener('touchstart', (event) => {
        const rect = canvas.getBoundingClientRect();
        const touch = event.touches[0];
        startDrawing(touch.clientX - rect.left, touch.clientY - rect.top);
    });

    canvas.addEventListener('touchmove', (event) => {
        event.preventDefault(); // Prevent scrolling while drawing
        const rect = canvas.getBoundingClientRect();
        const touch = event.touches[0];
        draw(touch.clientX - rect.left, touch.clientY - rect.top);
    });

    canvas.addEventListener('touchend', stopDrawing);

    saveButton.addEventListener('click', () => {
        const signatureDataURL = canvas.toDataURL('image/png');
        const blob = dataURLToBlob(signatureDataURL);
        console.log(signatureDataURL, blob);

        const sample_id = $("#sample_id").val();

        const formData = new FormData();
        formData.append('signature_pad', blob, 'signature.png');
        formData.append('sample_id', sample_id);
        console.log(formData);

        let url = "{{ config('diagnostic.sc_url') . 'patientSignatureCollection/' }}";
        fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    resultDiv.innerHTML = 'Signature saved successfully!';
                    $("#result").show();
                    $("#barcodegeneratorbuttondiv").show();
                    $("#save_signature").hide();
                } else {
                    resultDiv.innerHTML = 'Failed to save signature!';
                }
            })
            .catch(error => console.error('Error:', error));
    });

    clearButton.addEventListener('click', () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        $("#barcodegeneratorbuttondiv").hide();
        $("#save_signature").show();
        $("#result").hide();
        $('#patient_esign').prop('checked', false);
    });

    function dataURLToBlob(dataURL) {
        const byteString = atob(dataURL.split(',')[1]);
        const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);

        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }

        return new Blob([ab], {
            type: mimeString
        });
    }
</script>
