<?php if(count($list['OTCList']) > 0): ?>
    <?php $__currentLoopData = $list['OTCList']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td>
                <?php echo e($row->patients->name); ?><br><?php echo e($row->registration_no); ?>

                <input type="hidden" id="patient_phone" value="<?php echo e($list['phone']); ?>">
                <input type="hidden" id="membership_id" value="<?php echo e($row->id); ?>">
            </td>
            <td><?php echo e($row->start_date); ?></td>
            <td><?php echo e($row->end_date); ?></td>
            <td class="mb-0 counter h4">
                <?php echo e(number_format($list['OTCUtilization_total'], 2)); ?>

            </td>
            <td class="mb-0  counter h4" style="color: #017a01 !important;">
                <?php echo e(number_format($list['per_cycle_total'], 2)); ?>

            </td>
            <td class="mb-0 counter h4">
                <?php echo e(number_format(($list['per_cycle_total'] - $list['OTCUtilization_total']), 2)); ?>

            </td>
            <td><?php echo e($row->next_cycle); ?></td>
        </tr>
        <tr>
            <td colspan="6" class="border-0"></td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <tr>
        <td colspan="6" class="border-0">No Records Found</td>
    </tr>
    <tr>
        <td colspan="4" class="border-0"></td>
    </tr>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Membership\resources/views/otcBalance/api/listFamily.blade.php ENDPATH**/ ?>