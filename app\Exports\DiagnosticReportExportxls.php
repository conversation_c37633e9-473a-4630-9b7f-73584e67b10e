<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Contracts\View\View;

class DiagnosticReportExportxls implements FromView, ShouldAutoSize
{
    protected $reportData ;
    protected $list;

    public function __construct($reportData ,$list)
    {
       
        $this->reportData  = $reportData ;
        $this->list = $list;
        
    }
   /* public function view(): View
    {
        $data = $this->data;
        $list = $this->list;
       // dd($data);
        return view('report::diagnostic.export.diagnosticExport');
    }*/
        public function view(): View
    {
        return view('report::diagnostic.export.diagnosticExport', [
            'data' => $this->reportData ,
            'list' => $this->list,
        ]);
    }
}
