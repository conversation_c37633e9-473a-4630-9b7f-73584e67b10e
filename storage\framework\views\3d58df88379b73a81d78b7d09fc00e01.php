
<?php if(isset($payment_modes)): ?>
    <div class="row">
        <div class="col-md-12 mt-3 mt-md-0">
            <div class="form-group last">
                <?php if(isset($sub_text)): ?>
                    <h6 class="h6 fw-bold">Payment Mode :</h6>
                    <div class="mb-3 h8"><?php echo e($sub_text); ?></div>
                <?php else: ?>
                    <h6 class="h6 mb-3 fw-bold">Payment Mode :</h6>
                <?php endif; ?>
                <?php $__currentLoopData = $payment_modes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input hobbies_class" onclick="openPaymentMode(this)"
                            type="checkbox" id="payment_mode_<?php echo e($key); ?>" data-id="<?php echo e($row); ?>"
                            value="<?php echo e($key); ?>">
                        <label class="form-check-label"
                            for="payment_mode_<?php echo e($key); ?>"><?php echo e($row); ?></label>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <div class="row show_payment">
    </div>
<?php endif; ?>
<!--------------- Payment total starts here----------------->

<div class=" mt-3 ">
    <div class="row border rounded-2 bg-light align-items-center py-2">

        <div class="col-md-2">
            <p class="fw-bold text-dark m-0 mb-3 mb-md-0">Total Amount</p>
        </div>

        <div class="col-md-10">
            <div class="row">

                <div class="col-md-4 mb-2 mb-md-0  align-items-center">
                    <div class="row align-items-center">
                        <label for="exampleInputEmail1" class="col-5 col-md-5 fw-bold">Paid </label>
                        <div class="col-7 col-md-7">
                            <input type="text" class="form-control form-control-sm pay_in"
                                name="total_amount" id="total_amount" value="0" readonly=""
                                placeholder="">
                        </div>
                    </div>
                </div>


                <div class="col-md-4  align-items-center">
                    <div class="row align-items-center">
                        <label for="exampleInputEmail1" class="col-5 col-md-5 fw-bold">Due </label>
                        <div class="col-7 col-md-7">
                            <input type="text" class="form-control form-control-sm pay_in"
                                name="gross" id="total_due" value="<?php echo e($due); ?>" placeholder=" "
                                readonly="">
                            <input type="hidden" id="min_payment_percentage" value="<?php echo e($min_payment_percentage); ?>">
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-------------Payment total ends here----------------->
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Billing\resources/views/billing/api/paymentForm.blade.php ENDPATH**/ ?>