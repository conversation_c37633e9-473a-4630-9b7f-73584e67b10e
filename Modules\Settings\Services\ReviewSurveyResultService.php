<?php

namespace Modules\Settings\Services;

use App\Services\ApplicationDefaultService;
use Modules\Settings\Models\ReviewsSurveyResult;
use DB;

class ReviewSurveyResultService extends ApplicationDefaultService
{
    public $entity;
    public $columns = [
        'id',
        'survey_id',
        'url_slag',
        'patient_id',
        'source_type',
        'source_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'survey_id',
        'url_slag',
        'patient_id',
        'source_type',
        'source_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(ReviewsSurveyResult $entity) {
        $this->entity =$entity;
    }
    public function allReviewSurveyResults(){
        $this->entity = $this->entity->select($this->columns)
            ->where('status',1)
            ->get();
        return $this->entity;
    }
}
