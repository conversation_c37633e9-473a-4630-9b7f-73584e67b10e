<?php

return [
    'name' => 'Report',
    'title' => 'Report',
    'url' => env('APP_URL') . 'api/report/',
    'data_source_order_list' => [
        1 => 'Patient Order',
        2 => 'Backend Order',
        3 => 'Campaign',
    ],

    'membership_status' => [
        1 => 'Inactive',
        2 => 'Deleted',
        3 => 'Active',
        4 => 'Expired',
        6 => 'Upcoming',
    ],
    'appoint_status' => [
        1 => 'Pending',
        2 => 'Confirmed',
        3 => 'Arrived',
        4 => 'With Doctor',
        5 => 'Checked Out',
        6 => 'Closed',
        7 => 'Cancelled',
        8 => 'completed',
    ],


    'data_source' => [
        1 => 'Walkin OPD',
        2 => 'Website',
        3 => 'Mobile Application',
        4 => 'Request Appointment Page',
        5 => 'International Women\'s Day',
        6 => 'Campaign Page (Rs. 10)',
        7 => 'Telecommunication',
        8 => 'Women\'s Joint Pain Screening',
    ],

 'status_list' => [
        1 => 'Pending',
        2 => 'Assigned',
        3 => 'Collected',
        4 => 'Completed',
        5 => 'Confirmed',
        6 => 'Cancel',
        7 => 'Processing',
        8 => 'Dispatched',
        9 => 'Delivered',
        10 => 'Out of Stock',
        11 => 'Order placed to warehouse',
    ],
    


      'lead_datasource_list' => [
        1 => 'complementory test',
        2 => 'order medicine',
      
    ],
    'visit_type' => [
        'CV' => 'Clinic Visit',
        'HC' => 'Home Collection',
    ],

    'diagnostic_status_list' => [
        0 => 'Pending',
        1 => 'Confirmed',
        2 => 'Assigned',
        3 => 'Sample Collected',
        4 => 'Segricated',
        5 => 'Transfered',
        6 => 'Department Recieve',
        7 => 'Report Save',
        8 => 'Report Approved',
        9 => 'Completed',
        10 => 'Cancelled',
    ],


    'diagnostic_doctor_type' => [
        1 => 'myMD Doctor',
        2 => 'Other Doctor',
       
    ],
];
