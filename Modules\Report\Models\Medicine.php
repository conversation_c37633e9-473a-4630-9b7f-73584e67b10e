<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Medicine extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'name',
        'category',
        'c_item_code',
        'price',
        'box',
        's_price',
        'quantity',
        'generic',
        'company',
        'effects',
        'effects',
        'e_date',
        'add_date',
        'c_cat_code',
        'c_item_category_head_code',
        'item_category_class_code',
        'c_mfac_code',
        'c_group_code',
        'c_cont_code',
        'c_pack_type_code',
        'hsn_sac_code',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $dates = ['deleted_at'];

    public function medicineCategory(): BelongsTo
    {
        return $this->belongsTo(MedicineCategory::class, 'category', 'id');
    }
    public function orderChildMedicines(): HasMany
    {
        return $this->hasMany(OrderChildMedicine::class, 'medicine_id', 'id');
    }
}
