<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportCSVService extends ServiceProvider
{
    private $entity;
    private $header;
    private $body;
    private $fileName;
    public function __construct($entity,$header,$body,$fileName)
    {
        $this->entity = $entity;
        $this->header = $header;
        $this->body = $body;
        $this->fileName = $fileName;
    }
    public function export()
    {
        try {
            ini_set('max_execution_time', 0);
            ini_set('memory_limit', '2048M');
            $entity = $this->entity;
            $header = $this->header;
            $body = $this->body;
            $fileName = $this->fileName;
            $response = new StreamedResponse(function () use ($entity,$header,$body) {
                ob_clean(); // clean (erase) the output buffer
                $handle = fopen('php://output', 'w');
                if ($handle === false) {
                    throw new \Exception('Failed to open output stream');
                }
                // Add CSV headers
                fputcsv($handle, $header);
                $total = $entity->count();
                $chunkSize = 10;
                $offset = 0;
                do {
                    $rows = $entity->skip($offset)->take($chunkSize)->get()->toArray();
                    
                    foreach ($rows as $row) {
                        $bodyCsv = [];
                        foreach ($body as $keyVal) {
                            $converted = preg_replace_callback('/\{([\w\.]+)\}/', function ($matches) use ($row) {
                                try {
                                    $parts = explode('.', $matches[1]);
                                    $temp = $row;
                                    foreach ($parts as $part) {
                                        if (!isset($temp[$part])) return '';
                                        $temp = $temp[$part];
                                    }
                                    return is_scalar($temp) ? $temp : json_encode($temp);
                                } catch (\Throwable $e) {
                                    return '';
                                }
                            }, $keyVal);
                            if (strpos($converted, 'date("Y-m-d', 0) !== false) {
                                $converted = eval('return '.$converted.';') ?? '';
                            }
                            array_push($bodyCsv,$converted);
                        }
                        
                        fputcsv($handle, $bodyCsv);
                    }
                    $offset += $chunkSize;
                } while ($offset < $total);
                fclose($handle);
            });
            $response->headers->set('Content-Type', 'text/csv');
            $response->headers->set('Content-Disposition', 'attachment; filename="'.$fileName.'"');
            return $response;
        } catch (\Throwable $th) {
            // Return JSON response for API or fallback
            return response()->json([
                'success' => false,
                'message' => 'Failed to export CSV: ' . $th->getMessage(),
            ], 500);
        }
        
    }
}
