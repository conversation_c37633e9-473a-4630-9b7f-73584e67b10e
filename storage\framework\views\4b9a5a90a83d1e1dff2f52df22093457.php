<input type="hidden" class="form-control" name="template_id" id="template_id" value="<?php echo e($template_id); ?>">
<button type="button" onclick="clearTemplate()" id="cancel-templt-btn"
    class="bg-primary border-0 py-0 px-1 text-white rounded-1 h8 fw-normal py-1 px-2  mt-0 position-absolute"
    style="top: 4px !important; right: 10px !important; display: none;">Cancel Template</button>
<!----Default Medicine search starts here----->
<div class="form-group col-sm-12 mb-0">
    <div class="row">
        <div class="col-sm-12 mb-2">
            <div class="row">
                <div class="col-md-12">
                    <label class="form-label mb-1 fs-8 mb-1 text-gray">Lab Tests</label>
                    <select name="test_ids[]" id="test_ids"
                        class="selectpicker form-select select2-multpl-custom1 w-100 bg-light  p-0 border-0 shadow-none form-control fs-8 fw-bold"
                        data-style="py-0" multiple>
                        <?php $__currentLoopData = $list['tests']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($row['id']); ?>"
                                <?php echo e(in_array($row['id'], $test_childs) ? 'selected' : ''); ?>>
                                <?php echo e($row['test_name']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>

        <div class="col-md-12 mb-2">
            <div class="d-flex justify-content-between">
                <label class="form-label mb-1 fs-8 text-gray">Medicine</label>
            </div>

            <select id="medicine_search" class="select2-multpl-custom1 form-select form-control form-select-sm  w-100"
                data-style="py-0" onchange="medicineSearch(this.value)">
                <option value="">Search Medicine</option>
                <?php $__currentLoopData = $list['medicines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row['id'] . '||' . $row['name']); ?>">
                        <?php echo e($row['name']); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
</div>

<div class="col-md-12 medicine-thead d-none d-md-block mb-2">
    <section class="row align-items-start">
        <div class="form-group medicine_sect col-md-4  mb-0">
            <div class="col-md-12"><label class="form-label fs-8 text-gray d-block">
                    Medicine </label>
            </div>
        </div>
        <div class="form-group medicine_sect col-md-1 col-4 ps-md-0  pe-md-0 mb-0">
            <div class="col-md-12"><label class="form-label fs-8 text-gray d-block ">Frequency</label>
            </div>
        </div>
        <div class="form-group medicine_sect col-md-1 col-4 pe-md-0 mb-0">
            <div class="col-md-12"><label class="form-label fs-8 text-gray d-block ">Days</label></div>
        </div>
        <div class="form-group medicine_sect col-md-2 col-4 mb-0">
            <div class="col-md-12">
                <label class="form-label fs-8 text-gray d-block ">Instruction</label>
            </div>
        </div>
        <div class="form-group medicine_sect col-md-3 ps-md-0 mb-0  pe-md-0">
            <div class="col-md-12">
                <label class="form-label fs-8 text-gray d-block ">Notes</label>
            </div>
        </div>
    </section>

</div>
<div class="col-md-12" id="medicine_box">
    <?php if(isset($medicine_childs) && !empty($medicine_childs)): ?>
        <?php $__currentLoopData = $medicine_childs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $rand = rand(1000, 9999);
            ?>
            <section class="row" id="row-<?php echo e($rand); ?>">
                <div class="form-group col-md-4  mb-2">
                    <div class="col-md-12">
                        <label class="form-label h8 text-gray d-block d-md-none">
                            Medicine</label>
                    </div>
                    <div class="col-md-12">
                        <input type="hidden" name="medicine_ids[]" value="<?php echo e(isset($row['type_id']) ? $row['type_id'] : ''); ?>">
                        <input type="text" name="medicines[]" value="<?php echo e(isset($row['name']) ? $row['name'] : ''); ?>"
                            class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0 w-100"
                            placeholder="Medicine Name"="">
                    </div>
                </div>
                <div class="form-group col-md-1 col-4 ps-md-0  pe-md-0">
                    <div class="col-md-12">
                        <label class="form-label h8 text-gray d-block d-md-none">Frequency</label>
                    </div>
                    <div class="col-md-12">
                        <select name="frequencys[]"
                            class="form-select border-0 border-lightGray h8 border-bottom bg-transparent px-1 py-1 fs-8 rounded-0 w-100">
                            <option selected="" disabled="">Select</option>
                            <option value="1+1+1" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '1+1+1' ? 'selected' : '') : ''); ?>>1+1+1
                            </option>
                            <option value="1+1+0" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '1+1+0' ? 'selected' : '') : ''); ?>>1+1+0
                            </option>
                            <option value="0+1+1" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '0+1+1' ? 'selected' : '') : ''); ?>>0+1+1
                            </option>
                            <option value="1+0+1" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '1+0+1' ? 'selected' : '') : ''); ?>>1+0+1
                            </option>
                            <option value="1+0+0" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '1+0+0' ? 'selected' : '') : ''); ?>>1+0+0
                            </option>
                            <option value="0+0+1" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '0+0+1' ? 'selected' : '') : ''); ?>>0+0+1
                            </option>
                            <option value="0+1+0" <?php echo e(isset($row['frequency']) ? ($row['frequency'] == '0+1+0' ? 'selected' : '') : ''); ?>>0+1+0
                            </option>
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-1 col-3  pe-md-0 ps-0 ps-md-3">
                    <div class="col-md-12">
                        <label class="form-label h8 text-gray d-block d-md-none">Days</label>
                    </div>
                    <div class="col-md-12">
                        <input type="number" name="day_intervals[]"
                            class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0"
                            value="<?php echo e(isset($row['days']) ? $row['days'] : ''); ?>" min="1" style="width:100%;" data-style="py-0">
                    </div>
                </div>
                <div class="form-group col-md-2 col-5 ps-0 ps-md-3">
                    <div class="col-md-12">
                        <label class="form-label h8 text-gray d-block d-md-none">Instruction</label>
                    </div>
                    <div class="col-md-12">
                        <select name="instructions[]"
                            class="form-select form-select-sm border-0 border-lightGray h8 border-bottom bg-transparent px-1 py-1 fs-8 rounded-0 w-100">
                            <option selected="" disabled="">Select</option>
                            <option value="After Meal" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'After Meal' ? 'selected' : '') : ''); ?>>
                                After Meal</option>
                            <option value="Before Meal" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'Before Meal' ? 'selected' : '') : ''); ?>>
                                Before Meal</option>
                            <option value="After Breakfast" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'After Breakfast' ? 'selected' : '') : ''); ?>>
                                After Breakfast</option>
                            <option value="Before Breakfast"
                                <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'Before Breakfast' ? 'selected' : '') : ''); ?>>
                                Before Breakfast</option>
                            <option value="After Lunch"<?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'After Lunch' ? 'selected' : '') : ''); ?>>
                                After Lunch</option>
                            <option value="Before Lunch" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'Before Lunch' ? 'selected' : '') : ''); ?>>
                                Before Lunch</option>
                            <option value="After Dinner" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'After Dinner' ? 'selected' : '') : ''); ?>>
                                After Dinner</option>
                            <option value="Before Dinner" <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'Before Dinner' ? 'selected' : '') : ''); ?>>
                                Before Dinner</option>
                            <option value="In Empty Stomach"
                                <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'In Empty Stomach' ? 'selected' : '') : ''); ?>>
                                In Empty Stomach</option>
                            <option value="Not Applicable"
                                <?php echo e(isset($row['instruction']) ? ($row['instruction'] == 'Not Applicable' ? 'selected' : '') : ''); ?>>
                                Not Applicable</option>
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-3 ps-md-0  pe-md-0 mb-0">
                    <div class="col-md-12">
                        <label class="form-label h8 text-gray d-block d-md-none">Notes</label>
                    </div>
                    <div class="col-md-12">
                        <textarea rows="1" name="note_lists[]"
                            class="border-0 border-lightGray h8  border-bottom bg-transparent px-0 py-1 fs-8 rounded-0" placeholder="Notes"
                            style="width:100%;" data-style="py-0"><?php echo e(isset($row['notes']) ? $row['notes'] : ''); ?></textarea>
                    </div>
                </div>
                <div class="del col-md-1 align-items-center justify-content-end d-flex">
                    <button onclick="removeMedicine(<?php echo e($rand); ?>)" type="button"
                        class="btn btn-white icon-16 p-0 rounded-circle d-flex align-items-center justify-content-center mt-2 pt-1"
                        style="">
                        <svg width="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4"
                                d="M19.643 9.48851C19.643 9.5565 19.11 16.2973 18.8056 19.1342C18.615 20.8751 17.4927 21.9311 15.8092 21.9611C14.5157 21.9901 13.2494 22.0001 12.0036 22.0001C10.6809 22.0001 9.38741 21.9901 8.13185 21.9611C6.50477 21.9221 5.38147 20.8451 5.20057 19.1342C4.88741 16.2873 4.36418 9.5565 4.35445 9.48851C4.34473 9.28351 4.41086 9.08852 4.54507 8.93053C4.67734 8.78453 4.86796 8.69653 5.06831 8.69653H18.9388C19.1382 8.69653 19.3191 8.78453 19.4621 8.93053C19.5953 9.08852 19.6624 9.28351 19.643 9.48851Z"
                                fill="currentColor"></path>
                            <path
                                d="M21 5.97686C21 5.56588 20.6761 5.24389 20.2871 5.24389H17.3714C16.7781 5.24389 16.2627 4.8219 16.1304 4.22692L15.967 3.49795C15.7385 2.61698 14.9498 2 14.0647 2H9.93624C9.0415 2 8.26054 2.61698 8.02323 3.54595L7.87054 4.22792C7.7373 4.8219 7.22185 5.24389 6.62957 5.24389H3.71385C3.32386 5.24389 3 5.56588 3 5.97686V6.35685C3 6.75783 3.32386 7.08982 3.71385 7.08982H20.2871C20.6761 7.08982 21 6.75783 21 6.35685V5.97686Z"
                                fill="currentColor"></path>
                        </svg>
                    </button>
                </div>
            </section>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
</div>
<div class="col-md-12 mb-3">
    <button class="bg-gray border-0 py-0 px-1 text-white rounded-3 fs-8 fw-normal py-1 px-3  mt-0 text-uppercase"
        type="button" onclick="addMoreMedicine()">Add new medicine</button>
</div>

<div class="form-group col-md-12">
    <label class="form-label mb-1 text-gray fs-8 mb-1" for="advice">Advice</label>
    <textarea class="form-control form-control-sm bg-light text-dark h8" name="advice" rows="1" id="advice"><?php echo e($advice); ?></textarea>
</div>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Prescription\resources/views/prescription/api/treatmentPlan.blade.php ENDPATH**/ ?>