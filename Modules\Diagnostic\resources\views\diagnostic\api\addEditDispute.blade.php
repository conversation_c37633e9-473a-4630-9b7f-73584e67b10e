@if ($list['source_list']['source_id'] && !empty($list['offer_list']))
    @php
        $offer_list = $list['offer_list'][0];
        $data_offer = json_encode(
            [
                'offered_type' => $offer_list['offered_type'],
                'rate_type' => $offer_list['rate_type'],
                'rate' => $offer_list['rate'],
                'test_list' => $offer_list['test_list'],
                'hc_list' => $offer_list['hc_list'],
                'clinic_list' => $offer_list['clinic_list'],
            ],
            true,
        );
    @endphp
    <script>
        offerDiscount('<?php echo $data_offer; ?>');
    </script>
@endif
<form class="clearfix" method="post"
    action="{{ config('diagnostic.url') . 'add' }}"
    data-mode="add" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" id="source_id" name="source_id" value="">
    <input type="hidden" id="source_type" name="source_type" value="">
    <input type="hidden" id="source_test_id" name="source_test_id" value="">
    <input type="hidden" name="patient_id" value="{{ $data['patient_detail']->id }}">
    <input type="hidden" name="patient_phone" value="{{ $list['patient_phone'] }}">
    <div class="row" data-select2-id="select2-data-16-ukk2">
        <div class="col-sm-12" data-select2-id="select2-data-15-62ts">
            <!------------Top card starts here----------->
            <div class="card  mb-3" data-select2-id="select2-data-14-bl8l">
                <div class="card-header border-bottom p-4" style="background-color: #fff8f8;">
                    <div class=" row">
                        <!--Form header starts here-->
                        <div class="col-md-12 mb-3 mb-md-0">
                            <div class="d-flex flex-wrap justify-content-between">
                                <div class="d-flex gap-4 flex-wrap">
                                    <div class="d-flex align-items-center me-5">
                                        <h6 class="h5 fw-bold text-primary">
                                            <span>{{ $data['patient_detail']->name }}</span>
                                        </h6> &nbsp;<span class="h7" style="margin-top: 4px;">
                                            (<span>{{ $data['patient_detail']->sex }}</span>/<span>{{ Helper::ageCalculator($data['patient_detail']->birthdate) }}</span>)</span>
                                    </div>
                                    <div class="d-flex align-items-center  justify-content-between flex-wrap">
                                        <p class="text-dark h7 me-3 mb-0">
                                            <!--<span class="text-dark fw-bold">UHID:</span>--><svg height="20"
                                                fill="#d01337" style="    margin-top: -2px;"
                                                enable-background="new 0 0 24 24" id="fi_3596091" viewBox="0 0 24 24"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <g>
                                                    <path
                                                        d="m21.5 21h-19c-1.378 0-2.5-1.122-2.5-2.5v-13c0-1.378 1.122-2.5 2.5-2.5h19c1.378 0 2.5 1.122 2.5 2.5v13c0 1.378-1.122 2.5-2.5 2.5zm-19-17c-.827 0-1.5.673-1.5 1.5v13c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-13c0-.827-.673-1.5-1.5-1.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m7.5 12c-1.378 0-2.5-1.122-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.122 2.5 2.5-1.122 2.5-2.5 2.5zm0-4c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m11.5 17c-.276 0-.5-.224-.5-.5v-1c0-.827-.673-1.5-1.5-1.5h-4c-.827 0-1.5.673-1.5 1.5v1c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-1c0-1.378 1.122-2.5 2.5-2.5h4c1.378 0 2.5 1.122 2.5 2.5v1c0 .276-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 9h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 13h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                                <g>
                                                    <path
                                                        d="m20.5 17h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z">
                                                    </path>
                                                </g>
                                            </svg>
                                            <span id="patientUHIDNo">{{ $data['patient_detail']->uhid_no }}</span>
                                        </p>
                                        <p class=" text-dark h7 me-3 mb-0">
                                            <!--<span class="text-dark fw-bold">Phone:</span>-->
                                            <svg id="fi_159832" height="15" fill="#d01337"
                                                style="    margin-top: -3px;" version="1.1" viewBox="0 0 482.6 482.6"
                                                x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
                                                <g>
                                                    <path d="M98.339,320.8c47.6,56.9,104.9,101.7,170.3,133.4c24.9,11.8,58.2,25.8,95.3,28.2c2.3,0.1,4.5,0.2,6.8,0.2
            c24.9,0,44.9-8.6,61.2-26.3c0.1-0.1,0.3-0.3,0.4-0.5c5.8-7,12.4-13.3,19.3-20c4.7-4.5,9.5-9.2,14.1-14
            c21.3-22.2,21.3-50.4-0.2-71.9l-60.1-60.1c-10.2-10.6-22.4-16.2-35.2-16.2c-12.8,0-25.1,5.6-35.6,16.1l-35.8,35.8
            c-3.3-1.9-6.7-3.6-9.9-5.2c-4-2-7.7-3.9-11-6c-32.6-20.7-62.2-47.7-90.5-82.4c-14.3-18.1-23.9-33.3-30.6-48.8
            c9.4-8.5,18.2-17.4,26.7-26.1c3-3.1,6.1-6.2,9.2-9.3c10.8-10.8,16.6-23.3,16.6-36s-5.7-25.2-16.6-36l-29.8-29.8
            c-3.5-3.5-6.8-6.9-10.2-10.4c-6.6-6.8-13.5-13.8-20.3-20.1c-10.3-10.1-22.4-15.4-35.2-15.4c-12.7,0-24.9,5.3-35.6,15.5l-37.4,37.4
            c-13.6,13.6-21.3,30.1-22.9,49.2c-1.9,23.9,2.5,49.3,13.9,80C32.739,229.6,59.139,273.7,98.339,320.8z M25.739,104.2
            c1.2-13.3,6.3-24.4,15.9-34l37.2-37.2c5.8-5.6,12.2-8.5,18.4-8.5c6.1,0,12.3,2.9,18,8.7c6.7,6.2,13,12.7,19.8,19.6
            c3.4,3.5,6.9,7,10.4,10.6l29.8,29.8c6.2,6.2,9.4,12.5,9.4,18.7s-3.2,12.5-9.4,18.7c-3.1,3.1-6.2,6.3-9.3,9.4
            c-9.3,9.4-18,18.3-27.6,26.8c-0.2,0.2-0.3,0.3-0.5,0.5c-8.3,8.3-7,16.2-5,22.2c0.1,0.3,0.2,0.5,0.3,0.8
            c7.7,18.5,18.4,36.1,35.1,57.1c30,37,61.6,65.7,96.4,87.8c4.3,2.8,8.9,5,13.2,7.2c4,2,7.7,3.9,11,6c0.4,0.2,0.7,0.4,1.1,0.6
            c3.3,1.7,6.5,2.5,9.7,2.5c8,0,13.2-5.1,14.9-6.8l37.4-37.4c5.8-5.8,12.1-8.9,18.3-8.9c7.6,0,13.8,4.7,17.7,8.9l60.3,60.2
            c12,12,11.9,25-0.3,37.7c-4.2,4.5-8.6,8.8-13.3,13.3c-7,6.8-14.3,13.8-20.9,21.7c-11.5,12.4-25.2,18.2-42.9,18.2
            c-1.7,0-3.5-0.1-5.2-0.2c-32.8-2.1-63.3-14.9-86.2-25.8c-62.2-30.1-116.8-72.8-162.1-127c-37.3-44.9-62.4-86.7-79-131.5
            C28.039,146.4,24.139,124.3,25.739,104.2z">
                                                    </path>
                                                </g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                            </svg>
                                            <span>{{ Helper::maskPhoneNumber($list['patient_phone']) }}</span>
                                        </p>
                                        <!-- <p class="mb-1 text-gray">Age: </p> -->
                                        <span>
                                        </span>
                                        @if ($service)
                                            <p class="text-dark h7 mb-0">
                                                <svg height="18" width="20" style="margin-top: -2px;"
                                                    fill="#d01337" clip-rule="evenodd" fill-rule="evenodd"
                                                    id="fi_9720867" stroke-linejoin="round" stroke-miterlimit="2"
                                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="Icon">
                                                        <path
                                                            d="m7.035 8.641 3.996-4.58c.244-.28.598-.441.969-.441s.725.161.969.441l3.996 4.58 3.813-2.408c.427-.269.972-.264 1.394.014s.642.778.562 1.277l-1.812 11.322c-.141.884-.904 1.534-1.798 1.534h-14.248c-.894 0-1.657-.65-1.798-1.534l-1.812-11.322c-.08-.499.14-.999.562-1.277s.967-.283 1.394-.014zm-4.213-.886 1.737 10.854c.025.156.159.271.317.271h14.248c.158 0 .292-.115.317-.271l1.737-10.854-3.567 2.252c-.536.339-1.239.236-1.656-.241l-3.955-4.534-3.955 4.534c-.417.477-1.12.58-1.656.241l-3.567-2.252z">
                                                        </path>
                                                        <path
                                                            d="m20.037 15.129c.414 0 .75.336.75.75s-.336.75-.75.75h-16.074c-.414 0-.75-.336-.75-.75s.336-.75.75-.75z">
                                                        </path>
                                                    </g>
                                                </svg>

                                                {{ $service }}: Active <span>
                                                </span>
                                            </p>
                                        @endif
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <!--Form header ends here-->
                    </div>
                </div>
                <div class="card-body pb-3" data-select2-id="select2-data-13-l6y1">
                    <div class="row">
                        <div class="form-group col-md-3">
                            <label for="exampleInputEmail1" class="form-label">Visit Type *</label>
                            <select class="form-select form-select-sm" id="type_of_collection" name="type_of_collection"
                                value="" onchange="typeCollectionChange(this.value)">
                                <option value="">--Select Visit Type--</option>
                                @foreach ($list['visit_type'] as $key => $row)
                                    <option value="{{ $key }}"
                                        {{ $list['source_list']['source_id'] ? ($data['diagnostic']['type_of_collection'] == $key ? 'selected' : '') : '' }}>
                                        {{ $row }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-3" style="display: none;">
                            <label for="exampleInputEmail1" class="form-label">Visit Date *</label>
                            <input type="date" class="form-control form-control-sm" name="date_of_collection"
                                id="date_of_collection" required=""
                                value="{{ date('Y-m-d') }}"
                                min="{{ date('Y-m-d') }}">
                        </div>
                        <div class="col-md-3 form-group payment right-six" style="display: none;">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Status</label>
                            </div>
                            <div class="">
                                <select class="form-select form-select-sm m-bot15 js-example-basic-single"
                                    id="status" name="status" value="">
                                    @foreach ($list['status_list'] as $key => $row)
                                        <option value="{{ $key }}"
                                            {{$key == 1 ? 'selected' : ''}}>
                                            {{ $row }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 form-group right-six mb-3 mb-md-0">
                            <label class="form-label  d-block mb-2">Select Doctor:</label>
                            <input class="form-check-input" value="1" type="radio" name="doctortype"
                                id="doctortype" onchange="doctorTypeChange(this.value)"
                                {{ $list['source_list']['source_id'] ? ($data['diagnostic']['doctortype'] == 1 ? 'checked' : '') : '' }}>
                            <label class="form-check-label me-2" for="doctortype">myMD Doctor</label>
                            <input class="form-check-input" value="2" type="radio" name="doctortype"
                                id="doctortype1" onchange="doctorTypeChange(this.value)"
                                {{ $list['source_list']['source_id'] ? ($data['diagnostic']['doctortype'] == 2 ? 'checked' : '') : '' }}>
                            <label class="form-check-label" for="doctortype1">Other Doctor</label>
                        </div>
                        <div class="col-md-3 form-group panel selct-modal mymdDoctorDiv"
                            style="{{ $list['source_list']['source_id'] ? ($data['diagnostic']['doctortype'] == 1 ? '' : 'display:none;') : 'display:none;' }}">
                            <label for="exampleInputEmail1" class="form-label fw-bold"> Doctor</label>
                            <select id="doctor_id" name="doctor_id" class="select2-multpl-custom1 form-select"
                                data-style="py-0">
                                <option value="">Select</option>
                                @foreach ($list['doctor_list'] as $row)
                                    <option value="{{ $row->id }}"
                                        {{ $list['source_list']['source_id'] ? ($data['diagnostic']['doctor_id'] == $row->id ? 'selected' : '') : '' }}>
                                        {{ stripos($row->username, 'Dr.') !== 0 ? 'Dr. ' . $row->username : $row->username }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 form-group payment right-six otherDoctorDiv"
                            style="{{ $list['source_list']['source_id'] ? ($data['diagnostic']['doctortype'] == 2 ? '' : 'display:none;') : 'display:none;' }}">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Doctor Name</label>
                            </div>
                            <div class="d-flex">
                                <input type="text" class="form-control form-control-sm " name="doctor_first_name"
                                    id="doctor_first_name" placeholder="First Name"
                                    value="{{ $list['source_list']['source_id'] ? $data['diagnostic']->userDoctors->diagnosticDoctor->first_name : '' }}">
                                <input type="text" class="form-control form-control-sm " name="doctor_last_name"
                                    id="doctor_last_name" placeholder="Last Name"
                                    value="{{ $list['source_list']['source_id'] ? $data['diagnostic']->userDoctors->diagnosticDoctor->last_name : '' }}">
                            </div>
                        </div>
                        <div class="col-md-3 form-group payment right-six mb-3 mb-md-0">
                            <div class="payment_label">
                                <label for="exampleInputEmail1" class="form-label">Remarks</label>
                            </div>
                            <div class="">
                                <input type="text" class="form-control form-control-sm " rows="1"
                                    name="sample_remarks" id="sample_remarks" placeholder=" "
                                    value="{{ $list['source_list']['source_id'] ? $data['diagnostic']['remarks'] : '' }}">
                            </div>
                        </div>
                        <div class="col-md-3 form-group panel selct-modal4 CV_div {{ isset($list['user_clinic_id']) ? 'pe-none' : '' }}"
                            style="{{ $list['source_list']['source_id'] ? '' : 'display:none;' }}">
                            <label for="exampleInputEmail1" class="form-label "> Clinics</label>
                            <select id="clinic_id" name="clinic_id" class="select2-multpl-custom1 form-select"
                                data-style="py-0">
                                <option value="">Select</option>
                                @foreach ($list['clinic_list'] as $row)
                                    @php
                                        $selected = $list['source_list']['source_id'] ? ($data['diagnostic']['clinic_id'] == $row['id'] ? 'selected' : '') : '';
                                        if(isset($list['user_clinic_id'])){
                                            $selected = $list['user_clinic_id'] == $row['id'] ? 'selected' : '';
                                        }
                                    @endphp
                                    <option value="{{ $row['id'] }}"
                                        {{ $selected }}>
                                        {{ $row['clinic_name'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label class="form-label fw-bold" for="city">Upload Prescription</label>
                            <input type="file" id="prescription_upload"
                                class="default form-control form-control-sm" name="prescription_upload_file">
                        </div>
                        <div class="col-md-12 HC_div"
                            style="{{ $list['source_list']['source_id'] ? ($data['diagnostic']['type_of_collection'] == 'HC' ? '' : 'display:none;') : 'display:none;' }}">
                            <div class="row">
                                {{-- <div class="col-md-3 form-group panel">
                                    <label for="exampleInputBulding" class="form-label">Building No.</label>
                                    <input type="text" class="form-control form-control-sm" name="building_no"
                                        id="building_no" value="{{ $list['source_list']['source_id'] ? $data['diagnostic']['building_no'] : '' }}"
                                        placeholder="">
                                </div> --}}
                                @php
                                    $full_address = $list['source_list']['source_id'] ? $data['diagnostic']['full_address'] : ($list['source_list']['hc_address']['full_address'] ?? '');
                                    $landmark = $list['source_list']['source_id'] ? $data['diagnostic']['landmark'] : ($list['source_list']['hc_address']['landmark'] ?? '');
                                    $city = $list['source_list']['source_id'] ? $data['diagnostic']['city'] : ($list['source_list']['hc_address']['city'] ?? '');
                                    $pincode = $list['source_list']['source_id'] ? $data['diagnostic']['pincode'] : ($list['source_list']['hc_address']['pincode'] ?? '');
                                @endphp
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputAddress" class="form-label">Full Address</label>
                                    <input type="text" class="form-control form-control-sm" name="full_address"
                                        id="full_address"
                                        value="{{ $full_address }}" placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputLandmark" class="form-label">Land Mark</label>
                                    <input type="text" class="form-control form-control-sm" name="landmark"
                                        id="landmark" value="{{ $landmark }}"
                                        placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputCity" class="form-label">City</label>
                                    <input type="text" class="form-control form-control-sm" name="city"
                                        id="city" value="{{ $city }}"
                                        placeholder="">
                                </div>
                                <div class="col-md-3 form-group panel">
                                    <label for="exampleInputPincode" class="form-label">Pincode</label>
                                    <input type="text" class="form-control form-control-sm" name="pincode"
                                        id="pincode" value="{{ $pincode }}"
                                        placeholder="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card-body border-top" data-select2-id="select2-data-13-l6y1">
                    <div class="row">
                        <div class="col-md-12 col-12">
                            {{-- test_list --}}
                            <div class="row border-bottom pb-3 mb-3  d-md-flex">
                                <div class="col-md-12">
                                    {{-- <div class="row mb-4">
                                        <div class="col-md-12 ">
                                            <label for="exampleInputEmail1" class="form-label "> Tests</label>
                                        </div>
                                        <div class="col-md-3 ">
                                            <select class="select2-multpl-custom1 form-select" data-style="py-0"
                                                id="test_ids">
                                                <option value="">Select</option>
                                                @foreach ($list['test_list'] as $row)
                                                    <option value="{{ $row['id'] }}"
                                                        {{ $list['source_list']['source_id'] ? (in_array($row['id'], $data['diagnosticBreakupItemMergePackage']) ? 'disabled' : '') : '' }}>
                                                        {{ $row['test_name'] }} - {{ $row['test_price'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2 ">
                                            <button type="button" id="addItemButton"
                                                class="btn btn-sm btn-primary text-white"
                                                onclick="getTestList(test_ids.value)">Add
                                                Item</button>
                                        </div>
                                    </div> --}}

                                    <div class="row">
                                        <div class="col-md-12 col-4">


                                            <div class="row mb-3">
                                                <div class="col-md-3 ">
                                                    <h6 class="h7 labl-hgt">Items</h6>
                                                </div>
                                                <div class="col-md-2 ">
                                                    <h6 class="h7 labl-hgt">Test Code</h6>
                                                </div>
                                                <div class="col-md-2 ">
                                                    <h6 class="h7 labl-hgt">Report Delivery Date</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Charges</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Discount</h6>
                                                </div>
                                                <div class="col-md-1 ">
                                                    <h6 class="h7 labl-hgt">Total</h6>
                                                </div>
                                                <div class="col-md-1  text-md-center">
                                                    <h6 class="h7 labl-hgt">IsUrgent</h6>
                                                </div>
                                                <div class="col-md-1  text-md-center">
                                                    <h6 class="h7 labl-hgt">Action</h6>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-8">


                                            <div class="testItemDiv">
                                                @if ($list['source_list']['source_id'] && count($data['diagnosticBillItem']) > 0)
                                                    @foreach ($data['diagnosticBillItem'] as $key => $row)
                                                        @php
                                                            $test = $row->test;
                                                            $testData = [
                                                                'test' => $row->test,
                                                                'discount' => $row->discount,
                                                                'delivery_date' =>
                                                                    $data['diagnosticBreakupItemGroup'][$key]
                                                                        ->report_delivery_date ?? '',
                                                                'is_urgent' =>
                                                                    $data['diagnosticBreakupItemGroup'][$key]
                                                                        ->is_urgent ?? 0,
                                                                'package_test_list' =>
                                                                    $test->is_package == 2
                                                                        ? $test->packageTest($test->package_test)
                                                                        : [],
                                                            ];
                                                            // dd($testData);
                                                        @endphp
                                                        @include(
                                                            'diagnostic::diagnostic.api.testItemList',
                                                            [
                                                                'data' => $testData,
                                                            ]
                                                        )
                                                    @endforeach
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row HC_div mt-3"
                                        style="{{ $list['source_list']['source_id'] ? ($data['diagnostic']['type_of_collection'] == 'HC' ? '' : 'display:none;') : 'display:none;' }}">
                                        <h6 class="h6 fw-medium mb-2">Home Collection Charges</h6>
                                        <div class="col-md-3">
                                            <select class="form-select form-select-sm" data-style="py-0"
                                                name="home_collection_range" id="home_collection_range"
                                                onchange="homeCollectionRangeChange(this)">
                                                <option value="">Select Distance</option>
                                                @foreach ($list['distance_list'] as $key => $row)
                                                    <option value="{{ $row['id'] }}"
                                                        data-charge="{{ $row['charges'] }}">
                                                        {{ $row['area_range'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control form-control-sm"
                                                name="home_collection_charge" id="home_collection_charge"
                                                value=""
                                                placeholder="Home Collection Charge" readonly>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control form-control-sm"
                                                name="home_collection_quantity" id="home_collection_quantity"
                                                value="1"
                                                placeholder="" onkeyup="calculateCharges()"
                                                placeholder="Enter Quantity">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if ($list['source_list']['source_id'])
                            <div class="col-md-6  mt-4 mt-md-0  mb-3 mb-md-0 payment">
                                <div class="row">
                                    <div class="col-md-12 mb-2  row  align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Sub Total
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                name="sub_total" id="sub_total"
                                                value="" placeholder=" "
                                                readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-2 row align-items-center" id="discount_offer_div"
                                        style="">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">Offered
                                            Discount(₹)</label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                name="discount_offer" id="discount_offer"
                                                value=""
                                                placeholder=" " readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mb-2 row align-items-center" id="actaulRdPointsDiv"
                                        style="{{ $data['reward_redeem'] == 0 ? 'display: none !important;' : '' }}">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">Reward
                                            Points
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm rounded-1"
                                                name="reward_points_final" id="reward_points_final"
                                                value='' readonly placeholder="">
                                        </div>
                                    </div>
                                    <div class="col-md-12 row mb-2 align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Gross Total
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm pay_in"
                                                name="gross_total" id="payable_amount"
                                                value=""
                                                placeholder=" " readonly>
                                        </div>
                                    </div>
                                    {{-- <div class="col-md-12 row mb-2 align-items-center">
                                        <label for="exampleInputEmail1" class="col-5 col-md-4">
                                            Paid Amount
                                        </label>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm"
                                                value="{{ $data['diagnosticPayment']->sum('amount') }}"
                                                placeholder=" " readonly>
                                        </div>
                                    </div> --}}
                                    {{-- <div class="col-md-12  mb-0 row  payment right-six">
                                        <div class="payment_label col-5 col-md-4">
                                            <label for="exampleInputEmail1">
                                                Note
                                            </label>
                                        </div>
                                        <div class="col-7 col-md-5">
                                            <input type="text" class="form-control form-control-sm pay_in"
                                                name="remarks" id=""
                                                value="{{ $data['diagnosticPayment']->value('remarks') }}"
                                                placeholder=" ">
                                        </div>
                                    </div> --}}
                                </div>
                            </div>
                            <div class="col-md-6  mt-4 mt-md-0  mb-3 mb-md-0">
                                <div class="row">
                                    <div class="col-md-12 mb-3 d-flex align-items-start">
                                        <div class="copon-wrap row row-cols-md-3 row-cols-2 w-100 justify-content-end">
                                            @if (count($list['offer_list']) > 0)
                                            <input type="hidden" name="offered_type" id="offered_type"
                                                    value="{{ $list['offer_list'][0]['offered_type'] }}">
                                                <div class="campaign-coupon-card  col" style="padding:3px;">
                                                    <div class="form-check p-0">
                                                        <input class="form-check-input btn-check" type="radio"
                                                            name="offered_id" id="offered_id"
                                                            value="{{ $list['offer_list'][0]['offered_id'] }}"
                                                            checked>
                                                        <label for="offered_id"
                                                            class="form-check-label btn p-0 campaign-coupon-card-inner border border-1 border-gray rounded-1 w-100 pe-none">
                                                            <div class="">
                                                                <h3 class="text-gray fw-bold text-center text-uppercase h8 mt-1"
                                                                    style="min-height: 26px;">
                                                                    {{ $list['offer_list'][0]['title'] }}
                                                                </h3>
                                                                <p class="text-center px-1 mb-1 h8"
                                                                    style="min-height: 26px; line-height: 13px;">
                                                                    {{ $list['offer_list'][0]['description'] }}
                                                                </p>
                                                                <div class="offer text-center bg-gray text-center p-0">
                                                                    <h2 class="text-white h5 fw-bold ">
                                                                        {{ $list['offer_list'][0]['rate'] }}&nbsp;{{ $list['offer_list'][0]['rate_type'] == 0 ? '%' : 'Rs' }}
                                                                    </h2>
                                                                </div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <div class="row">
                        <div class="modal-footer">
                            <button type="submit" name="submit" class="btn btn-primary text-white">Submit</button>
                        </div>
                        <div class="form-group col-md-12">
                            <div id="errorMessage" class="" style="color: red;"></div>
                        </div>
                    </div>
                </div>

                <!------------Top card ends here----------->
            </div>
        </div>
</form>
@if (!empty($list['source_list']))
    <script>
        var source_list = {!! json_encode($list['source_list']) !!};
        console.log(source_list);
        // $('#search_patient').val('{{ $list['patient_phone'] }}');
        $("#source_id").val(source_list.source_id);
        $("#source_type").val(source_list.source_type);
        $("#source_test_id").val(source_list.source_test_id);
        // $("#clinic_id").val(source_list.source_clinic_id);
        // console.log(source_list.source_test_id);
        $.each(source_list.source_test_id, function(key, val) {
            getTestList(val);
        });
    </script>
@endif
