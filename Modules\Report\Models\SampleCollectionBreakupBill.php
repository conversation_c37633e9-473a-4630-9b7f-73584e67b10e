<?php

namespace Modules\Report\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SampleCollectionBreakupBill extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'id',
        'sample_collection_id',
        'item_id',
        'home_collection_id',
        'amount',
        'discount',
        'net_amount',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $dates = ['deleted_at'];
    public function sampleCollection(): BelongsTo
    {
        return $this->belongsTo(SampleCollection::class, 'sample_collection_id', 'id');
    }
    public function sampleCollectionbreakup(): BelongsTo
    {
        return $this->sampleCollection()->with('patient:id,name,phone,sex,birthdate', 'clinic:id,clinic_name', 'userDoctors:id,username as doctor_name', 'membership:id,name', 'campaign:id,campaign_name');
    }
    public function test(): BelongsTo
    {
        return $this->belongsTo(Test::class, 'item_id', 'id');
    }
    public function paymentBill(): HasOne
    {
        return $this->hasOne(PaymentBillMaster::class, 'service_id', 'id')->with('payments', 'payments.paymentDetails')->where('type', 'DG');
    }
    public function clinic()
    {
        return $this->hasOneThrough(Clinic::class, SampleCollection::class, 'id', 'id', 'sample_collection_id', 'clinic_id');
    }
}
