<form class="clearfix" method="post"
    action="<?php echo e($vital_id ? config('appointment.url') . 'updateVital/' . $vital_id : config('appointment.url') . 'addVital'); ?>"
    data-mode="<?php echo e($vital_id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" class="form-control" name="patient_id" id="patient_id" value="<?php echo e($list['patient_id']); ?>">
    <input type="hidden" class="form-control" name="appointment_id" id="appointment_id"
        value="<?php echo e($list['appointment_id']); ?>">
    <h6 class="mb-3 fw-bold">Vitals</h6>
    <div class="row">
        <div class="form-group col-md-3">
            <label for="exampleInputEmail1"> Blood Pressure:(mmHg) <b class="text-primary">*</b></label>
            <div class="input-group gap-3">
                <input type="text" class="form-control form-control-sm" id="blood_pressureUp" name="blood_pressureUp"
                    value="<?php echo e($vital_id ? explode('/', $data['blood_pressure'])[0] : ''); ?>">
                <input type="text" class="form-control form-control-sm" id="blood_pressureDwn"
                    name="blood_pressureDwn" value="<?php echo e($vital_id ? explode('/', $data['blood_pressure'])[1] : ''); ?>">
            </div>
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">Heart Rate:(bpm) <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" name="heart_rate" id="heart_rate"
                value="<?php echo e($vital_id ? $data['heart_rate'] : ''); ?>">
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">Temperature:(F) <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" name="temperature" id="temperature"
                value="<?php echo e($vital_id ? $data['temperature'] : ''); ?>">
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">SPO2:(%,Room air) <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" name="spo2" id="spo2"
                value="<?php echo e($vital_id ? $data['spo2'] : ''); ?>">
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">Height:(cm) <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" onkeyup="bmiCalculate()" name="height"
                id="height" value="<?php echo e($vital_id ? $data['height'] : ''); ?>">
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">Weight:(Kg) <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" name="weight" onkeyup="bmiCalculate()"
                id="weight" value="<?php echo e($vital_id ? $data['weight'] : ''); ?>">
        </div>

        <div class="form-group col-md-3">
            <label for="exampleInputEmail1">BMI:(kg/m2)</label>
            <input type="text" class="form-control form-control-sm" name="bmi" id="bmi" readonly=""
                value="<?php echo e($vital_id ? $data['bmi'] : ''); ?>">
        </div>
        <?php if($list['speciality'] == 'PAEDIATRIC'): ?>
            <div class="form-group col-md-3">
                <label for="exampleInputEmail1">Head Circumference (cm)</label>
                <input type="text" class="form-control form-control-sm" name="head_circumference"
                    id="head_circumference" value="<?php echo e($vital_id ? $data['head_circumference'] : ''); ?>">
            </div>
        <?php endif; ?>
    </div>
    <div class="row">
        <h6 class="mb-3 mt-3 fw-bold">Chief Complaints</h6>
        <div class="form-group col-md-12">
            <!-- <label for="exampleInputEmail1 h6 fw-bold mb-3">Chief Complaints</label> -->
            <textarea type="text" class="form-control form-control-sm" rows="4" name="chief_complaints"
                id="chief_complaints"><?php echo e($vital_id ? $list['chief_complaints'] : ''); ?></textarea>
        </div>
        <div class="form-group col-md-12">
            <button type="submit" name="submit"
                class="btn btn-primary text-white"><?php echo e($vital_id ? 'Update' : 'Submit'); ?></button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/vitalForm.blade.php ENDPATH**/ ?>