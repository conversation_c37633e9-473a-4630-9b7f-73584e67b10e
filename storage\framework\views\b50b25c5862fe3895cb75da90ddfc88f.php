<form class="clearfix" method="post"
    action="<?php echo e($id ? config('notification.templateSms_url') . 'update/' . $id : config('notification.templateSms_url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <div class="row">
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Category</label>
            <select id="sms_category_id" name="sms_category_id" class="selectpicker select2-multpl-custom1 form-select"
                data-style="py-0">
                <option value="">Select Category</option>
                <?php $__currentLoopData = $list['categorys']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row['category_id']); ?>"
                        <?php echo e($id ? ($data['sms_category_id'] == $row['category_id'] ? 'selected' : '') : ''); ?>>
                        <?php echo e($row['category_name']); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Type</label>
            <select id="sms_template_type" name="sms_template_type" class="selectpicker select2-multpl-custom1 form-select"
                data-style="py-0">
                <option value="">Select Type</option>
                <?php $__currentLoopData = $list['types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($key); ?>"
                        <?php echo e($id ? ($data['sms_template_type'] == $key ? 'selected' : '') : ''); ?>><?php echo e($row); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Name</label>
            <input type="text" class="form-control form-control-sm" name="sms_name"
                value="<?php echo e($id ? $data['sms_name'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Flow/Template Id</label>
            <input type="text" class="form-control form-control-sm" name="sms_flow_id"
                value="<?php echo e($id ? $data['sms_flow_id'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">DLT ID</label>
            <input type="text" class="form-control form-control-sm" name="sms_dlt_id"
                value="<?php echo e($id ? $data['sms_dlt_id'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Sender</label>
            <input type="text" class="form-control form-control-sm" name="sms_sender"
                value="<?php echo e($id ? $data['sms_sender'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-12">
            <label for="exampleInputEmail1" class="form-label fw-bold">Content</label>
            <textarea class="form-control form-control-sm" name="sms_content" rows="5"><?php echo e($id ? $data['sms_content'] : ''); ?></textarea>
        </div>
        <div class="form-group col-md-12">
            <button type="submit" name="submit"
                class="btn btn-primary text-white"><?php echo e($id ? 'Update' : 'Submit'); ?></button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Notification\resources/views/templateSms/api/addEdit.blade.php ENDPATH**/ ?>