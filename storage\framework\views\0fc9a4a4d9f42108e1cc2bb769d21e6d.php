<div class="row">

    <!-- <div class="row"> -->
    <div class="col-12">
        <div class="card ">
            <div class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                <div class="header-title">
            <h5 class="h5 mb-0">Select Clinic</h5>
            </div>
            </div>
            <div class="card-body">
            <div class="row row-cols-2 row-cols-md-auto row-cols-lg-auto   mt-1 gap-md-1 px-md-3">
                <?php $__currentLoopData = $data['clinic']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              
                    <!-- Adjusted column size to col-4 for 3 equal-width buttons -->
                    <div class="form-check col-md p-0 border border-light border-0">
                        <input type="radio" class="btn-check" name="clinic_id" onclick="getclinic(this)" id="clinic<?php echo e($row['id']); ?>"
                            value="<?php echo e($row['id']); ?>" autocomplete="off">
                        <label class="btn py-2 px-3 w-100  rounded shadow-sm btn-outline-primary"
                            for="clinic<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></label>
                    </div>
                
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            </div>
        </div>
    </div>
    <!-- </div> -->

    <div class="col-md-12">
        <div class="card p-4 pb-5 pt-0 mb-3">
            <h5 class="mt-4 mb-4 h5">Select Task</h5>
            <div id="taskDiv"
                class="d-flex justify-content-center justify-content-md-start align-items-center rounded flex-md-nowrap flex-wrap gap-2">
                <button type="button" onclick="taskSource('diagnostic',this)" id="diagnosticbutton"
                    class="btn btn-outline-gray py-3 taskClass">
                    <svg height=40 id=fi_2804895 viewBox="0 0 64 64" width=40 xmlns=http://www.w3.org/2000/svg>
                        <g data-name=06-clipboard id=_06-clipboard>
                            <path
                                d="m63 12.138a13.454 13.454 0 0 1 -4.824 10.407l-15.176 12.455-15.176-12.455a13.454 13.454 0 0 1 -4.824-10.407 11.1 11.1 0 0 1 20-6.662 11.1 11.1 0 0 1 20 6.662z"
                                fill=#e96565></path>
                            <path
                                d="m53.741 1.167c.***********.259.309.061-.082.132-.153.2-.233-.154-.031-.307-.05-.459-.076z"
                                fill=#d34c4c></path>
                            <path
                                d="m38.824 18.545a13.454 13.454 0 0 1 -4.824-10.407 11.1 11.1 0 0 1 2.389-6.886 11.043 11.043 0 0 0 -13.389 10.886 13.454 13.454 0 0 0 4.824 10.407l15.176 12.455 7.937-6.514z"
                                fill=#d34c4c></path>
                            <path d="m26 19h11v44h-36v-44h11z" fill=#298279></path>
                            <path d="m26 22h8v38h-30v-38h8z" fill=#eaeaea></path>
                            <path d="m7 27h8v8h-8z" fill=#e9c3b0></path>
                            <path d="m7 38h8v8h-8z" fill=#e9c3b0></path>
                            <path d="m7 49h8v8h-8z" fill=#e9c3b0></path>
                            <path d="m7 27v-8h-6v44h36v-6a30 30 0 0 1 -30-30z" opacity=.2></path>
                            <path d="m9 52 2 2 6-5" fill=none></path>
                            <path d="m12 17h14v7h-14z" fill=#5fcfb1></path>
                            <path d="m18 28h14v2h-14z" fill=#606060></path>
                            <path d="m18 32h9v2h-9z" fill=#606060></path>
                            <path d="m29 32h3v2h-3z" fill=#606060></path>
                            <path
                                d="m11 33a1 1 0 0 1 -.707-.293l-2-2 1.414-1.414 1.355 1.354 5.3-4.415 1.28 1.536-6 5a1 1 0 0 1 -.642.232z"
                                fill=#2f5ca9></path>
                            <path
                                d="m11 44a1 1 0 0 1 -.707-.293l-2-2 1.414-1.414 1.355 1.354 5.3-4.415 1.28 1.536-6 5a1 1 0 0 1 -.642.232z"
                                fill=#2f5ca9></path>
                            <path
                                d="m11 55a1 1 0 0 1 -.707-.293l-2-2 1.414-1.414 1.355 1.354 5.3-4.415 1.28 1.536-6 5a1 1 0 0 1 -.642.232z"
                                fill=#2f5ca9></path>
                            <g fill=#606060>
                                <path d="m18 39h5v2h-5z"></path>
                                <path d="m25 39h7v2h-7z"></path>
                                <path d="m18 43h8v2h-8z"></path>
                                <path d="m28 43h4v2h-4z"></path>
                                <path d="m18 54h14v2h-14z"></path>
                                <path d="m18 50h10v2h-10z"></path>
                                <path d="m30 50h2v2h-2z"></path>
                            </g>
                            <path
                                d="m43 25h-.055a1 1 0 0 1 -.923-.79l-2.402-11.21-1.72 3.447a1 1 0 0 1 -.9.553h-4v-2h3.382l2.718-5.447a1 1 0 0 1 1.873.238l2.237 10.417 1.841-5.524a1 1 0 0 1 1.878-.056l1.171 2.933 1-2.008a1 1 0 0 1 .9-.553h3v2h-2.382l-1.718 3.447a1.031 1.031 0 0 1 -.937.552 1 1 0 0 1 -.887-.627l-.986-2.466-2.136 6.41a1 1 0 0 1 -.954.684z"
                                fill=#fff></path>
                        </g>
                    </svg>
                    <span class="d-block mt-2 fw-bold">Diagnostic</span>
                </button>
                <button type="button" onclick="taskSource('phamacy',this)" id="phamacybutton"
                    class="btn btn-outline-gray py-3 taskClass">
                    <svg height=40 id=fi_1686939 viewBox="0 -30 512 512" width=40 xmlns=http://www.w3.org/2000/svg>
                        <path
                            d="m180.605469 234.582031h-113.878907c-2.335937 0-4.230468 1.894531-4.230468 4.230469v213.039062h122.339844v-213.039062c0-2.335938-1.894532-4.230469-4.230469-4.230469zm0 0"
                            fill=#78c2a4></path>
                        <path
                            d="m144.835938 234.582031v179.152344c0 10.007813-8.113282 18.117187-18.117188 18.117187h-64.222656v20h122.339844v-213.039062c0-2.335938-1.894532-4.230469-4.230469-4.230469zm0 0"
                            fill=#4fab86></path>
                        <path
                            d="m87.496094 423.21875v-160c0-2.007812 1.625-3.636719 3.636718-3.636719h65.066407c2.007812 0 3.636719 1.628907 3.636719 3.636719v160c0 2.007812-1.628907 3.632812-3.636719 3.632812h-65.066407c-2.007812 0-3.636718-1.625-3.636718-3.632812zm0 0"
                            fill=#b2ebf2></path>
                        <path
                            d="m129.835938 259.582031v121.058594c0 14.476563-11.734376 26.214844-26.214844 26.214844h-16.125v16.363281c0 2.007812 1.625 3.636719 3.636718 3.636719h65.066407c2.007812 0 3.636719-1.628907 3.636719-3.636719v-160c0-2.007812-1.628907-3.632812-3.636719-3.632812h-26.363281zm0 0"
                            fill=#80deea></path>
                        <path d="m7.5 416.488281h54.996094v35.367188h-54.996094zm0 0" fill=#78909c></path>
                        <path d="m7.5 431.855469h54.996094v20h-54.996094zm0 0" fill=#546e7a></path>
                        <path d="m184.835938 416.488281h319.664062v35.367188h-319.664062zm0 0" fill=#78909c></path>
                        <path
                            d="m464.5 416.488281v4.78125c0 5.84375-4.738281 10.582031-10.585938 10.582031h-269.078124v20h319.664062v-35.363281zm0 0"
                            fill=#546e7a></path>
                        <path
                            d="m22.511719 192.792969v223.695312h39.984375v-177.675781c0-2.335938 1.894531-4.230469 4.226562-4.230469h113.882813c2.335937 0 4.230469 1.894531 4.230469 4.230469v177.675781h304.652343v-223.695312zm0 0"
                            fill=#dce6eb></path>
                        <path
                            d="m489.488281 192.792969v223.695312h-304.652343v-20h190.421874c40.996094 0 74.230469-33.234375 74.230469-74.230469v-129.464843zm-466.976562 203.695312v20h39.984375v-20zm0 0"
                            fill=#b9c3cd></path>
                        <path
                            d="m417.105469 356.277344h-159.886719c-4.269531 0-7.730469-3.460938-7.730469-7.730469v-106.234375c0-4.269531 3.460938-7.730469 7.730469-7.730469h159.886719c4.269531 0 7.730469 3.460938 7.730469 7.730469v106.234375c0 4.269531-3.460938 7.730469-7.730469 7.730469zm0 0"
                            fill=#b2ebf2></path>
                        <path
                            d="m417.105469 234.582031h-32.269531v72.214844c0 16.28125-13.199219 29.480469-29.484376 29.480469h-105.863281v12.269531c0 4.269531 3.460938 7.730469 7.730469 7.730469h159.886719c4.269531 0 7.730469-3.460938 7.730469-7.730469v-106.234375c0-4.269531-3.460938-7.730469-7.730469-7.730469zm0 0"
                            fill=#80deea></path>
                        <path d="m184.152344 311.804688v62.824218h-24.316406v-62.824218zm0 0" fill=#78909c></path>
                        <path
                            d="m355.589844 356.277344h-36.855469v-24.855469c0-10.179687 8.25-18.425781 18.425781-18.425781 10.179688 0 18.425782 8.25 18.425782 18.425781v24.855469zm0 0"
                            fill=#80deea></path>
                        <path
                            d="m367.71875 273.765625c0 8.300781-6.730469 15.027344-15.027344 15.027344h-31.054687c-8.300781 0-15.027344-6.726563-15.027344-15.027344 0-8.296875 6.726563-15.027344 15.027344-15.027344h31.054687c8.296875 0 15.027344 6.730469 15.027344 15.027344zm0 0"
                            fill=#ffc14f></path>
                        <path
                            d="m352.6875 258.738281h-.851562c.558593 1.574219.878906 3.261719.878906 5.027344 0 8.300781-6.726563 15.03125-15.027344 15.03125h-30.199219c2.070313 5.820313 7.613281 10 14.144531 10h31.054688c8.300781 0 15.027344-6.730469 15.027344-15.03125.003906-8.296875-6.726563-15.027344-15.027344-15.027344zm0 0"
                            fill=#ffa90b></path>
                        <path
                            d="m321.636719 258.738281c-8.300781 0-15.027344 6.730469-15.027344 15.027344 0 8.300781 6.726563 15.027344 15.027344 15.027344h15.523437v-30.054688zm0 0"
                            fill=#e04955></path>
                        <path
                            d="m337.160156 278.796875h-29.671875c1.917969 5.394531 6.828125 9.378906 12.75 9.925781 1.070313.101563.039063.074219 16.925781.074219v-10zm0 0"
                            fill=#c62430></path>
                        <path
                            d="m504.5 67.464844h-497c-4.140625 0-7.5 3.359375-7.5 7.5v110.328125c0 4.144531 3.359375 7.5 7.5 7.5h497c4.144531 0 7.5-3.355469 7.5-7.5v-110.328125c0-4.140625-3.355469-7.5-7.5-7.5zm0 0"
                            fill=#78c2a4></path>
                        <path
                            d="m504.5 67.464844h-32.5v33.171875c0 39.851562-32.304688 72.15625-72.15625 72.15625h-399.84375v12.5c0 4.144531 3.359375 7.5 7.5 7.5h497c4.140625 0 7.5-3.355469 7.5-7.5v-110.328125c0-4.140625-3.359375-7.5-7.5-7.5zm0 0"
                            fill=#4fab86></path>
                        <path
                            d="m213.800781 192.792969h-15v-125.328125h15zm-49.699219-125.328125h-15v125.328125h15zm-114.402343 125.328125h15v-125.328125h-15zm64.699219-125.328125h-15v125.328125h15zm149.101562 0h-15v125.328125h15zm149.101562 0h-15v125.328125h15zm49.699219 0h-15v125.328125h15zm-149.101562 0h-15v125.328125h15zm49.699219 0h-15v125.328125h15zm0 0"
                            fill=#4fab86></path>
                        <path
                            d="m213.800781 172.792969v20h-15v-20zm-64.699219 0v20h15v-20zm-49.703124 0v20h15v-20zm-49.699219 0v20h15v-20zm198.800781 0v20h15v-20zm151.34375 0h-2.242188v20h15v-21.144531c-4.144531.738281-8.402343 1.144531-12.757812 1.144531zm-101.644531 0v20h15v-20zm149.101562-17.824219v37.824219h15v-56.035157c-3.980469 6.867188-9.058593 13.015626-15 18.210938zm-99.402343 17.824219v20h15v-20zm0 0"
                            fill=#00897b></path>
                        <path
                            d="m330.964844 74.964844c0 41.402344-33.5625 74.964844-74.964844 74.964844s-74.964844-33.5625-74.964844-74.964844 33.5625-74.964844 74.964844-74.964844 74.964844 33.5625 74.964844 74.964844zm0 0"
                            fill=#e04955></path>
                        <path
                            d="m267.964844.960938c14.167968 13.640624 23 32.785156 23 54.003906 0 41.402344-33.5625 74.964844-74.964844 74.964844-4.074219 0-8.066406-.335938-11.964844-.960938 13.476563 12.96875 31.78125 20.960938 51.964844 20.960938 41.402344 0 74.964844-33.5625 74.964844-74.964844 0-37.328125-27.289063-68.273438-63-74.003906zm0 0"
                            fill=#c62430></path>
                        <path
                            d="m292.769531 60.929688h-17.785156c-2.734375 0-4.949219-2.214844-4.949219-4.945313v-17.789063c0-2.730468-2.214844-4.949218-4.945312-4.949218h-18.175782c-2.734374 0-4.949218 2.21875-4.949218 4.949218v17.789063c0 2.730469-2.214844 4.945313-4.945313 4.945313h-17.789062c-2.730469 0-4.945313 2.214843-4.945313 4.949218v18.175782c0 2.730468 2.214844 4.945312 4.945313 4.945312h17.789062c2.730469 0 4.945313 2.214844 4.945313 4.945312v17.789063c0 2.734375 2.214844 4.949219 4.949218 4.949219h18.175782c2.730468 0 4.945312-2.214844 4.945312-4.949219v-17.789063c0-2.730468 2.214844-4.945312 4.949219-4.945312h17.785156c2.734375 0 4.949219-2.214844 4.949219-4.945312v-18.175782c0-2.734375-2.214844-4.949218-4.949219-4.949218zm0 0"
                            fill=#dce6eb></path>
                        <g fill=#b9c3cd>
                            <path
                                d="m297.71875 84.054688v-18.175782c0-2.734375-2.214844-4.949218-4.949219-4.949218h-2.0625c-.792969 10.046874-3.558593 19.53125-7.921875 28.070312h9.984375c2.734375 0 4.949219-2.214844 4.949219-4.945312zm0 0">
                            </path>
                            <path
                                d="m265.089844 116.683594c2.730468 0 4.945312-2.214844 4.945312-4.949219v-4.839844c-3.492187 3.632813-7.339844 6.917969-11.496094 9.789063zm0 0">
                            </path>
                        </g>
                    </svg>
                    <span class="d-block mt-2 fw-bold">Pharmacy</span>
                </button>
            </div>
            <div id="diagnosticSourceDiv" style="display: none;" class="mt-4">
                <h6 class="border-bottom pb-2 mb-3 fw-bold">Source</h6>
                <div class="d-flex justify-content-start align-items-center rounded flex-md-nowrap flex-wrap gap-2"
                    id="diagnosticsource">
                    <button type="button" onclick="sourcePage(1)" class="btn btn-primary">Campaign</button>
                    <button type="button" onclick="sourcePage(2)" class="btn btn-primary">Door to Door</button>
                    <button type="button" onclick="sourcePage(3)" class="btn btn-primary">Hotspot</button>
                </div>
            </div>
            <div id="pharmacySourceDiv" style="display: none;" class="mt-4">
                <h6 class="border-bottom pb-2 mb-3 fw-bold">Source</h6>
                <div class="d-flex justify-content-start align-items-center rounded flex-md-nowrap flex-wrap gap-2"
                    id="pharmacysource">
                    <button type="button" onclick="sourcePage(2)" class="btn  btn-primary">Door
                        to Door</button>
                    <button type="button" onclick="sourcePage(3)" class="btn  btn-primary">Hotspot</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let clinic_id = 0;
    let task_id = '';
    let source_id = 0;

    function getclinic(params) {
        clinic_id = $(params).val();
    }

    function taskSource(task, params) {
        $('.taskClass').removeClass('active');
        if (clinic_id == 0) {
            swal({
                title: "",
                text: "Please select a clinic",
                type: "warning",
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Ok',
            });
            return false;
        }
        task_id = task;
        $(params).addClass('active');
        $('#diagnosticSourceDiv').hide();
        $('#pharmacySourceDiv').hide();
        if (task_id == 'diagnostic') {
            $('#diagnosticSourceDiv').show();
        } else {
            $('#pharmacySourceDiv').show();
        }
    }

    function sourcePage(source) {
        source_id = source;
        if (task_id == 'diagnostic') {
            window.location.href = '<?php echo e(route('billing.index')); ?>?clinic_id=' + clinic_id + '&task_id=' + task_id + '&source_id=' + source_id;
        }
        else{
            window.location.href = '<?php echo e(route('pharmacy.order.index')); ?>?clinic_id=' + clinic_id + '&task_id=' + task_id + '&source_id=' + source_id;
        }
    }
</script><?php /**PATH C:\wamp64\www\mymd-care\Modules/Authorization\resources/views/admin/api/agentDashboard.blade.php ENDPATH**/ ?>