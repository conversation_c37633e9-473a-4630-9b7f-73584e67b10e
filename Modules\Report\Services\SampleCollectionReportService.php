<?php

namespace Modules\Report\Services;

use App\Services\ApplicationDefaultService;
use Modules\Report\Models\SampleCollectionBreakupTest;
use Modules\Report\Models\Clinic;
use DB;
use Carbon\Carbon;

class SampleCollectionReportService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;

    public $columns = [
        'sample_collection_breakup_tests.id',
        'sample_collection_breakup_tests.sample_collection_id',
        'sample_collection_breakup_tests.unique_id',
        'sample_collection_breakup_tests.package_id',
        'sample_collection_breakup_tests.test_id',
        'sample_collection_breakup_tests.itdose_testid',
        'sample_collection_breakup_tests.test_code',
        'sample_collection_breakup_tests.sample_type',
        'sample_collection_breakup_tests.sin_no',
        'sample_collection_breakup_tests.vial_qty',
        'sample_collection_breakup_tests.transferred_to',
        'sample_collection_breakup_tests.is_urgent',
        'sample_collection_breakup_tests.fieldboy',
        'sample_collection_breakup_tests.courier_details',
        'sample_collection_breakup_tests.docketno',
        'sample_collection_breakup_tests.batchno',
        'sample_collection_breakup_tests.sin_created_by',
        'sample_collection_breakup_tests.sin_created_on',
        'sample_collection_breakup_tests.is_centrifuge',
        'sample_collection_breakup_tests.segregation_created_by',
        'sample_collection_breakup_tests.segregation_created_on',
        'sample_collection_breakup_tests.transfer_by',
        'sample_collection_breakup_tests.transfer_on',
        'sample_collection_breakup_tests.itdose_test_status',
        'sample_collection_breakup_tests.sample_reject_status',
        // 'sample_collection_breakup_tests.test_delete_status',
        'sample_collection_breakup_tests.report_delivery_date',
        'sample_collection_breakup_tests.reportGenerated',
        'sample_collection_breakup_tests.status',
        'sample_collection_breakup_tests.created_by',
        'sample_collection_breakup_tests.modified_by',
        'sample_collection_breakup_tests.deleted_by',
        'sample_collection_breakup_tests.created_at',
        'sample_collection_breakup_tests.updated_at',
        'sample_collection_breakup_tests.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'sample_collection_id',
        'unique_id',
        'package_id',
        'test_id',
        'itdose_testid',
        'test_code',
        'sample_type',
        'sin_no',
        'vial_qty',
        'transferred_to',
        'is_urgent',
        'fieldboy',
        'courier_details',
        'docketno',
        'batchno',
        'sin_created_by',
        'sin_created_on',
        'is_centrifuge',
        'segregation_created_by',
        'segregation_created_on',
        'transfer_by',
        'transfer_on',
        'itdose_test_status',
        'sample_reject_status',
        // 'test_delete_status',
        'report_delivery_date',
        'reportGenerated',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(SampleCollectionBreakupTest $entity,Clinic $entityClinic) {
        $this->entity =$entity;
     $this->entityClinic =$entityClinic;
    }
    public function allSampleCollectionBreakupTests(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }

        public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function breakupTestForPdf($batchno){
        $this->entity = $this->entity->select('unique_id','sample_collection_id','test_id','test_code','sample_type','sin_no','transferred_to','fieldboy','courier_details','docketno','batchno')
            ->where('batchno',$batchno)
            ->where('status',5)
            ->with('test:id,test_name')
            ->with('sampleCollection:id,patient_id')
            ->get();
        return $this->entity;
    }
    public function updateBreakupTest($id,$request){
        $breakupTest = SampleCollectionBreakupTest::where('sample_collection_id',$id)->update($request->all());
        return $breakupTest;
    }
}
