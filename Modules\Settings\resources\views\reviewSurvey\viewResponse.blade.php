@extends('admin.layouts.app')
@section('title')
    {{ config('settings.title_review_survey', 'Responses For Doctor Consultation feedback') }}
@endsection
@section('meta')
@endsection
@section('style')
    <style>
        .tooltip1 {
            display: block;
            position: relative;
        }

        .tooltip1 .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #626262;
            color: #fff;
            border-radius: 6px;
            padding: 0px;
            position: absolute;
            z-index: 100;
            top: 10%;
            right: 27px;
            font-size: 11px;
            padding-right: 0;
            text-align: start;
        }

        .tooltip1 .tooltiptext::after {
            content: "";
            position: absolute;
            top: 15px;
            right: -10px;
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent transparent #343434;
        }

        .tooltip1:hover .tooltiptext {
            visibility: visible;
        }

        .icon-wrap {
            /* width: 83px;
                                                    text-align: center; */
            width: 100%;
            text-align: center;
            display: flex;
            flex-wrap: nowrap;
            flex-direction: row !important;
            gap: 5px;
            align-items: center !important;
        }

        @media only screen and (max-width:767px) {

            .icon-wrap {
                width: auto;
            }

        }
    </style>
@endsection
@section('content')
    <div class="col-sm-12 preloading">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">Review and Campaign Responses For {{ $reviewSurvey->survey_title }}</h4>
                </div>
                <div>
                    <a href="{{ route('reviewSurvey.index') }}">
                        <button type="button"
                            class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Back</button>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="row">
                            <div class="col">
                                <label class="mb-1 d-flex gap-2 align-items-center">
                                    <span>Show</span>
                                    <select id="perPageCount" class="form-select form-select-sm">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span>entries</span>
                                </label>
                            </div>
                            <div class="col">
                                <button type="button" class="btn btn-primary btn-sm" onclick="exportCSV(this)">CSV</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="row justify-content-end">
                            <div class="col-md-4">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="0">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th style="width: 10%;">
                                    ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Patient Name
                                </th>
                                <th>
                                    Service Type
                                </th>
                                <th>
                                    Service Date
                                </th>
                                <th>
                                    Contact
                                </th>
                                <th>
                                    Added On
                                </th>
                                <th>
                                    View Response
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @include('admin.custom.loading', ['td' => 7, 'action' => 1])
                        </tbody>
                        <tfoot>
                            @include('admin.custom.loadingPagination')
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('modal')
@endsection
@push('scripts')
    <script>
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "{{ config('settings.url_review_survey') . 'viewResponseList' }}";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id"
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    "survey_id": {
                        "type": "eq",
                        "value": "{{ $id }}"
                    }
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": perPage,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });
        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "{{ config('settings.url_review_survey') . 'exportResponse' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
@endpush
