<!DOCTYPE html>
<html>

<head>
    <title>Export Data</title>
    <meta charset="utf-8">
</head>

<body>
    <table border="1">
        <thead>
            <tr>
                <th>SL No</th>
                <th>Visit Date & Time</th>
                <th>WorkOrder-ID</th>
                <th>Collection Date</th>
                <th>Clinic</th>
                <th>Date</th>
                <th>Visit Type</th>
                <th>Created BY</th>
                <th>Test Name</th>
                <th>P-Name</th>
                <th>P-Age</th>
                <th>P-Gender</th>
                <th>P-Phone</th>
                <th>Doc Name</th>
                <th>Doc Type</th>
                <th>Doc Code</th>
                <th>Appointment Type</th>
                <th>Discount Type</th>
                <th>Collected BY</th>
                <th>Status</th>
                <th>Billed Amt</th>
                <th>Discount</th>
                <th>Redemption</th>
                <th>HC Charge</th>
                <th>Net Amt</th>
                <th>Paid</th>
                <th>Refund</th>
                <th>Due</th>
            </tr>
        </thead>
        <tbody>
            <?php if(!empty($data)): ?>
                <?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td><?php echo e(e($row['visit_datetime'] ?? '')); ?></td>
                        <td><?php echo e(e($row['unique_id'] ?? '')); ?></td>
                        <td><?php echo e(e($row['date_of_collection'] ?? '')); ?></td>
                        <td><?php echo e(e($row['clinic_name'] ?? '')); ?></td>
                        <td><?php echo e(e($row['created_at'] ?? '')); ?></td>
                        <td><?php echo e($visit_type[ e($row['type_of_collection'])] ?? ''); ?></td>
                        <td><?php echo e(e($row['created_by']['username'] ?? '')); ?></td>
                        <td>
                            
                        </td>
                        <td><?php echo e(e($row['patient_name'] ?? '')); ?></td>
                        <td><?php echo e(\Helper::ageCalculator( e($row['patient_birthdate'] ?? ''))); ?></td>
                        <td><?php echo e(e($row['patient_gender'] ?? '')); ?></td>
                        <td><?php echo e(e($row['patient_mobile'] ?? '')); ?></td>
                        <td><?php echo e(e($row['doctor_name'] ?? '')); ?></td>
                        <td><?php echo e($diagnostic_docotor_type[ e($row['doctortype'])] ?? ''); ?></td>
                     <td></td>

                        <td><?php echo e(e($row['appointment_type']) == 1 ? 'Walking' : 'Campaign'); ?></td>
                        <td><?php echo e(e($row['offered_name'] ?? '')); ?></td>
                        <td>
                           
                        </td>
                        <td><?php echo e($diagnostic_status_list[ e($row['status'])] ?? ''); ?></td>
                        <td><?php echo e(e($row['bill_amount_sum'] ?? '0')); ?></td>
                        <td><?php echo e(e($row['discount_sum'] ?? '0' )); ?></td>
                        <td><?php echo e(e($row['payment_bill_reward'] ?? '0' )); ?></td>
                        <td><?php echo e(e($row['home_collection_sum'] ?? '0')); ?></td>
                        <td><?php echo e(e($row['total_amount_sum'] ?? '0' )); ?></td>
                        <td><?php echo e(e($row['paid_amount_sum'] ?? '0')); ?></td>
                        <td><?php echo e(e($row['refund_amount_sum'] ?? '0')); ?></td>
                        <td><?php echo e(e($row['due_amount_sum'] ?? '0' )); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </tbody>
    </table>
</body>

</html>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Report\resources/views/diagnostic/export/diagnosticExport.blade.php ENDPATH**/ ?>