<?php $__env->startSection('title'); ?>
    <?php echo e(config('diagnostic.sc_title', 'Barcode Edit')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
    <link href="https://fonts.googleapis.com/css2?family=Caveat&display=swap" rel="stylesheet">
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
    <style>
        canvas {
            border: 1px solid #000;
        }

        .qr-close {
            position: fixed;
            z-index: 999;
            right: 1em;
            top: 1em;
            background-image: url(https://storage.googleapis.com/prod-mymd/live/image/profile_image/1734446996_close-icon.png);
        }

        /* barcode scanner css */
        #qr-reader {
            position: fixed !important;
            margin: 0 auto;
            z-index: 9999 !important;
            left: 0 !important;
            right: 0 !important;
            top: calc(50vh - 250px) !important;
        }

        /* #qr-reader:before{content: ""; background-color: rgba(0,0,0,0.5); width: 100%; height: 100vh; top: 0; left: 0; z-index: 99; position: absolute;} */

        .qr-wrapper {
            overflow: hidden;
        }

        .qr-wrapper:before {
            content: "";
            position: fixed;
            top: 0;
            z-index: 999;
            background: rgba(0, 0, 0, 0.85);
            display: block;
            height: 100vh;
            left: 0;
            width: 100%;
            overflow-y: hidden;
        }



        @media screen and (max-width: 767px) {

            #qr-reader,
            #qr-reader video {
                width: 100% !important;
            }

            #qr-reader {
                top: 14vh !important;
            }

            /* #qr-reader #qr-shaded-region{border-width: 26.5px 34px !important; } */

        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- camera design start -->
    <div id="qr-reader" style="width:500px; position:fixed; top:0; left:0;"></div>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">Barcode Edit</h4>
                </div>

                <div class="header-action">

                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm px-1">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>

                    <div class="col-md-10 ps-md-5">
                        <div class="row justify-content-end">
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <select name="date_of_collection" id="date_type"
                                    class="select2-multpl-custom1 form-select search-date-range" data-style="py-0">
                                    <option value="<?php echo e(date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d') ? 'selected' : ''); ?>>Today</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-1 days'))); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-1 days')) ? 'selected' : ''); ?>>
                                        Yesterday
                                    </option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        7
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        15
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        30
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        60
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        90
                                        Days</option>
                                </select>
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <input type="text" name="date_of_collection" id="date_range"
                                    placeholder="Please select a date range"
                                    class="form-control form-control-sm flatpickr-input search-date-range active"
                                    readonly="readonly" value="<?php echo e(request('d_range')); ?>">
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <select name="clinic_id" id="clinic_id"
                                    class="select2-multpl-custom1 form-select search-change" data-style="py-0">
                                    <option value="">Filter By Clinic</option>
                                    <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row['id']); ?>">
                                            <?php echo e($row['clinic_name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <input type="search" id="search" class="form-control form-control-sm search"
                                    placeholder="Search by Name or Phone No" data-index="2,3">

                            </div>
                            <div class="col-md  px-1 mb-1 mb-md-0" style="max-width: 90px;">
                                <a href="<?php echo e(route('diagnostic.barcodeEdit.index')); ?>"
                                    class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center">
                                    Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <!-- collection list starts here -->
                    <div class="col-md-6 ">
                        <div class="Table-custom-padding1 table-responsive">
                            <table id="data-list" class="table table-sm datatable_desc placeholder-glow"
                                data-toggle="data-table">
                                <thead>
                                    <tr>
                                        <th>
                                            ID
                                            <span class="asc-dsc float-end">
                                                <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                        <path
                                                            d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                                    </svg></button>
                                                <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        fill="currentColor" class="bi bi-caret-down-fill"
                                                        viewBox="0 0 16 16">
                                                        <path
                                                            d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                                    </svg></button>
                                            </span>
                                        </th>
                                        <th>
                                            Workorder-ID
                                            <span class="asc-dsc float-end">
                                                <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                        <path
                                                            d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                                    </svg></button>
                                                <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        fill="currentColor" class="bi bi-caret-down-fill"
                                                        viewBox="0 0 16 16">
                                                        <path
                                                            d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                                    </svg></button>
                                            </span>
                                        </th>
                                        <th>
                                            Date
                                        </th>
                                        <th>
                                            Patient Details
                                            <span class="asc-dsc float-end">
                                                <button type="button" data-index="2" data-sort="asc"
                                                    class="sort"><svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                        height="16" fill="currentColor" class="bi bi-caret-up-fill"
                                                        viewBox="0 0 16 16">
                                                        <path
                                                            d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                                    </svg></button>
                                                <button type="button" data-index="2" data-sort="desc"
                                                    class="sort"><svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                        height="16" fill="currentColor" class="bi bi-caret-down-fill"
                                                        viewBox="0 0 16 16">
                                                        <path
                                                            d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                                    </svg></button>
                                            </span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php echo $__env->make('admin.custom.loading', ['td' => 5, 'action' => 0], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </tbody>
                                <tfoot>
                                    <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <!-- collection list starts here -->
                    <div class="col-md-6  p-0 " id="data-add">

                    </div>
                </div>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts-head'); ?>
    <script>
        function searchFilter() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('diagnostic.sc_url') . 'listBarcodeEdit'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            $('#data-add').html('');
        }
        // date_type date_range clinic_id search
        $(document).on("change", "#date_type, #date_range, #clinic_id", function() {
            searchFilter();
        });
        $(document).on("keyup", "#search", function() {
            searchFilter();
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <script src="<?php echo e(asset('barcode/html5-qrcode.min.js')); ?>"></script>
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            // minDate: "today"
        });
        const today = new Date();
        const formattedDate = today.getFullYear() + '-' +
            String(today.getMonth() + 1).padStart(2, '0') + '-' +
            String(today.getDate()).padStart(2, '0');
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('diagnostic.sc_url') . 'listBarcodeEdit'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id",
                "unique_id",
                "patients.name",
                "patient_phone",
                // "clinics.clinic_name",
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    "date_of_collection": {
                        "type": "eq",
                        "value": formattedDate
                    }
                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 10,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });
        $(document).ready(function() {
            $(document).on("submit", "#submitForm", function() {
                let validation = validationCheck();
                if (validation != '') {
                    let msg = validation;
                    swal("", msg, "error");
                    return false;
                }
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function validationCheck() {
            let msg = '';
            $(".test_id").each(function() {
                var id = $(this).val();
                if ($(this).is(':checked')) {
                    let sample_barcode = $('#sample_barcode_' + id).val();
                    if (sample_barcode == '') {
                        msg = "Please enter barcode.";
                        return false;
                    } else if (sample_barcode.length != 8) {
                        msg = "Barcode must be 8 characters.";
                        return false;
                    }
                }
            });
            return msg;
        }        
        function findSample(id) {
            let canvas, ctx, saveButton, clearButton, resultDiv, drawing = '';
            let redirectUrl = "<?php echo e(route('diagnostic.barcodeEdit.index')); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id_form = '#data-add';
            setId(ht_id_form); // set show table id
            let url_form = "<?php echo e(config('diagnostic.sc_url') . 'formBarcodeEdit/'); ?>" + id;
            setListUrl(url_form); // api url for show table
            $(ht_id_form).show();
            let method_form = 'GET';
            setMethod(method_form);
            // let filter = {};
            // setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }

        function selectTestCollection(checkbox) {
            // console.log(checkbox,checkbox.value,checkbox.checked);
            if (checkbox.checked) {
                $('#append_div_' + checkbox.value).show();
            } else {
                $('#append_div_' + checkbox.value).hide();
            }
            testCalculation();
        }
        let cur_test_id = null;

        function checkBarcodeSample() {
            let checkSameBarcode = [];
            $(".test_id").each(function() {
                var sample_id = $(this).val();
                if ($(this).is(':checked')) {
                    var sample_type = $('#sample_type_' + sample_id).val().toLowerCase();
                    var sample_barcode = $('#sample_barcode_' + sample_id).val();
                    if (sample_barcode != '') {
                        $(".test_id").each(function() {
                            var id = $(this).val();
                            if ($(this).is(':checked') && id != sample_id) {
                                if ($('#sample_type_' + id).val().toLowerCase() != sample_type) {
                                    if ($('#sample_barcode_' + id).val() == sample_barcode) {
                                        checkSameBarcode.push(sample_barcode);
                                    }
                                }
                            }
                        });
                    }
                }
            });
            checkSameBarcode = [...new Set(checkSameBarcode)];

            return checkSameBarcode;
        }

        function testSampleTypeSame() {
            $(".test_id").each(function() {
                var sample_id = $(this).val();
                if ($(this).is(':checked')) {
                    if (!cur_test_id || cur_test_id == sample_id) {
                        var sample_type = $('#sample_type_' + sample_id).val().toLowerCase();
                        var sample_barcode = $('#sample_barcode_' + sample_id).val();
                        if (sample_barcode != '') {
                            $(".test_id").each(function() {
                                var id = $(this).val();
                                if ($(this).is(':checked') && id != sample_id) {
                                    if ($('#sample_type_' + id).val().toLowerCase() == sample_type) {
                                        $('#sample_barcode_' + id).val(sample_barcode);
                                    }
                                }
                            });
                        }
                    }
                }
            });
            cur_test_id = null;
            return true;
        }


        function setTestId(id) {
            cur_test_id = id;
            // validationCheck(cur_test_id);
            return true;
        }

        function testCalculation() {
            const sample_test = [];
            let checkSameBarcode = checkBarcodeSample();
            if (checkSameBarcode.length > 0) {
                let msg = "Barcode " + checkSameBarcode.join(',') + " Already exists.";
                swal("", msg, "error");
                return false;
            }

            // console.log('dcsdciua');
            testSampleTypeSame();
            $(".test_id").each(function() {
                var id = $(this).val();
                if ($(this).is(':checked')) {
                    var sample_type = $('#sample_type_' + id).val();
                    var sample_barcode = $('#sample_barcode_' + id).val();
                    var vialqty = $('#vialqty_' + id).val();

                    if (sample_type != '' && sample_barcode != '' && vialqty != '') {
                        var data = {
                            "test_id": id,
                            "sample_type": sample_type,
                            "sample_barcode": sample_barcode,
                            "vialqty": vialqty
                        };
                        sample_test.push(data);
                    }
                }
            });
            // console.log(sample_test, JSON.stringify(sample_test));
            $('#checked_sample_test').val(JSON.stringify(sample_test));
        }
        // qr scan
        var cur_id = 0;
        // qrCode();
        function onScanSuccess(decodedText, decodedResult) {
            // Handle the scanned QR code
            console.log(`QR Code detected: ${decodedText}`);
            document.getElementById('sample_barcode_' + cur_id).value = decodedText;
            // var sampletype = $('#sample_barcode_' + cur_id).val();
            console.log(decodedText, cur_id);
            // validationCheck(cur_id);
            testCalculation();
            // Stop the scanner after a successful scan
            closeQr();
        }

        function onScanFailure(error) {
            // Handle scan failure (optional)
            // console.warn(document.getElementById('qr-shaded-region').innerHTML);
        }

        function closeQr() {
            qrCodeScanner.stop().then(() => {
                $('body').removeClass('qr-wrapper');
                $('#close-Qr').remove();
                console.log("QR Code scanner stopped.");
            }).catch(err => {
                console.error("Error stopping QR Code scanner.", err);
            });
        }
        const qrCodeScanner = new Html5Qrcode("qr-reader");

        function qrCode(id) {
            cur_id = id;
            $('body').addClass('qr-wrapper');
            $('body').append(
                '<button id="close-Qr" type="button" class="btn-close qr-close" aria-label="Close" onclick="closeQr()"></button>'
            );
            Html5Qrcode.getCameras().then(cameras => {
                if (cameras && cameras.length) {
                    // Try to find the back camera
                    let backCamera = cameras.find(camera =>
                        camera.label.toLowerCase().includes("back") ||
                        camera.label.toLowerCase().includes("rear")
                    );

                    // Default to the first camera if no back camera is found
                    const cameraId = backCamera ? backCamera.id : cameras[0].id;

                    // Start the scanner with the selected camera
                    qrCodeScanner.start(
                        cameraId, {
                            fps: 10, // Scans per second
                            qrbox: 250 // QR code scanning box size
                        },
                        onScanSuccess,
                        onScanFailure
                    ).catch(err => {
                        console.error("Unable to start QR Code scanner.", err);
                    });
                } else {
                    console.error("No cameras found.");
                }
            }).catch(err => {
                console.error("Error fetching cameras.", err);
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/barcodeEdit/index.blade.php ENDPATH**/ ?>